export interface UserProfile {
  id: string;
  username: string;
  email: string;
  role: 'buyer' | 'coder';
  avatar?: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  bio?: string;
  location?: string;
  website?: string;
  socialLinks?: {
    github?: string;
    linkedin?: string;
    twitter?: string;
    telegram?: string;
  };
  createdAt: string;
  updatedAt: string;
  isVerified: boolean;
  rating: number;
  totalReviews: number;
}

export interface CoderProfile extends UserProfile {
  role: 'coder';
  skills: string[];
  experience: 'junior' | 'mid' | 'senior';
  hourlyRate?: {
    min: number;
    max: number;
    currency: 'IRR' | 'USD';
  };
  availability: 'available' | 'busy' | 'unavailable';
  languages: string[];
  education?: Education[];
  certifications?: Certification[];
  portfolio: PortfolioItem[];
  completedProjects: number;
  totalEarnings: number;
  responseTime: number; // in hours
  successRate: number; // percentage
}

export interface BuyerProfile extends UserProfile {
  role: 'buyer';
  company?: string;
  industry?: string;
  totalSpent: number;
  projectsPosted: number;
  hireRate: number; // percentage
}

export interface Education {
  id: string;
  institution: string;
  degree: string;
  field: string;
  startDate: string;
  endDate?: string;
  description?: string;
}

export interface Certification {
  id: string;
  name: string;
  issuer: string;
  issueDate: string;
  expiryDate?: string;
  credentialId?: string;
  credentialUrl?: string;
}

export interface PortfolioItem {
  id: string;
  title: string;
  description: string;
  category: string;
  skills: string[];
  images: string[];
  liveUrl?: string;
  sourceUrl?: string;
  completedAt: string;
  client?: string;
  testimonial?: {
    text: string;
    rating: number;
    clientName: string;
  };
}

export interface Review {
  id: string;
  projectId: string;
  reviewerId: string;
  revieweeId: string;
  rating: number;
  comment: string;
  createdAt: string;
  projectTitle: string;
  reviewer: {
    id: string;
    username: string;
    avatar?: string;
  };
}

export interface UpdateProfileData {
  firstName?: string;
  lastName?: string;
  bio?: string;
  location?: string;
  website?: string;
  phone?: string;
  socialLinks?: {
    github?: string;
    linkedin?: string;
    twitter?: string;
    telegram?: string;
  };
}

export interface UpdateCoderProfileData extends UpdateProfileData {
  skills?: string[];
  experience?: 'junior' | 'mid' | 'senior';
  hourlyRate?: {
    min: number;
    max: number;
    currency: 'IRR' | 'USD';
  };
  availability?: 'available' | 'busy' | 'unavailable';
  languages?: string[];
}

export interface UpdateBuyerProfileData extends UpdateProfileData {
  company?: string;
  industry?: string;
}

export const EXPERIENCE_LEVELS = [
  { value: 'junior', label: 'مبتدی (کمتر از ۲ سال)' },
  { value: 'mid', label: 'متوسط (۲-۵ سال)' },
  { value: 'senior', label: 'ارشد (بیش از ۵ سال)' }
] as const;

export const AVAILABILITY_STATUS = [
  { value: 'available', label: 'آماده همکاری', color: 'bg-green-500' },
  { value: 'busy', label: 'مشغول', color: 'bg-yellow-500' },
  { value: 'unavailable', label: 'غیرفعال', color: 'bg-red-500' }
] as const;

export const LANGUAGES = [
  'فارسی',
  'انگلیسی',
  'عربی',
  'ترکی',
  'آلمانی',
  'فرانسوی',
  'اسپانیایی',
  'روسی',
  'چینی',
  'ژاپنی'
] as const;

export const INDUSTRIES = [
  'فناوری اطلاعات',
  'تجارت الکترونیک',
  'بانکداری و مالی',
  'بهداشت و درمان',
  'آموزش',
  'رسانه و سرگرمی',
  'املاک',
  'حمل و نقل',
  'گردشگری',
  'کشاورزی',
  'صنعت',
  'خدمات',
  'سایر'
] as const;

export type ExperienceLevel = typeof EXPERIENCE_LEVELS[number]['value'];
export type AvailabilityStatus = typeof AVAILABILITY_STATUS[number]['value'];
export type Language = typeof LANGUAGES[number];
export type Industry = typeof INDUSTRIES[number];
