use actix_web::{HttpResponse, ResponseError};
use thiserror::Error;
use diesel_async::pooled_connection::bb8;
use crate::common::logger::LoggerService;

#[derive(Debug, Error)]
pub enum AppError {
    #[error("Database error: {0}")]
    Database(#[from] diesel::result::Error),

    #[error("Connection pool error: {0}")]
    ConnectionPool(#[from] bb8::RunError),    #[error("Redis error: {0}")]
    Redis(#[from] redis::RedisError),

    #[error("JWT error: {0}")]
    Jwt(#[from] jsonwebtoken::errors::Error),

    #[error("Validation error: {0}")]
    Validation(String),

    #[error("Authentication error: {0}")]
    Authentication(String),

    #[error("Authorization error: {0}")]
    #[allow(dead_code)]
    Authorization(String),

    #[error("Not found: {0}")]
    NotFound(String),

    #[error("Conflict: {0}")]
    Conflict(String),

    #[error("Bad request: {0}")]
    BadRequest(String),

    #[error("Internal server error: {0}")]
    InternalServer(String),
}

impl ResponseError for AppError {
    fn error_response(&self) -> HttpResponse {
        let response = match self {
            AppError::Validation(msg) => {
                LoggerService::log_error(&format!("Validation error: {msg}"), Some("AppError"));
                HttpResponse::BadRequest().json(serde_json::json!({
                    "error": "validation_error",
                    "message": msg
                }))
            },
            AppError::Authentication(msg) => {
                LoggerService::log_error(&format!("Authentication error: {msg}"), Some("AppError"));
                HttpResponse::Unauthorized().json(serde_json::json!({
                    "error": "authentication_error",
                    "message": msg
                }))
            },
            AppError::Authorization(msg) => {
                LoggerService::log_error(&format!("Authorization error: {msg}"), Some("AppError"));
                HttpResponse::Forbidden().json(serde_json::json!({
                    "error": "authorization_error",
                    "message": msg
                }))
            },
            AppError::NotFound(msg) => {
                LoggerService::log_error(&format!("Not found: {msg}"), Some("AppError"));
                HttpResponse::NotFound().json(serde_json::json!({
                    "error": "not_found",
                    "message": msg
                }))
            },
            AppError::Conflict(msg) => {
                LoggerService::log_error(&format!("Conflict: {msg}"), Some("AppError"));
                HttpResponse::Conflict().json(serde_json::json!({
                    "error": "conflict",
                    "message": msg
                }))
            },
            AppError::BadRequest(msg) => {
                LoggerService::log_error(&format!("Bad request: {msg}"), Some("AppError"));
                HttpResponse::BadRequest().json(serde_json::json!({
                    "error": "bad_request",
                    "message": msg
                }))
            },
            AppError::Database(err) => {
                LoggerService::log_error(&format!("Database error: {err}"), Some("AppError"));
                HttpResponse::InternalServerError().json(serde_json::json!({
                    "error": "internal_server_error",
                    "message": "An unexpected error occurred"
                }))
            },
            AppError::ConnectionPool(err) => {
                LoggerService::log_error(&format!("Connection pool error: {err}"), Some("AppError"));
                HttpResponse::InternalServerError().json(serde_json::json!({
                    "error": "internal_server_error",
                    "message": "An unexpected error occurred"
                }))
            },
            AppError::Redis(err) => {
                LoggerService::log_error(&format!("Redis error: {err}"), Some("AppError"));
                HttpResponse::InternalServerError().json(serde_json::json!({
                    "error": "internal_server_error",
                    "message": "An unexpected error occurred"
                }))
            },
            AppError::Jwt(err) => {
                LoggerService::log_error(&format!("JWT error: {err}"), Some("AppError"));
                HttpResponse::Unauthorized().json(serde_json::json!({
                    "error": "authentication_error",
                    "message": "Invalid or expired token"
                }))
            },
            AppError::InternalServer(msg) => {
                LoggerService::log_error(&format!("Internal server error: {msg}"), Some("AppError"));
                HttpResponse::InternalServerError().json(serde_json::json!({
                    "error": "internal_server_error",
                    "message": "An unexpected error occurred"
                }))
            },
        };

        response
    }
}
