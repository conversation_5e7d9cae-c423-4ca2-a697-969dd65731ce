use actix_web::web::Bytes;
use anyhow::{Result, anyhow};

#[allow(dead_code)]
pub struct FileValidator;

#[allow(dead_code)]
impl FileValidator {
    const MAX_FILE_SIZE: usize = 10 * 1024 * 1024; // 10MB
    const ALLOWED_TYPES: &'static [&'static str] = &[
        "image/jpeg",
        "image/png",
        "image/gif",
        "image/webp",
        "application/pdf",
        "text/plain",
        "application/json",
    ];

    pub fn validate_file(file_data: &Bytes, content_type: &str) -> Result<()> {
        Self::validate_size(file_data)?;
        Self::validate_type(content_type)?;
        Ok(())
    }

    fn validate_size(file_data: &Bytes) -> Result<()> {
        if file_data.len() > Self::MAX_FILE_SIZE {
            return Err(anyhow!("File size exceeds maximum allowed size"));
        }
        Ok(())
    }

    fn validate_type(content_type: &str) -> Result<()> {
        if !Self::ALLOWED_TYPES.contains(&content_type) {
            return Err(anyhow!("File type not allowed"));
        }
        Ok(())
    }
}
