'use client';

import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/core/utils';
import { Construction } from 'lucide-react';

interface EmptyDashboardSectionProps {
  title?: string;
  description?: string;
  className?: string;
}

export function EmptyDashboardSection({
  title = 'در حال توسعه',
  description = 'این بخش در حال توسعه است و به‌زودی در دسترس خواهد بود.',
  className,
}: EmptyDashboardSectionProps) {
  return (
    <Card className={cn('border-dashed', className)}>
      <CardContent className="flex flex-col items-center justify-center p-12 text-center">
        <Construction className="h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium mb-2">{title}</h3>
        <p className="text-muted-foreground max-w-md">{description}</p>
      </CardContent>
    </Card>
  );
}
