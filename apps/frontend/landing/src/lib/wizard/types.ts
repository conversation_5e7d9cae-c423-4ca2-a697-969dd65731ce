export interface WizardSubmissionRequest {
  full_name: string;
  email: string;
  phone: string;
  project_type: 'webapp' | 'mobile' | 'startup';
  description: string;
}

export interface WizardSubmissionResponse {
  id: string;
  full_name: string;
  email: string;
  phone: string;
  project_type: string;
  description: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
  };
}
