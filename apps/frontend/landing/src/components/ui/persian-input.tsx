'use client';

import * as React from 'react';
import { cn } from '@/lib/core/utils';
import { getTextDirection, toEnglishNumbers } from '@/lib/utils/persian';

export interface PersianInputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  convertNumbers?: boolean;
  persianOnly?: boolean;
  autoDirection?: boolean;
}

const PersianInput = React.forwardRef<HTMLInputElement, PersianInputProps>(
  ({ className, type, convertNumbers = false, persianOnly = false, autoDirection = true, onChange, ...props }, ref) => {
    const [direction, setDirection] = React.useState<'rtl' | 'ltr'>('rtl');

    const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      let value = event.target.value;

      // Auto-detect direction
      if (autoDirection) {
        const newDirection = getTextDirection(value) || 'rtl';
        setDirection(newDirection);
      }

      // Convert Persian numbers to English if needed
      if (convertNumbers && type === 'number') {
        value = toEnglishNumbers(value);
        event.target.value = value;
      }

      // Filter non-Persian characters if persianOnly is true
      if (persianOnly) {
        const persianRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s]/g;
        value = value.match(persianRegex)?.join('') || '';
        event.target.value = value;
      }

      onChange?.(event);
    };

    return (
      <input
        type={type}
        className={cn(
          'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
          autoDirection && direction === 'rtl' && 'text-right',
          autoDirection && direction === 'ltr' && 'text-left',
          className
        )}
        dir={autoDirection ? direction : 'rtl'}
        onChange={handleChange}
        ref={ref}
        {...props}
      />
    );
  }
);
PersianInput.displayName = 'PersianInput';

export { PersianInput };
