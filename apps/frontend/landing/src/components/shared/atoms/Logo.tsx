'use client';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React from 'react';
interface LogoProps {
  className?: string;
  asLink?: boolean;
}

const Logo: React.FC<LogoProps> = ({ className, asLink = true }) => {
  const router = useRouter();
  return (
    <Image
      src={require('../../../../public/dolfak.webp')}
      alt="logo"
      width={500}
      height={500}
      className={`w-[clamp(5rem,7vw,12rem)] h-auto object-contain transition-all duration-300 hover:scale-105 ${className || ''}`}
      priority
      onClick={asLink ? () => router.push('/') : undefined}
    />
  );
};

export default Logo;
