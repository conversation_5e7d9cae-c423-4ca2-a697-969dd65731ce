const ACCESS_TOKEN_KEY = 'access_token';
const REFRESH_TOKEN_KEY = 'refresh_token';
const USER_KEY = 'user';

export class TokenManager {
  private static getCookieOptions(): string {
    const isProduction = process.env.NODE_ENV === 'production';
    const maxAge = 7 * 24 * 60 * 60 * 1000;
    const expires = new Date(Date.now() + maxAge).toUTCString();

    if (isProduction) {
      return `; path=/; expires=${expires}; SameSite=None; Secure; Domain=.dolfak.com`;
    } else {
      return `; path=/; expires=${expires}; SameSite=Lax; Domain=localhost`;
    }
  }

  private static getClearCookieOptions(): string {
    const isProduction = process.env.NODE_ENV === 'production';

    if (isProduction) {
      return '; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=None; Secure; Domain=.dolfak.com';
    } else {
      return '; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax; Domain=localhost';
    }
  }

  static setTokens(accessToken: string, refreshToken: string): void {
    if (typeof document === 'undefined') return;

    const options = this.getCookieOptions();
    document.cookie = `${ACCESS_TOKEN_KEY}=${accessToken}${options}`;
    document.cookie = `${REFRESH_TOKEN_KEY}=${refreshToken}${options}`;
  }

  static getAccessToken(): string | null {
    if (typeof document === 'undefined') return null;
    const matches = document.cookie.match(
      new RegExp('(?:^|; )' + ACCESS_TOKEN_KEY + '=([^;]*)'),
    );
    return matches ? decodeURIComponent(matches[1]) : null;
  }

  static getRefreshToken(): string | null {
    if (typeof document === 'undefined') return null;
    const matches = document.cookie.match(
      new RegExp('(?:^|; )' + REFRESH_TOKEN_KEY + '=([^;]*)'),
    );
    return matches ? decodeURIComponent(matches[1]) : null;
  }

  static setUser(user: any): void {
    if (typeof document === 'undefined') return;
    const options = this.getCookieOptions();
    document.cookie = `${USER_KEY}=${encodeURIComponent(JSON.stringify(user))}${options}`;
  }

  static getUser(): any | null {
    if (typeof document === 'undefined') return null;
    const matches = document.cookie.match(
      new RegExp('(?:^|; )' + USER_KEY + '=([^;]*)'),
    );
    if (!matches) return null;
    try {
      return JSON.parse(decodeURIComponent(matches[1]));
    } catch {
      return null;
    }
  }

  static clearAll(): void {
    if (typeof document === 'undefined') return;

    const options = this.getClearCookieOptions();
    document.cookie = `${ACCESS_TOKEN_KEY}=${options}`;
    document.cookie = `${REFRESH_TOKEN_KEY}=${options}`;
    document.cookie = `${USER_KEY}=${options}`;
  }

  static hasValidTokens(): boolean {
    const accessToken = this.getAccessToken();
    const refreshToken = this.getRefreshToken();
    return !!(accessToken || refreshToken);
  }

  static getTokenType(): string {
    return 'Bearer';
  }

  static isTokenExpired(): boolean {
    const token = this.getAccessToken();
    if (!token) return true;

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const now = Date.now() / 1000;
      return payload.exp < now;
    } catch {
      return true;
    }
  }

  static checkAuthenticationStatus(): boolean {
    if (typeof document === 'undefined') return false;

    const accessToken = this.getAccessToken();
    const refreshToken = this.getRefreshToken();

    return !!(accessToken || refreshToken);
  }

  static setUserData = this.setUser;
  static getUserData = this.getUser;
  static clearTokens = this.clearAll;
  static hasValidToken = this.hasValidTokens;

  static isRemembered(): boolean {
    return false;
  }
}
