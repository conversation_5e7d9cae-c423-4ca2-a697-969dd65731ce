'use client';
import { useCallback, useMemo, useState } from 'react';

export type SectionMode = 'default' | 'wizard' | 'loading' | string;

export interface UseSectionModeReturn {
  mode: SectionMode;
  setMode: (mode: SectionMode) => void;
  isMode: (mode: SectionMode) => boolean;
  resetMode: () => void;
  toggleMode: (mode: SectionMode) => void;
}

export const useSectionMode = (
  initialMode: SectionMode = 'default',
): UseSectionModeReturn => {
  const stableInitialMode = useMemo(() => initialMode, [initialMode]);
  const [mode, setModeState] = useState<SectionMode>(stableInitialMode);

  const setMode = useCallback((newMode: SectionMode) => {
    setModeState(newMode);
  }, []);

  const isMode = useCallback(
    (checkMode: SectionMode) => {
      return mode === checkMode;
    },
    [mode],
  );

  const resetMode = useCallback(() => {
    setModeState(stableInitialMode);
  }, [stableInitialMode]);

  const toggleMode = useCallback((toggleableMode: SectionMode) => {
    setModeState((current) =>
      current === toggleableMode ? 'default' : toggleableMode,
    );
  }, []);

  return {
    mode,
    setMode,
    isMode,
    resetMode,
    toggleMode,
  };
};
