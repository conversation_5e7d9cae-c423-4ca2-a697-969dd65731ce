import { Card, CardContent, CardHeader } from '@/components/ui/card';
import React from 'react';

interface ServiceCardProps {
  icon: string;
  title: string;
  description: string;
  className?: string;
}

const ServiceCard: React.FC<ServiceCardProps> = ({
  icon,
  title,
  description,
  className,
}) => {
  return (
    <Card
      className={`group p-[clamp(0.75rem,1.5vw,1rem)] text-center transition-all duration-300 hover:shadow-xl hover:-translate-y-1 border border-primary/20 hover:border-primary/40 bg-card/80 backdrop-blur-sm hover:scale-105 ${className || ''}`}
    >
      <div className="text-[clamp(1.5rem,3vw,2rem)] mb-3 bg-gradient-to-br from-primary to-accent text-background rounded-lg w-[clamp(2.5rem,6vw,3rem)] h-[clamp(2.5rem,6vw,3rem)] flex items-center justify-center mx-auto transform transition-transform group-hover:scale-110 font-mono relative overflow-hidden shadow-lg">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-accent/10 animate-pulse"></div>
        <span className="relative z-10">{icon}</span>
      </div>
      <CardHeader className="pb-1">
        <h3 className="text-[clamp(0.875rem,1.5vw,1rem)] font-semibold mb-2 text-primary font-mono">
          {'{'} {title} {'}'}
        </h3>
      </CardHeader>
      <CardContent>
        <p className=" text-[clamp(0.75rem,1vw,0.875rem)] leading-relaxed">
          {description}
        </p>
      </CardContent>
    </Card>
  );
};

export default ServiceCard;
