import { useAtom } from 'jotai';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import { createWizardStore } from './wizard-store';
import { WizardConfig, WizardStepData } from './wizard-types';
import {
  canNavigateToStep,
  checkStepDependencies,
  createStepContext,
  debounce,
  getVisibleSteps,
  normalizeValidationResult,
  validateAllSteps,
} from './wizard-utils';

export const useWizardStore = (wizardId: string) => {
  return useMemo(() => createWizardStore(wizardId), [wizardId]);
};

export const useWizardNavigation = (
  config: WizardConfig,
  store: ReturnType<typeof createWizardStore>,
) => {
  const [wizardState] = useAtom(store.wizardAtom);
  const [, setCurrentStep] = useAtom(store.setCurrentStepAtom);
  const [, setLoading] = useAtom(store.setLoadingAtom);
  const [, setValidationErrors] = useAtom(store.setValidationErrorsAtom);
  const [, markStepComplete] = useAtom(store.markStepCompleteAtom);
  const [, setWizardComplete] = useAtom(store.setWizardCompleteAtom);
  const [, resetWizard] = useAtom(store.resetWizardAtom);

  const stableSteps = useMemo(() => config.steps, [config.steps]);
  const stableWizardId = useMemo(() => config.wizardId, [config.wizardId]);
  const stableStepData = useMemo(
    () => wizardState?.stepData || {},
    [wizardState?.stepData],
  );

  const visibleSteps = useMemo(
    () => getVisibleSteps(stableSteps, stableStepData, stableWizardId),
    [stableSteps, stableStepData, stableWizardId],
  );

  const currentVisibleStepIndex = Math.max(
    0,
    visibleSteps.findIndex(
      ({ originalIndex }) => originalIndex === (wizardState?.currentStep || 0),
    ),
  );
  const currentStep = visibleSteps[currentVisibleStepIndex] || null;

  const goToStep = useCallback(
    async (targetStepIndex: number) => {
      if (!wizardState) return false;

      if (
        !canNavigateToStep(
          targetStepIndex,
          wizardState.currentStep,
          wizardState.completedSteps,
          config.allowStepSkipping || false,
          config.steps,
        )
      ) {
        return false;
      }

      if (config.onBeforeStepChange) {
        const canChange = await config.onBeforeStepChange(
          wizardState.currentStep,
          targetStepIndex,
          wizardState.stepData[wizardState.currentStep] || {},
        );
        if (!canChange) return false;
      }

      setCurrentStep(targetStepIndex);
      config.onStepChange?.(
        targetStepIndex,
        wizardState.stepData[targetStepIndex] || {},
        targetStepIndex > wizardState.currentStep ? 'next' : 'previous',
      );

      return true;
    },
    [wizardState, config, setCurrentStep],
  );

  const next = useCallback(async () => {
    if (!currentStep || !wizardState || wizardState.isLoading) return;

    setLoading(true);
    const currentStepConfig = currentStep.step;
    const currentStepData =
      wizardState.stepData[currentStep.originalIndex] || {};

    try {
      if (currentStepConfig.validate) {
        const context = createStepContext(
          currentStep.originalIndex,
          config.steps.length,
          config.wizardId,
          wizardState.stepData || {},
          wizardState.validationErrors || {},
        );

        const validationResult = await currentStepConfig.validate(
          currentStepData,
          context,
        );
        const errors = normalizeValidationResult(validationResult);

        setValidationErrors(currentStep.originalIndex, errors);

        if (errors.length > 0) {
          config.onValidationError?.(currentStep.originalIndex, errors);
          return;
        }
      }

      if (currentStepConfig.onExit) {
        const context = createStepContext(
          currentStep.originalIndex,
          config.steps.length,
          config.wizardId,
          wizardState.stepData || {},
          wizardState.validationErrors || {},
        );
        await currentStepConfig.onExit(context);
      }

      if (currentVisibleStepIndex === visibleSteps.length - 1) {
        markStepComplete(currentStep.originalIndex);

        if (config.onComplete) {
          await config.onComplete(wizardState.stepData, {
            wizardId: config.wizardId,
          });
        }

        setWizardComplete(true);

        if (config.resetOnComplete) {
          resetWizard();
        }
      } else {
        const nextStep = visibleSteps[currentVisibleStepIndex + 1];
        if (nextStep) {
          markStepComplete(currentStep.originalIndex);
          await goToStep(nextStep.originalIndex);
        }
      }
    } finally {
      setLoading(false);
    }
  }, [
    currentStep,
    currentVisibleStepIndex,
    visibleSteps,
    wizardState,
    config,
    setLoading,
    setValidationErrors,
    markStepComplete,
    setWizardComplete,
    goToStep,
    resetWizard,
  ]);

  const previous = useCallback(async () => {
    if (
      currentVisibleStepIndex <= 0 ||
      !currentStep ||
      visibleSteps.length === 0
    )
      return;

    const prevStepIndex =
      visibleSteps[currentVisibleStepIndex - 1]?.originalIndex;

    if (typeof prevStepIndex === 'number') {
      await goToStep(prevStepIndex);
    }
  }, [currentVisibleStepIndex, visibleSteps, goToStep, currentStep]);

  const reset = useCallback(() => {
    resetWizard();
  }, [resetWizard]);

  return {
    visibleSteps,
    currentStep,
    currentVisibleStepIndex,
    next,
    previous,
    goToStep,
    reset,
    canGoNext:
      currentStep && wizardState
        ? checkStepDependencies(
            currentStep.step,
            wizardState.stepData || {},
            config.steps,
          ) &&
          !(
            wizardState.validationErrors?.[currentStep.originalIndex]?.length >
            0
          )
        : false,
    canGoPrevious: currentVisibleStepIndex > 0,
  };
};

export const useWizardStep = <T extends WizardStepData>(
  stepIndex: number,
  config: WizardConfig,
  store: ReturnType<typeof createWizardStore>,
) => {
  const [wizardState] = useAtom(store.wizardAtom);
  const [, setStepData] = useAtom(store.setStepDataAtom);
  const [, setValidationErrors] = useAtom(store.setValidationErrorsAtom);

  const stepConfig = config.steps[stepIndex];
  const stepData = (wizardState?.stepData?.[stepIndex] || {}) as T;
  const validationErrors = wizardState?.validationErrors?.[stepIndex] || [];

  const context = useMemo(
    () =>
      createStepContext<T>(
        stepIndex,
        config.steps.length,
        config.wizardId,
        wizardState?.stepData || {},
        wizardState?.validationErrors || {},
      ),
    [
      stepIndex,
      config.steps.length,
      config.wizardId,
      wizardState?.stepData,
      wizardState?.validationErrors,
    ],
  );

  const updateData = useCallback(
    (data: Partial<T> | ((prev: T) => T)) => {
      setStepData(stepIndex, data);
    },
    [stepIndex, setStepData],
  );

  const stableStepConfig = useMemo(() => stepConfig, [stepConfig]);
  const stableStepsLength = useMemo(
    () => config.steps.length,
    [config.steps.length],
  );
  const stableConfigWizardId = useMemo(
    () => config.wizardId,
    [config.wizardId],
  );

  const debouncedValidate = useMemo(
    () =>
      debounce(async (data: T) => {
        if (stableStepConfig?.validate) {
          try {
            const validationContext = createStepContext<T>(
              stepIndex,
              stableStepsLength,
              stableConfigWizardId,
              wizardState?.stepData || {},
              wizardState?.validationErrors || {},
            );
            const result = await stableStepConfig.validate(
              data,
              validationContext,
            );
            const errors = normalizeValidationResult(result);
            setValidationErrors(stepIndex, errors);
          } catch (error) {
            setValidationErrors(stepIndex, [
              error instanceof Error ? error.message : 'Validation error',
            ]);
          }
        }
      }, 300),
    [
      stableStepConfig,
      stepIndex,
      stableStepsLength,
      stableConfigWizardId,
      setValidationErrors,
    ],
  );

  useEffect(() => {
    // Only run validation if step data has meaningful content (not just empty/undefined values)
    if (stepData && Object.keys(stepData).length > 0) {
      const hasActualContent = Object.values(stepData).some(
        (value) => value !== undefined && value !== null && value !== '',
      );

      if (hasActualContent) {
        debouncedValidate(stepData);
      }
    }

    return () => {
      // Cleanup debounced validation on unmount
      debouncedValidate.cancel();
    };
  }, [stepData, debouncedValidate]);

  const stableOnMount = useMemo(
    () => stableStepConfig?.onMount,
    [stableStepConfig?.onMount],
  );
  const stableOnUnmount = useMemo(
    () => stableStepConfig?.onUnmount,
    [stableStepConfig?.onUnmount],
  );
  const stableOnEnter = useMemo(
    () => stableStepConfig?.onEnter,
    [stableStepConfig?.onEnter],
  );

  useEffect(() => {
    if (wizardState?.currentStep === stepIndex && stableOnMount) {
      stableOnMount(context);
    }

    return () => {
      if (wizardState?.currentStep === stepIndex && stableOnUnmount) {
        stableOnUnmount(context);
      }
    };
  }, [
    wizardState?.currentStep,
    stepIndex,
    stableOnMount,
    stableOnUnmount,
    context,
  ]);

  useEffect(() => {
    if (wizardState?.currentStep === stepIndex && stableOnEnter) {
      stableOnEnter(context);
    }
  }, [wizardState?.currentStep, stepIndex, stableOnEnter, context]);

  return {
    stepData,
    updateData,
    validationErrors,
    context,
    isActive: wizardState?.currentStep === stepIndex,
    isCompleted: wizardState?.completedSteps?.has(stepIndex) || false,
    isTouched: wizardState?.touchedSteps?.has(stepIndex) || false,
  };
};

export const useWizardAutoSave = (
  config: WizardConfig,
  store: ReturnType<typeof createWizardStore>,
) => {
  const [wizardState] = useAtom(store.wizardAtom);
  const [, saveProgress] = useAtom(store.saveProgressAtom);
  const autoSaveRef = useRef<NodeJS.Timeout | null>(null);

  const stableAutoSave = useMemo(() => config.autoSave, [config.autoSave]);
  const stableAutoSaveInterval = useMemo(
    () => config.autoSaveInterval || 30000,
    [config.autoSaveInterval],
  );

  useEffect(() => {
    if (!stableAutoSave || !wizardState?.hasUnsavedChanges) return;

    autoSaveRef.current = setTimeout(() => {
      saveProgress();
    }, stableAutoSaveInterval);

    return () => {
      if (autoSaveRef.current) {
        clearTimeout(autoSaveRef.current);
      }
    };
  }, [
    stableAutoSave,
    stableAutoSaveInterval,
    wizardState?.hasUnsavedChanges,
    saveProgress,
  ]);

  const manualSave = useCallback(() => {
    saveProgress();
  }, [saveProgress]);

  return {
    hasUnsavedChanges: wizardState?.hasUnsavedChanges || false,
    lastSaveTime: wizardState?.lastSaveTime,
    manualSave,
  };
};

export const useWizardValidation = (
  config: WizardConfig,
  store: ReturnType<typeof createWizardStore>,
) => {
  const [wizardState] = useAtom(store.wizardAtom);

  const stableConfigSteps = useMemo(() => config.steps, [config.steps]);
  const stableValidationWizardId = useMemo(
    () => config.wizardId,
    [config.wizardId],
  );

  const validateCurrentStep = useCallback(async () => {
    if (!wizardState) return false;

    const currentStepConfig = stableConfigSteps[wizardState.currentStep];
    if (!currentStepConfig?.validate) return true;

    const context = createStepContext(
      wizardState.currentStep,
      stableConfigSteps.length,
      stableValidationWizardId,
      wizardState.stepData || {},
      wizardState.validationErrors || {},
    );

    try {
      const result = await currentStepConfig.validate(
        wizardState.stepData[wizardState.currentStep] || {},
        context,
      );
      return normalizeValidationResult(result).length === 0;
    } catch {
      return false;
    }
  }, [stableConfigSteps, stableValidationWizardId, wizardState]);

  const validateAllStepsAsync = useCallback(async (): Promise<boolean> => {
    if (!wizardState?.stepData) return false;

    const errors = await validateAllSteps(
      stableConfigSteps,
      wizardState.stepData,
      stableValidationWizardId,
    );
    return Object.values(errors).every(
      (stepErrors: string[]) => stepErrors.length === 0,
    );
  }, [stableConfigSteps, wizardState?.stepData, stableValidationWizardId]);

  return {
    validateCurrentStep,
    validateAllSteps: validateAllStepsAsync,
    hasErrors: Object.values(wizardState?.validationErrors || {}).some(
      (errors) => errors.length > 0,
    ),
    currentStepErrors:
      wizardState?.validationErrors?.[wizardState?.currentStep || 0] || [],
  };
};
