import React from 'react';

interface FeatureItemProps {
  text: string;
  className?: string;
}

const FeatureItem: React.FC<FeatureItemProps> = ({ text, className }) => {
  return (
    <li
      className={`flex items-center gap-[clamp(0.375rem,0.75vw,0.5rem)] font-mono ${className || ''}`}
    >
      <span className="flex-shrink-0 w-[clamp(1rem,2vw,1.25rem)] h-[clamp(1rem,2vw,1.25rem)] rounded-lg bg-gradient-to-br from-primary to-accent flex items-center justify-center text-background text-[clamp(0.5rem,1vw,0.625rem)] shadow-md transition-transform duration-300 hover:scale-110">
        ✓
      </span>
      <span className="text-foreground text-[clamp(0.75rem,1.5vw,0.875rem)]">
        {text}
      </span>
    </li>
  );
};

export default FeatureItem;
