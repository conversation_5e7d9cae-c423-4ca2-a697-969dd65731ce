import { Components } from '@/components/registry';
import { SectionModeWrapper } from '@/components/shared/section-mode/SectionModeWrapper';

interface Props {}
const LandingPage = (props: Props) => {
  return (
    <>
      <SectionModeWrapper>
        <Components.HeroSection />
      </SectionModeWrapper>
      <Components.FeaturesSection />
      <Components.PricingSection />
      <Components.ProductsOverviewSection />
    </>
  );
};
export default LandingPage;
