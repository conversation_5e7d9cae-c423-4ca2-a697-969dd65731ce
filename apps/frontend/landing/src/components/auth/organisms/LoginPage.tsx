'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { AlertCircle } from 'lucide-react';
import { useState } from 'react';
import { GoogleOAuthWithRole } from '../forms/GoogleOAuthWithRole';
import { LoginForm } from '../forms/LoginForm';
import { SignupForm } from '../forms/SignupForm';

interface LoginPageProps {
  onSuccess?: () => void;
}

function LoginPage({ onSuccess }: LoginPageProps) {
  const [error, setError] = useState<string | null>(null);

  const handleSuccess = () => {
    setError(null);
    onSuccess?.();
  };

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
  };

  return (
    <div className="flex items-center justify-center">
      <div className="max-w-md w-full space-y-2">
        <div className="text-center">
          <h2 className="mt-2 text-xl font-extrabold text-gray-900">
            ورود به حساب کاربری
          </h2>
          <span className="text-sm text-gray-600">
            روش احراز هویت خود را انتخاب کنید
          </span>
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <Card className="p-2">
          <CardHeader>
            <CardTitle>احراز هویت</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <GoogleOAuthWithRole
              onSuccess={handleSuccess}
              onError={handleError}
            />

            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <Separator className="w-full" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">
                  یا ادامه با
                </span>
              </div>
            </div>

            <Tabs defaultValue="signup" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                {/* <TabsTrigger value="phone">شماره تلفن</TabsTrigger> */}
                <TabsTrigger value="login">ورود</TabsTrigger>
                <TabsTrigger value="signup">ثبت‌نام</TabsTrigger>
              </TabsList>

              {/* <TabsContent value="phone" className="mt-6">
                <PhoneOtpForm onSuccess={handleSuccess} onError={handleError} />
              </TabsContent> */}

              <TabsContent value="login" className="mt-6">
                <LoginForm onSuccess={handleSuccess} onError={handleError} />
              </TabsContent>

              <TabsContent value="signup" className="mt-6">
                <SignupForm onSuccess={handleSuccess} onError={handleError} />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
export default LoginPage;
