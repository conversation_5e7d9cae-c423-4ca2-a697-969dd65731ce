import { NextRequest, NextResponse } from 'next/server';

const PUBLIC_ROUTES = [
  '/',
  '/login',
  '/callback',
  '/contact',
  '/services',
  '/shop',
  '/blog',
];

const AUTH_ROUTES = ['/login', '/callback'];
const PRIVATE_ROUTES = ['/dashboard', '/buyers', '/coders'];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const accessToken = request.cookies.get('access_token')?.value;
  const isAuthenticated = !!accessToken;

  const isPublicRoute = PUBLIC_ROUTES.some((route) => {
    if (route === '/') {
      return pathname === '/';
    }
    return pathname === route || pathname.startsWith(`${route}/`);
  });

  const isAuthRoute = AUTH_ROUTES.some(
    (route) => pathname === route || pathname.startsWith(`${route}/`),
  );

  const isPrivateRoute = PRIVATE_ROUTES.some(
    (route) => pathname === route || pathname.startsWith(`${route}/`),
  );

  // Handle dashboard redirect based on user role
  if (pathname === '/dashboard' && isAuthenticated) {
    const userCookie = request.cookies.get('user');

    if (userCookie) {
      try {
        const user = JSON.parse(decodeURIComponent(userCookie.value));
        const redirectPath = user.role === 'buyer' ? '/buyers' : '/coders';
        return NextResponse.redirect(new URL(redirectPath, request.url));
      } catch {
        return NextResponse.redirect(new URL('/buyers', request.url));
      }
    }

    return NextResponse.redirect(new URL('/buyers', request.url));
  }

  // Redirect authenticated users away from auth pages
  if (isAuthenticated && isAuthRoute) {
    return NextResponse.redirect(new URL('/', request.url));
  }

  // Redirect unauthenticated users from private routes to login
  if (!isAuthenticated && isPrivateRoute) {
    const loginUrl = new URL('/login', request.url);
    loginUrl.searchParams.set('redirect', pathname);
    return NextResponse.redirect(loginUrl);
  }

  // For any route that's not explicitly public or auth, assume it's private
  if (!isAuthenticated && !isPublicRoute && !isAuthRoute) {
    const loginUrl = new URL('/login', request.url);
    loginUrl.searchParams.set('redirect', pathname);
    return NextResponse.redirect(loginUrl);
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.png|.*\\.jpg|.*\\.jpeg|.*\\.gif|.*\\.svg|.*\\.webp).*)',
  ],
};
