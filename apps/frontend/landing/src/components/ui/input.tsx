import * as React from 'react';

import { cn } from '@/lib/core/utils';

function Input({ className, type, ...props }: React.ComponentProps<'input'>) {
  return (
    <input
      type={type}
      data-slot="input"
      className={cn(
        'file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-gray-200 flex h-9 w-full min-w-0 rounded-md border bg-white px-3 py-1 text-base shadow-xs transition-all duration-300 outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
        'focus-visible:border-indigo-400 focus-visible:ring-indigo-100 focus-visible:ring-[3px] focus-visible:bg-white',
        'hover:border-indigo-300 hover:bg-white',
        'placeholder-shown:bg-white placeholder-shown:border-gray-200',
        'not-placeholder-shown:bg-white not-placeholder-shown:text-gray-800 not-placeholder-shown:border-gray-200',
        'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive',
        className,
      )}
      {...props}
    />
  );
}

export { Input };
