use redis::Client;
use crate::common::config::RedisConfig;
use super::redis_cache::RedisCache;

#[derive(Clone)]
#[allow(dead_code)]
pub struct CacheService {
    pub redis: RedisCache,
}

impl CacheService {
    pub async fn new(config: &RedisConfig) -> Self {
        let client = Client::open(config.url.as_str())
            .expect("Failed to create Redis client");

        let redis = RedisCache::new(client);

        Self { redis }
    }
}
