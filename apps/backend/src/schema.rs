// @generated automatically by Diesel CLI.

diesel::table! {
    auth_tokens (id) {
        id -> Uuid,
        user_id -> Uuid,
        token_hash -> Varchar,
        expires_at -> Timestamptz,
        created_at -> Timestamptz,
        updated_at -> Timestamptz,
    }
}

diesel::table! {
    phone_otp (id) {
        id -> Uuid,
        phone -> Varchar,
        #[max_length = 6]
        otp_code -> Varchar,
        verified -> Nullable<Bool>,
        expires_at -> Timestamptz,
        attempts -> Nullable<Int4>,
        created_at -> Nullable<Timestamptz>,
        updated_at -> Nullable<Timestamptz>,
    }
}

diesel::table! {
    users (id) {
        id -> Uuid,
        email -> Varchar,
        username -> Varchar,
        password_hash -> Varchar,
        created_at -> Timestamptz,
        updated_at -> Timestamptz,
        phone -> Nullable<Varchar>,
        phone_verified -> Nullable<Bool>,
        role -> Varchar,
    }
}

diesel::table! {
    wizard_submissions (id) {
        id -> U<PERSON>,
        full_name -> Varchar,
        email -> Varchar,
        phone -> Varchar,
        project_type -> Varchar,
        description -> Text,
        status -> Varchar,
        created_at -> Timestamptz,
        updated_at -> Timestamptz,
    }
}

diesel::joinable!(auth_tokens -> users (user_id));

diesel::allow_tables_to_appear_in_same_query!(
    auth_tokens,
    phone_otp,
    users,
    wizard_submissions,
);
