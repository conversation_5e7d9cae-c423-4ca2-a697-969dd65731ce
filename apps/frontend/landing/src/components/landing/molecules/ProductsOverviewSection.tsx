import { Button } from '@/components/ui/button';
import {
  ArrowRight,
  Globe,
  Monitor,
  ShoppingCart,
  Smartphone,
} from 'lucide-react';
import Link from 'next/link';
import React from 'react';

interface ProductsOverviewSectionProps {
  className?: string;
}

const ProductsOverviewSection: React.FC<ProductsOverviewSectionProps> = ({
  className,
}) => {
  const productCategories = [
    {
      icon: Globe,
      title: 'وب اپلیکیشن‌ها',
      count: '۱۵+',
      description: 'فروشگاه آنلاین، سایت شرکتی، سیستم مدیریت',
    },
    {
      icon: Smartphone,
      title: 'اپلیکیشن‌های موبایل',
      count: '۱۲+',
      description: 'اپلیکیشن تاکسی، سفارش غذا، فروشگاهی',
    },
    {
      icon: Monitor,
      title: 'اپلیکیشن‌های دسکتاپ',
      count: '۱۰+',
      description: 'نرم‌افزار انبارداری، حسابداری، CRM',
    },
  ];

  return (
    <section
      id="products-overview"
      className={`py-8 sm:py-12 lg:py-16 xl:py-20 bg-gradient-to-br from-background to-card relative ${className || ''}`}
    >
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-10 left-10 text-primary/10 font-mono text-xs rotate-12 float">
          {'const shop = new Store()'}
        </div>
        <div
          className="absolute top-20 right-20 text-accent/10 font-mono text-xs -rotate-12 float"
          style={{ animationDelay: '1s' }}
        >
          {'ready.toBuy = true'}
        </div>
        <div
          className="absolute bottom-20 left-1/4 text-primary/10 font-mono text-xs rotate-6 float"
          style={{ animationDelay: '3s' }}
        >
          {'<applications/>'}
        </div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-8 sm:mb-12 lg:mb-16">
          <div className="inline-block mb-3 sm:mb-4 px-3 sm:px-4 py-2 bg-card/80 text-primary rounded-lg text-xs sm:text-sm font-mono border border-primary/20 shadow-lg">
            <span dir="ltr" className="text-accent">
              browse
            </span>{' '}
            --shop
          </div>
          <h2 className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold mb-3 sm:mb-4 font-mono">
            <span className="text-primary">{'{'}</span>
            <span className="text-foreground mx-2">
              اپلیکیشن‌های آماده برای خرید
            </span>
            <span className="text-primary">{'}'}</span>
          </h2>
          <div className="max-w-2xl mx-auto text-sm sm:text-base font-mono bg-card/50 border border-primary/20 rounded-lg p-3 sm:p-4 backdrop-blur-sm">
            <div dir="ltr" className="text-accent">
              // ایده ی استارتاپی داری؟ همین اول زیاد خرج نکن
            </div>
            <div className=" mt-2">
              یه اپلیکیشن آماده بخر - مقرون به صرفه و سریع!
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 mb-6 sm:mb-8">
          {productCategories.map((category, index) => (
            <div
              key={index}
              className="group bg-card/80 border border-primary/20 rounded-lg p-4 sm:p-6 backdrop-blur-sm transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:border-primary/40 hover:bg-card/90"
            >
              <div className="text-center">
                <div className="flex justify-center mb-4">
                  <category.icon className="w-10 h-10 sm:w-12 sm:h-12 lg:w-16 lg:h-16 text-primary group-hover:scale-110 transition-transform duration-300" />
                </div>
                <h3 className="text-base sm:text-lg lg:text-xl font-bold font-mono text-primary mb-2">
                  {category.title}
                </h3>
                <div className="text-lg sm:text-xl lg:text-2xl font-bold text-accent mb-3 font-mono">
                  {category.count}
                </div>
                <p className="text-muted-foreground text-xs sm:text-sm leading-relaxed">
                  {category.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center">
          <Button
            size="lg"
            className=" shadow-lg transition-all duration-300 hover:scale-105 group neo-brutal bg-gradient-to-r from-primary to-accent hover:from-primary/80 hover:to-accent/80 text-sm sm:text-base px-6 sm:px-8 py-3 sm:py-4 rounded-lg font-mono5 group"
            asChild
          >
            <Link
              href="/shop"
              className="flex items-center justify-center gap-3 sm:gap-4"
            >
              <ArrowRight className="w-5 h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 group-hover:-translate-x-1 transition-transform order-2" />
              <span className="order-1">مشاهده همه اپلیکیشن‌ها</span>
              <ShoppingCart className="w-5 h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 group-hover:animate-bounce order-0" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
};

export default ProductsOverviewSection;
