use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct UserCreatedEvent {
    pub user_id: Uuid,
    pub email: String,
    pub username: String,
}

#[allow(dead_code)]
impl UserCreatedEvent {
    pub fn new(user_id: Uuid, email: String, username: String) -> Self {
        Self {
            user_id,
            email,
            username,
        }
    }
}
