'use client';

import { Button } from '@/components/ui/button';
import { useEffect } from 'react';

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    console.error(error);
  }, [error]);

  return (
    <html>
      <body>
        <div className="flex min-h-screen items-center justify-center px-4">
          <div className="text-center">
            <div className="space-y-6">
              <div className="space-y-2">
                <h1 className="text-9xl font-bold text-destructive">500</h1>
                <h2 className="text-3xl font-semibold">خطای سرور</h2>
                <p className="text-lg text-muted-foreground max-w-md mx-auto">
                  متأسفانه خطایی رخ داده است. لطفاً دوباره تلاش کنید.
                </p>
              </div>
              <div className="flex gap-4 justify-center">
                <Button onClick={reset}>تلاش مجدد</Button>
                <Button
                  variant="outline"
                  onClick={() => (window.location.href = '/')}
                >
                  بازگشت به خانه
                </Button>
              </div>
            </div>
          </div>
        </div>
      </body>
    </html>
  );
}
