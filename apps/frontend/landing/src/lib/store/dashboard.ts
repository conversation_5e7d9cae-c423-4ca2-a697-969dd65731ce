import { atom } from 'jotai';

export interface DashboardState {
  isLoading: boolean;
  currentSection: 'overview' | 'projects' | 'profile' | 'settings';
  sidebarCollapsed: boolean;
}

export const dashboardStateAtom = atom<DashboardState>({
  isLoading: false,
  currentSection: 'overview',
  sidebarCollapsed: false,
});

export const currentSectionAtom = atom(
  (get) => get(dashboardStateAtom).currentSection,
  (get, set, section: DashboardState['currentSection']) => {
    const currentState = get(dashboardStateAtom);
    set(dashboardStateAtom, {
      ...currentState,
      currentSection: section,
    });
  },
);

export const sidebarCollapsedAtom = atom(
  (get) => get(dashboardStateAtom).sidebarCollapsed,
  (get, set, collapsed: boolean) => {
    const currentState = get(dashboardStateAtom);
    set(dashboardStateAtom, {
      ...currentState,
      sidebarCollapsed: collapsed,
    });
  },
);

export const isLoadingAtom = atom(
  (get) => get(dashboardStateAtom).isLoading,
  (get, set, loading: boolean) => {
    const currentState = get(dashboardStateAtom);
    set(dashboardStateAtom, {
      ...currentState,
      isLoading: loading,
    });
  },
);
