use actix_web::{
    dev::{forward_ready, Service, ServiceRequest, ServiceResponse, Transform},
    Error,
    HttpMessage,
    http::header::AUTHORIZATION,
};
use futures_util::future::LocalBoxFuture;
use std::future::{ready, Ready};
use std::rc::Rc;

/// Authentication middleware for protecting routes
pub struct AuthMiddleware;

impl<S, B> Transform<S, ServiceRequest> for AuthMiddleware
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Transform = AuthMiddlewareService<S>;
    type InitError = ();
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ready(Ok(AuthMiddlewareService {
            service: Rc::new(service),
        }))
    }
}

pub struct AuthMiddlewareService<S> {
    service: Rc<S>,
}

impl<S, B> Service<ServiceRequest> for AuthMiddlewareService<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Future = LocalBoxFuture<'static, Result<Self::Response, Self::Error>>;

    forward_ready!(service);

    fn call(&self, req: ServiceRequest) -> Self::Future {
        let service = Rc::clone(&self.service);

        Box::pin(async move {
            // Skip auth for health check and public routes
            let path = req.path();
            if path.starts_with("/health") ||
               path.starts_with("/api/auth/login") ||
               path.starts_with("/api/auth/register") ||
               path.starts_with("/api/auth/google") ||
               path.starts_with("/swagger") ||
               path.starts_with("/api-docs") {
                return service.call(req).await;
            }

            // Extract and validate JWT token
            if let Some(auth_header) = req.headers().get(AUTHORIZATION) {
                if let Ok(auth_str) = auth_header.to_str() {
                    if auth_str.starts_with("Bearer ") {
                        let token = auth_str.trim_start_matches("Bearer ");

                        // TODO: Validate JWT token here
                        // For now, we'll just pass the token to the request extensions
                        req.extensions_mut().insert(token.to_string());

                        return service.call(req).await;
                    }
                }
            }

            // Check for token in cookies as fallback
            if let Some(cookie_header) = req.headers().get(actix_web::http::header::COOKIE) {
                if let Ok(cookie_str) = cookie_header.to_str() {
                    for cookie in cookie_str.split(';') {
                        let cookie = cookie.trim();
                        if cookie.starts_with("access_token=") {
                            let token = cookie.trim_start_matches("access_token=");
                            req.extensions_mut().insert(token.to_string());
                            return service.call(req).await;
                        }
                    }
                }
            }

            // No valid token found
            Err(actix_web::error::ErrorUnauthorized("Unauthorized: Missing or invalid token"))
        })
    }
}
