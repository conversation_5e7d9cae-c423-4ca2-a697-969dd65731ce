'use client';

import { Components } from '@/components/registry';
import { Button } from '@/components/ui/button';
import React, { useState } from 'react';

interface ProductsSectionProps {
  className?: string;
}

const ProductsSection: React.FC<ProductsSectionProps> = ({ className }) => {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  const categories = [
    { id: null, label: 'همه', icon: '📁' },
    { id: 'web', label: 'وب اپلیکیشن', icon: '🌐' },
    { id: 'mobile', label: 'اپلیکیشن موبایل', icon: '📱' },
    { id: 'desktop', label: 'اپلیکیشن دسکتاپ', icon: '💻' },
  ];

  const products = [
    {
      imageAlt: 'تصویر داشبورد فروشگاهی وب',
      title: 'سیستم فروشگاه آنلاین',
      description:
        'وب اپلیکیشن کامل فروشگاه اینترنتی با پنل مدیریت، سیستم پرداخت و مدیریت محصولات',
      price: '۸,۵۰۰,۰۰۰ تومان',
      category: 'web',
    },
    {
      imageAlt: 'تصویر اپلیکیشن موبایل رستوران',
      title: 'اپلیکیشن سفارش غذا',
      description:
        'اپلیکیشن موبایل سفارش آنلاین غذا با قابلیت ردیابی سفارش و پرداخت آنلاین',
      price: '۶,۲۰۰,۰۰۰ تومان',
      category: 'mobile',
    },
    {
      imageAlt: 'تصویر نرم‌افزار حسابداری دسکتاپ',
      title: 'نرم‌افزار مدیریت انبار',
      description: 'اپلیکیشن دسکتاپ مدیریت انبار و فروش با گزارش‌گیری کامل',
      price: '۴,۸۰۰,۰۰۰ تومان',
      category: 'desktop',
    },
    {
      imageAlt: 'تصویر سایت شرکتی',
      title: 'قالب سایت شرکتی',
      description: 'وب سایت حرفه‌ای شرکتی با سیستم مدیریت محتوا و بلاگ',
      price: '۳,۵۰۰,۰۰۰ تومان',
      category: 'web',
    },
    {
      imageAlt: 'تصویر اپلیکیشن موبایل تاکسی',
      title: 'اپلیکیشن درخواست تاکسی',
      description: 'اپلیکیشن موبایل درخواست تاکسی با نقشه و سیستم پرداخت',
      price: '۹,۲۰۰,۰۰۰ تومان',
      category: 'mobile',
    },
    {
      imageAlt: 'تصویر نرم‌افزار CRM دسکتاپ',
      title: 'نرم‌افزار مدیریت مشتریان',
      description: 'اپلیکیشن دسکتاپ CRM برای مدیریت مشتریان و فروش',
      price: '۵,۵۰۰,۰۰۰ تومان',
      category: 'desktop',
    },
  ];

  const filteredProducts = selectedCategory
    ? products.filter((product) => product.category === selectedCategory)
    : products;

  return (
    <section
      id="products"
      className={`py-[clamp(1rem,8vw,3rem)] bg-gradient-to-br from-card via-background to-secondary relative overflow-hidden ${className || ''}`}
    >
      {/* Geeky background decorative elements */}
      <div className="absolute top-0 left-0 w-full h-[clamp(2rem,8vw,4rem)] bg-gradient-to-b from-background opacity-50"></div>
      <div className="absolute -top-24 -right-24 w-[clamp(12rem,30vw,24rem)] h-[clamp(12rem,30vw,24rem)] rounded-full bg-primary/10 mix-blend-screen filter blur-3xl opacity-30"></div>
      <div className="absolute -bottom-24 -left-24 w-[clamp(12rem,30vw,24rem)] h-[clamp(12rem,30vw,24rem)] rounded-full bg-accent/10 mix-blend-screen filter blur-3xl opacity-30"></div>

      {/* Code snippets floating around */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-10 left-10 text-primary/10 font-mono text-xs rotate-12 float">
          {'const products = []'}
        </div>
        <div
          className="absolute top-20 right-20 text-accent/10 font-mono text-xs -rotate-12 float"
          style={{ animationDelay: '1s' }}
        >
          {'npm run build'}
        </div>
        <div
          className="absolute bottom-20 left-1/3 text-primary/10 font-mono text-xs rotate-6 float"
          style={{ animationDelay: '3s' }}
        >
          {'git push origin main'}
        </div>
      </div>

      <div className="container relative z-10 mx-auto px-4">
        <div className="text-center mb-[clamp(2rem,8vw,4rem)]">
          <div
            dir="ltr"
            className="inline-block mb-4 px-4 py-2 bg-card/80 text-primary rounded-lg text-[clamp(0.75rem,1.5vw,0.875rem)] font-mono border border-primary/20 shadow-lg"
          >
            <span className="text-accent">await</span> fetchProducts()
          </div>
          <h2 className="ext-md sm:text-xl md:text-2xl lg:text-3xl font-bold mb-4 font-mono">
            <span className="text-primary">{'['}</span>
            <span className="text-foreground mx-2">فروشگاه اپلیکیشن‌ها</span>
            <span className="text-primary">{']'}</span>
          </h2>
          <div className=" max-w-2xl mx-auto text-[clamp(0.875rem,2vw,1.125rem)] font-mono bg-card/30 border border-primary/10 rounded-lg p-4">
            <div className="text-accent mb-2">// مجموعه اپلیکیشن‌های آماده</div>
            <div>
              اپلیکیشن‌های آماده ما را در دسته‌بندی‌های مختلف مشاهده و خریداری
              کنید
            </div>
          </div>
        </div>

        {/* Category Filters */}
        <div className="mb-8 flex justify-center">
          <div className="flex flex-wrap gap-3 p-4 bg-card/50 border border-primary/20 rounded-lg backdrop-blur-sm">
            {categories.map((category) => (
              <Button
                key={category.id || 'all'}
                variant={
                  selectedCategory === category.id ? 'default' : 'outline'
                }
                size="sm"
                onClick={() => setSelectedCategory(category.id)}
                className={`font-mono text-sm transition-all duration-300 group neo-brutal hover:scale-105 shadow-lg flex items-center gap-2 ${
                  selectedCategory === category.id
                    ? 'bg-gradient-to-r from-primary to-accent text-primary-foreground'
                    : 'bg-gradient-to-r from-primary/20 to-accent/20 hover:from-primary/30 hover:to-accent/30 border-primary/30 text-primary hover:border-primary/50'
                }`}
              >
                <span className="group-hover:animate-pulse">
                  {category.icon}
                </span>
                {category.label}
              </Button>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-2 2xl:grid-cols-3 3xl:grid-cols-4 gap-[clamp(1rem,3vw,2rem)]">
          {filteredProducts.map((product, index) => (
            <Components.ProductCard
              key={index}
              imageAlt={product.imageAlt}
              title={product.title}
              description={product.description}
              price={product.price}
              category={product.category}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default ProductsSection;
