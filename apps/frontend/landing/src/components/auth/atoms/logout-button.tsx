'use client';

import { Button } from '@/components/ui/button';
import { useAuth } from '@/hooks/auth/use-auth';
import { useAuthModal } from '@/hooks/auth/use-auth-modal';
import { LogOut } from 'lucide-react';
import { useState } from 'react';

interface LogoutButtonProps {
  variant?:
    | 'default'
    | 'destructive'
    | 'outline'
    | 'secondary'
    | 'ghost'
    | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
}

export function LogoutButton({
  variant = 'outline',
  size = 'default',
  className,
}: LogoutButtonProps) {
  const { logout } = useAuth();
  const { resetModal } = useAuthModal();
  const [isLoading, setIsLoading] = useState(false);

  const handleLogout = async () => {
    try {
      setIsLoading(true);
      await logout();
      resetModal(); // Reset modal state on logout
    } catch (error) {
      console.error('Logout failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleLogout}
      disabled={isLoading}
      className={className}
    >
      <LogOut className="w-4 h-4 mr-2" />
      {isLoading ? 'در حال خروج' : 'خروج'}
    </Button>
  );
}
