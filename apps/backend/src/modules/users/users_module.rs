use actix_web::web;
use crate::common::CommonModule;
use super::users_controller::UsersController;

pub struct UsersModule;

impl UsersModule {
    pub fn configure(cfg: &mut web::ServiceConfig, common_module: &CommonModule) {
        let users_controller = UsersController::new(common_module);

        cfg.service(
            web::scope("/users")
                .app_data(web::Data::new(users_controller))
                .route("", web::get().to(UsersController::get_all))
                .route("", web::post().to(UsersController::create))
                .route("/me", web::get().to(UsersController::get_me))
                .route("/profile", web::get().to(UsersController::get_profile))
                .route("/{id}", web::get().to(UsersController::get_by_id))
                .route("/{id}", web::put().to(UsersController::update))
                .route("/{id}", web::delete().to(UsersController::delete))
        );
    }
}
