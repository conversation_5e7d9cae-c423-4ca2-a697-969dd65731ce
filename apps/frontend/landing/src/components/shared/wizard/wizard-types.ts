export type WizardValidationResult = true | string | string[];

export type WizardStepData<T = Record<string, any>> = T;

export interface WizardStepContext<T = Record<string, any>> {
  stepIndex: number;
  totalSteps: number;
  wizardId: string;
  allStepData: Record<number, WizardStepData>;
  currentStepData: T;
  isFirstStep: boolean;
  isLastStep: boolean;
  isValid: boolean;
  validationErrors: string[];
}

export interface WizardStepProps<T = Record<string, any>> {
  data: T;
  onDataChange: (data: Partial<T> | ((prev: T) => T)) => void;
  onNext: () => Promise<void> | void;
  onPrevious: () => void;
  context: WizardStepContext<T>;
  canGoNext: boolean;
  canGoPrevious: boolean;
}

export interface WizardStepLifecycle<T = Record<string, any>> {
  onEnter?: (context: WizardStepContext<T>) => void | Promise<void>;
  onExit?: (context: WizardStepContext<T>) => void | Promise<void>;
  onMount?: (context: WizardStepContext<T>) => void | Promise<void>;
  onUnmount?: (context: WizardStepContext<T>) => void;
}

export interface WizardStepDependency {
  stepId: string;
  condition: (stepData: WizardStepData) => boolean;
}

export interface WizardStep<T = Record<string, any>>
  extends WizardStepLifecycle<T> {
  id: string;
  title: string;
  description?: string;
  component: React.ComponentType<WizardStepProps<T>>;
  validate?: (
    data: T,
    context: WizardStepContext<T>,
  ) => WizardValidationResult | Promise<WizardValidationResult>;
  isOptional?: boolean;
  isVisible?: (context: WizardStepContext) => boolean;
  dependencies?: WizardStepDependency[];
  icon?: React.ComponentType<{ className?: string }>;
  estimatedTime?: number;
  allowSkip?: boolean;
}

export interface WizardTheme {
  progressVariant?: 'line' | 'dots' | 'steps';
  navigationPosition?: 'bottom' | 'floating' | 'sidebar';
  animationType?: 'slide' | 'fade' | 'none';
  colorScheme?: 'auto' | 'light' | 'dark';
}

export interface WizardConfig<T = Record<string, any>> {
  wizardId: string;
  steps: WizardStep<T>[];
  onComplete?: (
    allData: Record<number, WizardStepData>,
    context: { wizardId: string },
  ) => void | Promise<void>;
  onStepChange?: (
    stepIndex: number,
    stepData: WizardStepData,
    direction: 'next' | 'previous',
  ) => void;
  onValidationError?: (stepIndex: number, errors: string[]) => void;
  onBeforeStepChange?: (
    fromStep: number,
    toStep: number,
    stepData: WizardStepData,
  ) => boolean | Promise<boolean>;
  persistProgress?: boolean;
  allowStepSkipping?: boolean;
  autoSave?: boolean;
  autoSaveInterval?: number;
  theme?: WizardTheme;
  confirmOnExit?: boolean;
  resetOnComplete?: boolean;
}

export interface WizardState {
  currentStep: number;
  stepData: Record<number, WizardStepData>;
  isComplete: boolean;
  validationErrors: Record<number, string[]>;
  completedSteps: Set<number>;
  touchedSteps: Set<number>;
  isLoading: boolean;
  hasUnsavedChanges: boolean;
  lastSaveTime?: number;
}
