/* Neon glow effect - subtle version */
.neon-glow {
  box-shadow:
    0 0 2px var(--primary),
    0 0 4px var(--primary),
    0 0 6px var(--primary);
}

.neon-text {
  text-shadow:
    0 0 2px var(--primary),
    0 0 4px var(--primary);
}

/* Circuit board pattern */
.circuit-bg {
  background-image:
    linear-gradient(90deg, var(--border) 1px, transparent 1px),
    linear-gradient(var(--border) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Progress bar enhancement - subtle */
.progress-glow {
  box-shadow: 0 0 5px var(--primary);
}

/* Neo brutalist button effect - softer shadows */
.neo-brutal {
  border: 2px solid var(--border);
  box-shadow: 2px 2px 0px var(--primary);
  transition: all 0.1s ease;
}

.neo-brutal:hover {
  transform: translate(1px, 1px);
  box-shadow: 1px 1px 0px var(--primary);
}

.neo-brutal:active {
  transform: translate(2px, 2px);
  box-shadow: 0px 0px 0px var(--primary);
}

/* Glitch text variations */
.glitch-intense {
  position: relative;
}

.glitch-intense::before,
.glitch-intense::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Cyberpunk glow effects - more subtle */
.cyber-glow {
  box-shadow:
    0 0 5px var(--primary),
    0 0 10px var(--primary),
    inset 0 0 2px var(--primary);
}

.cyber-border {
  border: 1px solid var(--primary);
  box-shadow: 0 0 3px var(--primary);
}

/* Terminal window effect */
.terminal-window {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 8px;
  position: relative;
}

.terminal-window::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 24px;
  background: var(--muted);
  border-radius: 8px 8px 0 0;
  border-bottom: 1px solid var(--border);
}

.terminal-window::after {
  content: '';
  position: absolute;
  top: 6px;
  left: 8px;
  width: 6px;
  height: 6px;
  background: var(--destructive);
  border-radius: 50%;
  box-shadow:
    12px 0 0 var(--accent),
    24px 0 0 var(--primary);
}

/* Code block enhancement */
.code-block {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: 1rem;
  font-family: var(--font-mono);
  position: relative;
  overflow: hidden;
}

/* Lighter code block variant */
.code-block-light {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: 1rem;
  font-family: var(--font-mono);
  position: relative;
  overflow: hidden;
}

/* Additional geeky utility classes */
.ascii-art {
  font-family: monospace;
  white-space: pre;
  line-height: 1;
  font-size: 0.6rem;
  color: var(--muted-foreground);
}

.matrix-background {
  position: relative;
  overflow: hidden;
}

.matrix-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(90deg, transparent 95%, var(--primary) 100%),
    linear-gradient(0deg, transparent 95%, var(--primary) 100%);
  background-size: 20px 20px;
  pointer-events: none;
}

/* Retro CRT effect */
.crt-effect {
  position: relative;
  background: var(--card);
}

.crt-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: repeating-linear-gradient(
    0deg,
    transparent,
    transparent 2px,
    var(--primary) 2px,
    var(--primary) 4px
  );
  pointer-events: none;
}
