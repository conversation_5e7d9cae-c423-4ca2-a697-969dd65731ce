import { cn } from '@/lib/core/utils';
import Link from 'next/link';
import React from 'react';

interface FooterLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
}

const FooterLink: React.FC<FooterLinkProps> = ({
  href,
  children,
  className,
}) => {
  return (
    <li>
      <Link
        href={href}
        className={cn(
          'text-[clamp(0.75rem,1vw,0.875rem)] py-1 px-2 hover:text-primary transition-colors duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/50 foreground hover:bg-primary/10 rounded border border-transparent hover:border-primary/20',
          className,
        )}
      >
        <span className="text-accent mr-1">→</span>
        {children}
      </Link>
    </li>
  );
};

export default FooterLink;
