use crate::common::{CommonModule, errors::AppError, utils::pagination::{PaginationParams, PaginatedResponse}};
use super::{users_repository::UsersRepository, entities::UserEntity, dto::{CreateUserDto, UpdateUserDto, UserResponseDto}};
use bcrypt::{hash, DEFAULT_COST};

pub struct UsersService {
    repository: UsersRepository,
}

impl UsersService {
    pub fn new(common_module: &CommonModule) -> Self {
        Self {
            repository: UsersRepository::new(common_module),
        }
    }

    pub async fn find_paginated(&self, params: &PaginationParams) -> Result<PaginatedResponse<UserResponseDto>, AppError> {
        let (users, total) = self.repository.find_paginated(params).await?;
        let user_dtos: Vec<UserResponseDto> = users.into_iter().map(|user| user.into()).collect();
        Ok(PaginatedResponse::new(user_dtos, params, total))
    }

    pub async fn find_by_id(&self, id: &str) -> Result<Option<UserResponseDto>, AppError> {
        let user = self.repository.find_by_id(id).await?;
        Ok(user.map(|u| u.into()))
    }

    pub async fn find_by_email(&self, email: &str) -> Result<Option<UserEntity>, AppError> {
        self.repository.find_by_email(email).await
    }

    pub async fn find_by_username(&self, username: &str) -> Result<Option<UserEntity>, AppError> {
        self.repository.find_by_username(username).await
    }

    pub async fn find_by_phone(&self, phone: &str) -> Result<Option<UserEntity>, AppError> {
        self.repository.find_by_phone(phone).await
    }

    pub async fn create(&self, new_user: &super::entities::NewUserEntity) -> Result<UserEntity, AppError> {
        self.repository.create(new_user).await
    }

    pub async fn create_user(&self, create_dto: &CreateUserDto) -> Result<UserResponseDto, AppError> {
        if (self.find_by_username(&create_dto.username).await?).is_some() {
            return Err(crate::common::errors::CustomErrors::username_already_exists());
        }

        let password_hash = hash(&create_dto.password, DEFAULT_COST)
            .map_err(|_| AppError::InternalServer("Failed to hash password".to_string()))?;

        let role = create_dto.role.clone().unwrap_or_default();

        let new_user = UserEntity::new_with_role(
            create_dto.email.clone(),
            create_dto.username.clone(),
            password_hash,
            role,
        );

        let created_user = self.repository.create(&new_user).await?;
        Ok(created_user.into())
    }

    pub async fn update_user(&self, id: &str, update_dto: &UpdateUserDto) -> Result<UserResponseDto, AppError> {
        if let Some(new_username) = &update_dto.username {
            if let Some(existing_user) = self.find_by_username(new_username).await? {
                if existing_user.id.to_string() != id {
                    return Err(crate::common::errors::CustomErrors::username_already_exists());
                }
            }
        }

        let user = self.repository.update(id, update_dto).await?;
        Ok(user.into())
    }

    pub async fn delete_user(&self, id: &str) -> Result<(), AppError> {
        self.repository.delete(id).await
    }
}
