'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { CoderProfile, BuyerProfile } from '@/lib/types/user';
import { formatCurrency } from '@/lib/utils/project';
import { 
  MapPin, 
  Star, 
  Clock, 
  CheckCircle, 
  Globe, 
  Github, 
  Linkedin, 
  MessageCircle,
  ExternalLink
} from 'lucide-react';

interface UserProfileCardProps {
  user: CoderProfile | BuyerProfile;
  onViewProfile?: (userId: string) => void;
  onSendMessage?: (userId: string) => void;
  onHire?: (userId: string) => void;
  variant?: 'compact' | 'detailed';
  showActions?: boolean;
}

export function UserProfileCard({
  user,
  onViewProfile,
  onSendMessage,
  onHire,
  variant = 'compact',
  showActions = true
}: UserProfileCardProps) {
  const isCoder = user.role === 'coder';
  const coderUser = user as CoderProfile;
  const buyerUser = user as BuyerProfile;

  const getAvailabilityColor = (status: string) => {
    switch (status) {
      case 'available':
        return 'bg-green-500';
      case 'busy':
        return 'bg-yellow-500';
      case 'unavailable':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getAvailabilityLabel = (status: string) => {
    switch (status) {
      case 'available':
        return 'آماده همکاری';
      case 'busy':
        return 'مشغول';
      case 'unavailable':
        return 'غیرفعال';
      default:
        return 'نامشخص';
    }
  };

  return (
    <Card className="h-full flex flex-col hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start gap-3">
          <div className="relative">
            <Avatar className="h-12 w-12">
              <AvatarImage src={user.avatar} alt={user.username} />
              <AvatarFallback className="bg-primary text-primary-foreground">
                {user.username.slice(0, 2)}
              </AvatarFallback>
            </Avatar>
            {isCoder && (
              <div 
                className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${getAvailabilityColor(coderUser.availability)}`}
                title={getAvailabilityLabel(coderUser.availability)}
              />
            )}
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="font-semibold text-lg leading-tight truncate">
                {user.username}
              </h3>
              {user.isVerified && (
                <CheckCircle className="h-4 w-4 text-blue-500" />
              )}
            </div>
            
            <div className="flex items-center gap-2 mb-2">
              <div className="flex items-center gap-1">
                <Star className="h-4 w-4 text-yellow-500 fill-current" />
                <span className="text-sm font-medium">{user.rating}</span>
                <span className="text-xs text-muted-foreground">
                  ({user.totalReviews} نظر)
                </span>
              </div>
            </div>

            {user.location && (
              <div className="flex items-center gap-1 text-sm text-muted-foreground mb-2">
                <MapPin className="h-3 w-3" />
                <span>{user.location}</span>
              </div>
            )}
          </div>
        </div>

        {user.bio && variant === 'detailed' && (
          <p className="text-sm text-muted-foreground line-clamp-3 mt-2">
            {user.bio}
          </p>
        )}
      </CardHeader>

      <CardContent className="flex-1 pb-3">
        <div className="space-y-3">
          {isCoder && (
            <>
              {/* Skills */}
              <div className="flex flex-wrap gap-1">
                {coderUser.skills.slice(0, variant === 'compact' ? 3 : 6).map((skill, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {skill}
                  </Badge>
                ))}
                {coderUser.skills.length > (variant === 'compact' ? 3 : 6) && (
                  <Badge variant="outline" className="text-xs">
                    +{coderUser.skills.length - (variant === 'compact' ? 3 : 6)}
                  </Badge>
                )}
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div>
                  <div className="text-muted-foreground">پروژه‌های تکمیل شده</div>
                  <div className="font-medium">{coderUser.completedProjects}</div>
                </div>
                <div>
                  <div className="text-muted-foreground">نرخ موفقیت</div>
                  <div className="font-medium">{coderUser.successRate}%</div>
                </div>
              </div>

              {/* Hourly Rate */}
              {coderUser.hourlyRate && (
                <div className="text-sm">
                  <div className="text-muted-foreground">نرخ ساعتی</div>
                  <div className="font-medium">
                    {formatCurrency(coderUser.hourlyRate.min)} - {formatCurrency(coderUser.hourlyRate.max)}
                  </div>
                </div>
              )}

              {/* Response Time */}
              <div className="flex items-center gap-1 text-sm">
                <Clock className="h-3 w-3 text-muted-foreground" />
                <span className="text-muted-foreground">پاسخ در</span>
                <span className="font-medium">{coderUser.responseTime} ساعت</span>
              </div>
            </>
          )}

          {!isCoder && (
            <>
              {/* Company */}
              {buyerUser.company && (
                <div className="text-sm">
                  <div className="text-muted-foreground">شرکت</div>
                  <div className="font-medium">{buyerUser.company}</div>
                </div>
              )}

              {/* Stats */}
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div>
                  <div className="text-muted-foreground">پروژه‌های منتشر شده</div>
                  <div className="font-medium">{buyerUser.projectsPosted}</div>
                </div>
                <div>
                  <div className="text-muted-foreground">نرخ استخدام</div>
                  <div className="font-medium">{buyerUser.hireRate}%</div>
                </div>
              </div>
            </>
          )}

          {/* Social Links */}
          {variant === 'detailed' && user.socialLinks && (
            <div className="flex gap-2">
              {user.socialLinks.github && (
                <Button variant="outline" size="sm" asChild>
                  <a href={user.socialLinks.github} target="_blank" rel="noopener noreferrer">
                    <Github className="h-4 w-4" />
                  </a>
                </Button>
              )}
              {user.socialLinks.linkedin && (
                <Button variant="outline" size="sm" asChild>
                  <a href={user.socialLinks.linkedin} target="_blank" rel="noopener noreferrer">
                    <Linkedin className="h-4 w-4" />
                  </a>
                </Button>
              )}
              {user.website && (
                <Button variant="outline" size="sm" asChild>
                  <a href={user.website} target="_blank" rel="noopener noreferrer">
                    <Globe className="h-4 w-4" />
                  </a>
                </Button>
              )}
            </div>
          )}
        </div>
      </CardContent>

      {showActions && (
        <CardFooter className="pt-3">
          <div className="flex gap-2 w-full">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onViewProfile?.(user.id)}
              className="flex-1"
            >
              <ExternalLink className="h-4 w-4 ml-1" />
              مشاهده پروفایل
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => onSendMessage?.(user.id)}
            >
              <MessageCircle className="h-4 w-4" />
            </Button>
            
            {isCoder && coderUser.availability === 'available' && (
              <Button
                size="sm"
                onClick={() => onHire?.(user.id)}
              >
                استخدام
              </Button>
            )}
          </div>
        </CardFooter>
      )}
    </Card>
  );
}
