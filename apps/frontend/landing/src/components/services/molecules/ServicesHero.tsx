import { Button } from '@/components/ui/button';
import { useAuthModal } from '@/hooks/auth/use-auth-modal';
import React from 'react';

interface ServicesHeroProps {
  className?: string;
}

const ServicesHero: React.FC<ServicesHeroProps> = ({ className }) => {
  const { openModal } = useAuthModal();
  return (
    <section
      className={`py-6 lg:py-10 bg-gradient-to-br from-background via-card/20 to-secondary/10 relative overflow-hidden ${className || ''}`}
    >
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -left-40 w-96 h-80 rounded-full bg-primary/10 mix-blend-screen filter blur-3xl opacity-70 animate-pulse"></div>
        <div
          className="absolute -bottom-20 right-20 w-80 h-72 rounded-full bg-accent/10 mix-blend-screen filter blur-3xl opacity-60 animate-pulse"
          style={{ animationDelay: '2s' }}
        ></div>
      </div>

      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 text-primary/5 font-mono text-lg rotate-45 animate-bounce">
          {'<Services />'}
        </div>
        <div
          className="absolute top-1/3 right-1/3 text-accent/5 font-mono text-lg -rotate-45 animate-bounce"
          style={{ animationDelay: '1s' }}
        >
          {'{ comprehensive }'}
        </div>
        <div
          className="absolute bottom-1/4 left-1/2 text-primary/5 font-mono text-lg rotate-12 animate-bounce"
          style={{ animationDelay: '2s' }}
        >
          export default
        </div>
        <div className="absolute top-1/2 right-1/4 text-accent/5 font-mono text-sm rotate-180 animate-pulse">
          function solve()
        </div>
      </div>

      <div className="container relative z-10 mx-auto px-4 text-center">
        <div className="max-w-4xl mx-auto">
          <div className="inline-block mb-6 px-4 py-2 bg-card/80 text-primary rounded-lg text-sm font-mono border border-primary/20 shadow-lg">
            <span className="text-accent">import</span>{' '}
            {'{ ProfessionalServices }'}{' '}
            <span className="text-accent">from</span>{' '}
            <span className="text-primary">'@dolfak/solutions'</span>
          </div>

          <h1 className="text-md md:text-xl lg:text-2xl xl:text-3xl font-bold mb-6 font-mono leading-tight">
            <span className="text-foreground">خدمات </span>
            <span className="text-primary">جامع </span>
            <span className="text-foreground">فناوری</span>
            <br />
            <span className="text-accent">برای کسب‌وکار شما</span>
          </h1>

          <div dir="ltr" className="max-w-3xl mx-auto mb-8">
            <div className="bg-card/40 border border-primary/10 rounded-xl p-6 font-mono text-left">
              <div className="text-accent mb-2 text-sm">
                {'// راهکارهای نوآورانه و حرفه‌ای'}
              </div>
              <div className="text-foreground text-base leading-relaxed">
                <span className="text-primary">const</span> ourMission = ()
                =&gt; {'{'}
                <br />
                <span className="ml-4 text-muted-foreground">
                  <span className="text-accent">return</span> 'ارائه بهترین
                  خدمات فناوری اطلاعات';
                  <br />
                </span>
                {'}'};
              </div>
            </div>
          </div>

          <p className="text-lg md:text-xl text-muted-foreground mb-8 leading-relaxed max-w-3xl mx-auto">
            از توسعه نرم‌افزار سفارشی تا پیاده‌سازی راهکارهای هوش مصنوعی،
            <span className="text-primary font-medium">
              {' '}
              ما تمام نیازهای دیجیتال{' '}
            </span>
            کسب و کار شما را با کیفیت بالا و قیمت مناسب برآورده می‌کنیم.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Button
              size="lg"
              className=" transition-all duration-300 hover:scale-105 group neo-brutal text-green-200! bg-gradient-to-r from-primary to-accent hover:from-primary/80 hover:to-accent/80 shadow-sm font-mono text-[clamp(0.4rem,3vw,0.8rem)]!"
              onClick={openModal}
            >
              مشاهده تمام خدمات
            </Button>
            <Button
              size="lg"
              className=" transition-all duration-300 hover:scale-105 group neo-brutal text-green-200! bg-gradient-to-r from-primary to-accent hover:from-primary/80 hover:to-accent/80 shadow-sm font-mono text-[clamp(0.4rem,3vw,0.8rem)]!"
              onClick={openModal}
            >
              درخواست مشاوره رایگان
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16">
            <div className="bg-card/60 backdrop-blur-sm border border-primary/20 rounded-xl p-6 text-center hover:border-primary/40 transition-all duration-300">
              <div className="text-3xl font-bold text-primary font-mono mb-2">
                ۱۵+
              </div>
              <div className="text-sm text-muted-foreground font-mono">
                نوع خدمات مختلف
              </div>
            </div>
            <div className="bg-card/60 backdrop-blur-sm border border-primary/20 rounded-xl p-6 text-center hover:border-primary/40 transition-all duration-300">
              <div className="text-3xl font-bold text-accent font-mono mb-2">
                ۵+
              </div>
              <div className="text-sm text-muted-foreground font-mono">
                سال تجربه
              </div>
            </div>
            <div className="bg-card/60 backdrop-blur-sm border border-primary/20 rounded-xl p-6 text-center hover:border-primary/40 transition-all duration-300">
              <div className="text-3xl font-bold text-primary font-mono mb-2">
                ۲۴/۷
              </div>
              <div className="text-sm text-muted-foreground font-mono">
                پشتیبانی
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ServicesHero;
