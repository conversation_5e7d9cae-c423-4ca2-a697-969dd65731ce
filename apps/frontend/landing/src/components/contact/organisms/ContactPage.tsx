import React from 'react';
import ContactForm from '../molecules/ContactForm';
import ContactHero from '../molecules/ContactHero';
import ContactInfo from '../molecules/ContactInfo';
import SocialLinks from '../molecules/SocialLinks';

interface ContactPageProps {}

const ContactPage: React.FC<ContactPageProps> = () => {
  return (
    <div className="min-h-screen">
      <ContactHero />

      <section className="py-10 bg-background">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="space-y-8">
              <ContactInfo />
              <SocialLinks />
            </div>
            <div>
              <ContactForm />
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ContactPage;
