use serde::{Deserialize, Serialize};
use utoipa::ToSchema;
use validator::Validate;
use crate::modules::users::entities::UserRole;

#[derive(Debug, Deserialize, Validate, ToSchema)]
#[allow(dead_code)]
pub struct GoogleAuthCallbackDto {
    #[validate(length(min = 1, message = "Code is required"))]
    pub code: String,

    pub state: Option<String>,

    #[schema(example = "buyer")]
    pub role: Option<UserRole>,
}

#[derive(Debug, Serialize, ToSchema)]
pub struct GoogleAuthUrlDto {
    pub auth_url: String,
    pub state: String,
}

#[derive(Debug, Serialize, ToSchema)]
pub struct GoogleUserDto {
    pub google_id: String,
    pub email: String,
    pub name: String,
    pub picture: Option<String>,
    pub verified_email: bool,
}
