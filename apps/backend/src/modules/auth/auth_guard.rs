use actix_web::HttpRequest;
use crate::common::errors::{AppError, CustomErrors};
use super::jwt_strategy::JwtStrategy;

pub struct AuthGuard {
    jwt_strategy: JwtStrategy,
}

impl AuthGuard {
    pub fn new(jwt_strategy: JwtStrategy) -> Self {
        Self { jwt_strategy }
    }

    pub fn extract_user_id_from_request(&self, req: &HttpRequest) -> Result<String, AppError> {
        if let Some(token) = self.extract_token_from_request(req)? {
            return self.jwt_strategy.verify_token(&token);
        }

        Err(CustomErrors::invalid_token())
    }

    pub fn extract_token_from_request(&self, req: &HttpRequest) -> Result<Option<String>, AppError> {
        if let Some(token) = self.extract_token_from_header(req)? {
            return Ok(Some(token));
        }

        if let Some(token) = self.extract_token_from_cookies(req)? {
            return Ok(Some(token));
        }

        Ok(None)
    }

    fn extract_token_from_header(&self, req: &HttpRequest) -> Result<Option<String>, AppError> {
        if let Some(auth_header) = req.headers().get("Authorization") {
            let auth_str = auth_header.to_str()
                .map_err(|_| CustomErrors::invalid_token())?;

            if let Some(token) = auth_str.strip_prefix("Bearer ") {
                return Ok(Some(token.to_string()));
            }
        }
        Ok(None)
    }

    fn extract_token_from_cookies(&self, req: &HttpRequest) -> Result<Option<String>, AppError> {
        if let Some(cookie_header) = req.headers().get("Cookie") {
            let cookie_str = cookie_header.to_str()
                .map_err(|_| CustomErrors::invalid_token())?;

            for cookie in cookie_str.split(';') {
                let cookie = cookie.trim();
                if let Some(token_value) = cookie.strip_prefix("access_token=") {
                    return Ok(Some(token_value.to_string()));
                }
            }
        }
        Ok(None)
    }

    pub fn extract_refresh_token_from_cookies(&self, req: &HttpRequest) -> Result<Option<String>, AppError> {
        if let Some(cookie_header) = req.headers().get("Cookie") {
            let cookie_str = cookie_header.to_str()
                .map_err(|_| CustomErrors::invalid_token())?;

            for cookie in cookie_str.split(';') {
                let cookie = cookie.trim();
                if let Some(token_value) = cookie.strip_prefix("refresh_token=") {
                    return Ok(Some(token_value.to_string()));
                }
            }
        }
        Ok(None)
    }
}
