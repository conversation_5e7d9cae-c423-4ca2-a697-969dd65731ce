import { Project, Proposal } from '@/lib/types/project';

export const mockProjects: Project[] = [
  {
    id: '1',
    title: 'توسعه سایت فروشگاهی',
    description: 'نیاز به توسعه یک سایت فروشگاهی کامل با پنل مدیریت، سیستم پرداخت و مدیریت محصولات. سایت باید ریسپانسیو بوده و از تکنولوژی‌های مدرن استفاده کند.',
    budget: {
      min: 50000000,
      max: 80000000,
      currency: 'IRR'
    },
    deadline: '2025-03-15',
    status: 'published',
    category: 'وب سایت',
    skills: ['React', 'Node.js', 'MongoDB', 'Tailwind CSS'],
    complexity: 'intermediate',
    buyerId: 'buyer1',
    buyer: {
      id: 'buyer1',
      username: 'علی احمدی',
      rating: 4.8,
      completedProjects: 12
    },
    proposals: [],
    createdAt: '2025-01-20T10:00:00Z',
    updatedAt: '2025-01-20T10:00:00Z',
    milestones: [
      {
        id: 'm1',
        title: 'طراحی و UI/UX',
        description: 'طراحی رابط کاربری و تجربه کاربری',
        amount: 20000000,
        dueDate: '2025-02-15',
        status: 'pending'
      },
      {
        id: 'm2',
        title: 'توسعه بک‌اند',
        description: 'پیاده‌سازی API و دیتابیس',
        amount: 30000000,
        dueDate: '2025-02-28',
        status: 'pending'
      },
      {
        id: 'm3',
        title: 'توسعه فرانت‌اند',
        description: 'پیاده‌سازی رابط کاربری',
        amount: 25000000,
        dueDate: '2025-03-10',
        status: 'pending'
      },
      {
        id: 'm4',
        title: 'تست و راه‌اندازی',
        description: 'تست کامل و راه‌اندازی نهایی',
        amount: 5000000,
        dueDate: '2025-03-15',
        status: 'pending'
      }
    ]
  },
  {
    id: '2',
    title: 'اپلیکیشن موبایل تاکسی',
    description: 'توسعه اپلیکیشن موبایل برای سرویس تاکسی آنلاین شامل اپ مسافر، راننده و پنل مدیریت.',
    budget: {
      min: 120000000,
      max: 200000000,
      currency: 'IRR'
    },
    deadline: '2025-05-01',
    status: 'in_progress',
    category: 'اپلیکیشن موبایل',
    skills: ['Flutter', 'Firebase', 'Google Maps API', 'Node.js'],
    complexity: 'expert',
    buyerId: 'buyer2',
    buyer: {
      id: 'buyer2',
      username: 'مریم کریمی',
      rating: 4.9,
      completedProjects: 8
    },
    proposals: [],
    assignedCoder: {
      id: 'coder1',
      username: 'محمد رضایی',
      rating: 4.7
    },
    createdAt: '2025-01-10T14:30:00Z',
    updatedAt: '2025-01-22T09:15:00Z'
  },
  {
    id: '3',
    title: 'سیستم مدیریت انبار',
    description: 'توسعه سیستم مدیریت انبار برای شرکت تولیدی با قابلیت‌های ردیابی موجودی، گزارش‌گیری و مدیریت سفارشات.',
    budget: {
      min: 30000000,
      max: 50000000,
      currency: 'IRR'
    },
    deadline: '2025-04-01',
    status: 'published',
    category: 'دسکتاپ',
    skills: ['C#', '.NET', 'SQL Server', 'WPF'],
    complexity: 'intermediate',
    buyerId: 'buyer3',
    buyer: {
      id: 'buyer3',
      username: 'حسن محمدی',
      rating: 4.6,
      completedProjects: 15
    },
    proposals: [],
    createdAt: '2025-01-18T16:45:00Z',
    updatedAt: '2025-01-18T16:45:00Z'
  },
  {
    id: '4',
    title: 'API سرویس پرداخت',
    description: 'توسعه API برای سرویس پرداخت آنلاین با پشتیبانی از درگاه‌های مختلف پرداخت ایرانی.',
    budget: {
      min: 40000000,
      max: 60000000,
      currency: 'IRR'
    },
    deadline: '2025-03-20',
    status: 'published',
    category: 'API و بک‌اند',
    skills: ['Node.js', 'Express', 'PostgreSQL', 'Redis', 'Docker'],
    complexity: 'expert',
    buyerId: 'buyer4',
    buyer: {
      id: 'buyer4',
      username: 'فاطمه نوری',
      rating: 4.7,
      completedProjects: 20
    },
    proposals: [],
    createdAt: '2025-01-15T11:20:00Z',
    updatedAt: '2025-01-15T11:20:00Z'
  },
  {
    id: '5',
    title: 'طراحی UI/UX اپلیکیشن بانکی',
    description: 'طراحی رابط کاربری و تجربه کاربری برای اپلیکیشن موبایل بانکی با تمرکز بر امنیت و سادگی استفاده.',
    budget: {
      min: 25000000,
      max: 35000000,
      currency: 'IRR'
    },
    deadline: '2025-02-28',
    status: 'completed',
    category: 'طراحی UI/UX',
    skills: ['Figma', 'Adobe XD', 'Prototyping', 'User Research'],
    complexity: 'intermediate',
    buyerId: 'buyer5',
    buyer: {
      id: 'buyer5',
      username: 'رضا صادقی',
      rating: 4.8,
      completedProjects: 10
    },
    proposals: [],
    assignedCoder: {
      id: 'coder2',
      username: 'سارا احمدی',
      rating: 4.9
    },
    createdAt: '2025-01-05T09:00:00Z',
    updatedAt: '2025-01-23T14:30:00Z'
  }
];

export const mockProposals: Proposal[] = [
  {
    id: 'p1',
    projectId: '1',
    coderId: 'coder3',
    coder: {
      id: 'coder3',
      username: 'امیر حسینی',
      rating: 4.6,
      completedProjects: 18,
      skills: ['React', 'Node.js', 'MongoDB', 'Tailwind CSS']
    },
    coverLetter: 'سلام، من با بیش از ۵ سال تجربه در توسعه وب، آماده انجام این پروژه هستم. نمونه کارهای مشابه در پروفایل من موجود است.',
    proposedBudget: 65000000,
    proposedDeadline: '2025-03-10',
    status: 'pending',
    createdAt: '2025-01-21T10:30:00Z',
    updatedAt: '2025-01-21T10:30:00Z'
  },
  {
    id: 'p2',
    projectId: '1',
    coderId: 'coder4',
    coder: {
      id: 'coder4',
      username: 'نیلوفر کریمی',
      rating: 4.8,
      completedProjects: 25,
      skills: ['React', 'Node.js', 'MongoDB', 'TypeScript']
    },
    coverLetter: 'با توجه به تجربه گسترده‌ام در توسعه فروشگاه‌های آنلاین، می‌توانم این پروژه را با کیفیت بالا و در زمان مقرر تحویل دهم.',
    proposedBudget: 70000000,
    proposedDeadline: '2025-03-12',
    status: 'pending',
    createdAt: '2025-01-21T14:15:00Z',
    updatedAt: '2025-01-21T14:15:00Z'
  }
];

export function getProjectsByBuyer(buyerId: string): Project[] {
  return mockProjects.filter(project => project.buyerId === buyerId);
}

export function getProjectsByCoder(coderId: string): Project[] {
  return mockProjects.filter(project => project.assignedCoder?.id === coderId);
}

export function getAvailableProjects(): Project[] {
  return mockProjects.filter(project => project.status === 'published');
}

export function getProposalsByProject(projectId: string): Proposal[] {
  return mockProposals.filter(proposal => proposal.projectId === projectId);
}

export function getProposalsByCoder(coderId: string): Proposal[] {
  return mockProposals.filter(proposal => proposal.coderId === coderId);
}
