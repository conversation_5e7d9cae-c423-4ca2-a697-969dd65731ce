export interface Project {
  id: string;
  title: string;
  description: string;
  budget: {
    min: number;
    max: number;
    currency: 'IRR' | 'USD';
  };
  deadline: string;
  status: 'draft' | 'published' | 'in_progress' | 'completed' | 'cancelled';
  category: string;
  skills: string[];
  complexity: 'beginner' | 'intermediate' | 'expert';
  buyerId: string;
  buyer: {
    id: string;
    username: string;
    avatar?: string;
    rating: number;
    completedProjects: number;
  };
  proposals: Proposal[];
  assignedCoder?: {
    id: string;
    username: string;
    avatar?: string;
    rating: number;
  };
  createdAt: string;
  updatedAt: string;
  attachments?: ProjectAttachment[];
  milestones?: ProjectMilestone[];
}

export interface Proposal {
  id: string;
  projectId: string;
  coderId: string;
  coder: {
    id: string;
    username: string;
    avatar?: string;
    rating: number;
    completedProjects: number;
    skills: string[];
  };
  coverLetter: string;
  proposedBudget: number;
  proposedDeadline: string;
  status: 'pending' | 'accepted' | 'rejected' | 'withdrawn';
  createdAt: string;
  updatedAt: string;
}

export interface ProjectAttachment {
  id: string;
  name: string;
  url: string;
  type: string;
  size: number;
  uploadedAt: string;
}

export interface ProjectMilestone {
  id: string;
  title: string;
  description: string;
  amount: number;
  dueDate: string;
  status: 'pending' | 'in_progress' | 'completed' | 'overdue';
  completedAt?: string;
}

export interface ProjectFilter {
  category?: string;
  skills?: string[];
  budgetRange?: {
    min: number;
    max: number;
  };
  complexity?: string;
  status?: string;
  sortBy?: 'newest' | 'oldest' | 'budget_high' | 'budget_low' | 'deadline';
}

export interface CreateProjectData {
  title: string;
  description: string;
  category: string;
  skills: string[];
  complexity: 'beginner' | 'intermediate' | 'expert';
  budget: {
    min: number;
    max: number;
    currency: 'IRR' | 'USD';
  };
  deadline: string;
  attachments?: File[];
  milestones?: Omit<ProjectMilestone, 'id' | 'status' | 'completedAt'>[];
}

export interface CreateProposalData {
  projectId: string;
  coverLetter: string;
  proposedBudget: number;
  proposedDeadline: string;
}

export const PROJECT_CATEGORIES = [
  'وب سایت',
  'اپلیکیشن موبایل',
  'دسکتاپ',
  'API و بک‌اند',
  'طراحی UI/UX',
  'تست نرم‌افزار',
  'DevOps',
  'هوش مصنوعی',
  'بلاک چین',
  'بازی',
  'سایر'
] as const;

export const PROGRAMMING_SKILLS = [
  'JavaScript',
  'TypeScript',
  'React',
  'Vue.js',
  'Angular',
  'Node.js',
  'Python',
  'Django',
  'Flask',
  'Java',
  'Spring',
  'C#',
  '.NET',
  'PHP',
  'Laravel',
  'Ruby',
  'Rails',
  'Go',
  'Rust',
  'Swift',
  'Kotlin',
  'Flutter',
  'React Native',
  'HTML/CSS',
  'Tailwind CSS',
  'Bootstrap',
  'MySQL',
  'PostgreSQL',
  'MongoDB',
  'Redis',
  'Docker',
  'Kubernetes',
  'AWS',
  'Azure',
  'GCP',
  'Git',
  'CI/CD'
] as const;

export type ProjectCategory = typeof PROJECT_CATEGORIES[number];
export type ProgrammingSkill = typeof PROGRAMMING_SKILLS[number];
