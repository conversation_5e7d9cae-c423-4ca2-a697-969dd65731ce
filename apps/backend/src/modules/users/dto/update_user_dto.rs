use serde::{Deserialize, Serialize};
use validator::Validate;
use utoipa::ToSchema;
use crate::common::validators::custom_validators::validate_username;

#[derive(Debug, Deserialize, Serialize, Validate, ToSchema)]
pub struct UpdateUserDto {
    #[validate(email(message = "Invalid email format"))]
    #[schema(example = "<EMAIL>")]
    pub email: Option<String>,

    #[validate(custom(function = "validate_username"))]
    #[schema(example = "newusername")]
    pub username: Option<String>,
}
