import api from '../core/api';
import {
  AuthResponse,
  GoogleAuthCallbackPayload,
  GoogleAuthUrlResponse,
  LoginPayload,
  PhoneLoginPayload,
  RegisterPayload,
  SendOtpPayload,
  VerifyOtpPayload,
} from './types';

export const login = async (payload: LoginPayload): Promise<AuthResponse> => {
  const { data } = await api.post<AuthResponse>('/auth/login', payload);

  if (!data.access_token || !data.refresh_token) {
    throw new Error('Invalid response: missing tokens');
  }

  return data;
};

export const register = async (
  payload: RegisterPayload,
): Promise<AuthResponse> => {
  const { data } = await api.post<AuthResponse>('/auth/register', payload);

  if (!data.access_token || !data.refresh_token) {
    throw new Error('Invalid response: missing tokens');
  }

  return data;
};

export const sendOtp = async (payload: SendOtpPayload): Promise<void> => {
  await api.post('/auth/send-otp', payload);
};

export const verifyOtp = async (payload: VerifyOtpPayload): Promise<void> => {
  await api.post('/auth/verify-otp', payload);
};

export const phoneLogin = async (
  payload: PhoneLoginPayload,
): Promise<AuthResponse> => {
  const { data } = await api.post<AuthResponse>('/auth/phone-login', payload);

  if (!data.access_token || !data.refresh_token) {
    throw new Error('Invalid response: missing tokens');
  }

  return data;
};

export const getGoogleAuthUrl = async (): Promise<GoogleAuthUrlResponse> => {
  const { data } = await api.get<GoogleAuthUrlResponse>('/auth/google/url');
  return data;
};

export const googleCallback = async (
  payload: GoogleAuthCallbackPayload,
): Promise<AuthResponse> => {
  const { data } = await api.post<AuthResponse>(
    '/auth/google/callback',
    payload,
  );

  if (!data.access_token || !data.refresh_token) {
    throw new Error('Invalid response: missing tokens');
  }

  return data;
};

export const refreshToken = async (
  refreshToken?: string,
): Promise<AuthResponse> => {
  const { data } = await api.post<AuthResponse>('/auth/refresh', {
    refresh_token: refreshToken || '',
  });

  if (!data.access_token || !data.refresh_token) {
    throw new Error('Invalid response: missing tokens');
  }

  return data;
};

export const logout = async (): Promise<void> => {
  try {
    await api.post('/auth/logout', {}, { timeout: 5000 });
  } catch (error) {
    console.warn('Logout API call failed:', error);
  }
};

export const getCurrentUser = async (): Promise<any> => {
  const { data } = await api.get('/users/me');
  return data;
};

export const verifyToken = async (): Promise<boolean> => {
  try {
    await getCurrentUser();
    return true;
  } catch {
    return false;
  }
};
