import { WizardStepProps } from '@/components/shared/wizard/wizard-types';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, Mail, MessageCircle, Phone } from 'lucide-react';

interface ConfirmationData {
  fullName?: string;
  email?: string;
  phone?: string;
  projectType?: string;
  description?: string;
}

const getProjectTypeLabel = (projectType: string) => {
  const types: Record<string, string> = {
    webapp: 'وب اپلیکیشن',
    mobile: 'اپلیکیشن موبایل',
    startup: 'استارتاپ',
  };
  return types[projectType] || projectType;
};

export const ConfirmationStep = ({
  data,
  onNext,
  context,
  canGoNext,
}: WizardStepProps<ConfirmationData>) => {
  const handleComplete = () => {
    if (canGoNext) {
      onNext();
    }
  };

  // Get all data from previous steps
  const allData = context.allStepData;
  const combinedData = Object.values(allData).reduce((acc, stepData) => {
    if (stepData && typeof stepData === 'object') {
      return { ...acc, ...stepData };
    }
    return acc;
  }, {} as ConfirmationData);

  return (
    <Card className="border-0 shadow-none bg-transparent">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <CheckCircle className="w-4 h-4 text-primary" />
          تایید نهایی
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="p-3 bg-muted/30 rounded-lg">
          <h4 className="font-medium mb-2 text-sm">اطلاعات وارد شده:</h4>
          <div className="space-y-1 text-xs">
            <div className="flex justify-between">
              <span>نام:</span>
              <span className="font-medium">
                {combinedData.fullName || 'وارد نشده'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>ایمیل:</span>
              <span className="font-medium">
                {combinedData.email || 'وارد نشده'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>تلفن:</span>
              <span className="font-medium">
                {combinedData.phone || 'وارد نشده'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>نوع پروژه:</span>
              <span className="font-medium">
                {combinedData.projectType
                  ? getProjectTypeLabel(combinedData.projectType)
                  : 'انتخاب نشده'}
              </span>
            </div>
          </div>
        </div>

        {combinedData.description && (
          <div className="p-3 bg-muted/30 rounded-lg">
            <h4 className="font-medium mb-1 text-sm">توضیحات پروژه:</h4>
            <p className="text-xs text-muted-foreground">
              {combinedData.description}
            </p>
          </div>
        )}

        <div className="text-center space-y-3">
          <div className="flex items-center justify-center gap-3 text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <Phone className="w-3 h-3" />
              <span>تماس</span>
            </div>
            <div className="flex items-center gap-1">
              <Mail className="w-3 h-3" />
              <span>ایمیل</span>
            </div>
            <div className="flex items-center gap-1">
              <MessageCircle className="w-3 h-3" />
              <span>مشاوره</span>
            </div>
          </div>

          <p className="text-xs text-muted-foreground">
            بعد از تایید نهایی تیم ما در سریع‌ترین زمان با شما تماس خواهد گرفت
          </p>
        </div>
      </CardContent>
    </Card>
  );
};
