use crate::common::{CommonModule, errors::{AppError, CustomErrors}};
use crate::modules::users::{users_service::UsersService, entities::UserEntity};
use crate::modules::shared::otp::{PhoneOtpService, SendOtpDto, VerifyOtpDto, PhoneLoginDto};
use super::{dto::{LoginDto, RegisterDto, AuthResponseDto}, jwt_strategy::JwtStrategy};
use bcrypt::{hash, verify, DEFAULT_COST};

pub struct AuthService {
    users_service: UsersService,
    jwt_strategy: JwtStrategy,
    phone_otp_service: PhoneOtpService,
    common_module: CommonModule,
}

impl AuthService {
    pub fn new(common_module: &CommonModule) -> Self {
        Self {
            users_service: UsersService::new(common_module),
            jwt_strategy: JwtStrategy::new(&common_module.config.jwt),
            phone_otp_service: PhoneOtpService::new(common_module),
            common_module: common_module.clone(),
        }
    }

    pub async fn login(&self, login_dto: &LoginDto) -> Result<AuthResponseDto, AppError> {
        let user = self.users_service.find_by_email(&login_dto.email).await?
            .ok_or_else(CustomErrors::invalid_credentials)?;

        if !verify(&login_dto.password, &user.password_hash)
            .map_err(|_| CustomErrors::invalid_credentials())? {
            return Err(CustomErrors::invalid_credentials());
        }

        let access_token = self.jwt_strategy.generate_access_token(&user.id.to_string())?;
        let refresh_token = self.jwt_strategy.generate_refresh_token(&user.id.to_string())?;

        Ok(AuthResponseDto {
            access_token,
            refresh_token,
            token_type: "Bearer".to_string(),
            expires_in: self.common_module.config.jwt.expires_in,
            user: user.into(),
        })
    }

    pub async fn register(&self, register_dto: &RegisterDto) -> Result<AuthResponseDto, AppError> {
        if register_dto.password != register_dto.confirm_password {
            return Err(CustomErrors::passwords_do_not_match());
        }

        if self.users_service.find_by_email(&register_dto.email).await?.is_some() {
            return Err(CustomErrors::email_already_exists());
        }

        if self.users_service.find_by_username(&register_dto.username).await?.is_some() {
            return Err(CustomErrors::username_already_exists());
        }

        let password_hash = hash(&register_dto.password, DEFAULT_COST)
            .map_err(|_| AppError::InternalServer("Failed to hash password".to_string()))?;

        let role = register_dto.role.clone().unwrap_or_default();

        let new_user = UserEntity::new_with_role(
            register_dto.email.clone(),
            register_dto.username.clone(),
            password_hash,
            role,
        );

        let created_user = self.users_service.create(&new_user).await?;
        let access_token = self.jwt_strategy.generate_access_token(&created_user.id.to_string())?;
        let refresh_token = self.jwt_strategy.generate_refresh_token(&created_user.id.to_string())?;

        Ok(AuthResponseDto {
            access_token,
            refresh_token,
            token_type: "Bearer".to_string(),
            expires_in: self.common_module.config.jwt.expires_in,
            user: created_user.into(),
        })
    }

    pub async fn refresh_token(&self, token: &str) -> Result<AuthResponseDto, AppError> {
        let user_id = self.jwt_strategy.verify_refresh_token(token)?;
        let user = self.users_service.find_by_id(&user_id).await?
            .ok_or_else(CustomErrors::user_not_found)?;

        let access_token = self.jwt_strategy.generate_access_token(&user.id.to_string())?;
        let refresh_token = self.jwt_strategy.generate_refresh_token(&user.id.to_string())?;

        Ok(AuthResponseDto {
            access_token,
            refresh_token,
            token_type: "Bearer".to_string(),
            expires_in: self.common_module.config.jwt.expires_in,
            user,
        })
    }

    pub async fn logout(&self, token: &str) -> Result<(), AppError> {
        let _user_id = self.jwt_strategy.verify_token(token)?;
        Ok(())
    }

    pub async fn send_otp(&self, send_otp_dto: &SendOtpDto) -> Result<(), AppError> {
        self.phone_otp_service.send_otp(&send_otp_dto.phone).await
    }

    pub async fn verify_otp(&self, verify_otp_dto: &VerifyOtpDto) -> Result<(), AppError> {
        self.phone_otp_service.verify_otp(&verify_otp_dto.phone, &verify_otp_dto.otp_code).await?;
        Ok(())
    }

    pub async fn phone_otp_login(&self, phone_login_dto: &PhoneLoginDto) -> Result<AuthResponseDto, AppError> {
        let phone = &phone_login_dto.phone;
        let otp_code = &phone_login_dto.otp_code;
        let role = phone_login_dto.role.clone().unwrap_or_default();

        let verified_otp = self.phone_otp_service.verify_otp(phone, otp_code).await?;

        let existing_user = self.users_service.find_by_phone(phone).await?;
        if let Some(user) = existing_user {
            let access_token = self.jwt_strategy.generate_access_token(&user.id.to_string())?;
            let refresh_token = self.jwt_strategy.generate_refresh_token(&user.id.to_string())?;

            return Ok(AuthResponseDto {
                access_token,
                refresh_token,
                token_type: "Bearer".to_string(),
                expires_in: self.common_module.config.jwt.expires_in,
                user: user.into(),
            });
        }

        let username = format!("phone_{}", phone.replace('+', ""));

        if self.users_service.find_by_phone(phone).await?.is_some() {
            return Err(CustomErrors::phone_already_exists());
        }

        if self.users_service.find_by_username(&username).await?.is_some() {
            return Err(CustomErrors::username_already_exists());
        }

        let temp_password_hash = hash("temporary_password", DEFAULT_COST)
            .map_err(|_| AppError::InternalServer("Failed to hash password".to_string()))?;

        let now = chrono::Utc::now();
        let new_user = crate::modules::users::entities::NewUserEntity {
            id: uuid::Uuid::new_v4(),
            email: format!("{username}@phone.temp"),
            username: username.to_string(),
            password_hash: temp_password_hash,
            created_at: now,
            updated_at: now,
            phone: Some(verified_otp.phone),
            phone_verified: Some(true),
            role,
        };

        let created_user = self.users_service.create(&new_user).await?;
        let access_token = self.jwt_strategy.generate_access_token(&created_user.id.to_string())?;
        let refresh_token = self.jwt_strategy.generate_refresh_token(&created_user.id.to_string())?;

        Ok(AuthResponseDto {
            access_token,
            refresh_token,
            token_type: "Bearer".to_string(),
            expires_in: self.common_module.config.jwt.expires_in,
            user: created_user.into(),
        })
    }

    #[allow(dead_code)]
    pub async fn register_with_phone(&self, phone: &str, otp_code: &str, username: &str) -> Result<AuthResponseDto, AppError> {
        let verified_otp = self.phone_otp_service
            .verify_otp(phone, otp_code)
            .await?;

        if self.users_service.find_by_phone(&verified_otp.phone).await?.is_some() {
            return Err(CustomErrors::phone_already_exists());
        }

        if self.users_service.find_by_username(username).await?.is_some() {
            return Err(CustomErrors::username_already_exists());
        }

        let temp_password_hash = hash("temporary_password", DEFAULT_COST)
            .map_err(|_| AppError::InternalServer("Failed to hash password".to_string()))?;

        let now = chrono::Utc::now();
        let new_user = crate::modules::users::entities::NewUserEntity {
            id: uuid::Uuid::new_v4(),
            email: format!("{username}@phone.temp"),
            username: username.to_string(),
            password_hash: temp_password_hash,
            created_at: now,
            updated_at: now,
            phone: Some(verified_otp.phone),
            phone_verified: Some(true),
            role: crate::modules::users::entities::UserRole::Buyer,
        };

        let created_user = self.users_service.create(&new_user).await?;
        let access_token = self.jwt_strategy.generate_access_token(&created_user.id.to_string())?;
        let refresh_token = self.jwt_strategy.generate_refresh_token(&created_user.id.to_string())?;

        Ok(AuthResponseDto {
            access_token,
            refresh_token,
            token_type: "Bearer".to_string(),
            expires_in: self.common_module.config.jwt.expires_in,
            user: created_user.into(),
        })
    }

    pub fn get_jwt_config(&self) -> &crate::common::config::JwtConfig {
        &self.common_module.config.jwt
    }
}
