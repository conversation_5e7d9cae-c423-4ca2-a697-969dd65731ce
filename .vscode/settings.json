{
  // Optimize VS Code performance for Turborepo monorepo
  "files.watcherExclude": {
    "**/.git/objects/**": true,
    "**/.git/subtree-cache/**": true,
    "**/node_modules/**": true,
    "**/.next/**": true,
    "**/.nuxt/**": true,
    "**/dist/**": true,
    "**/build/**": true,
    "**/.turbo/**": true,
    "**/coverage/**": true,
    "**/.nyc_output/**": true,
    "**/tmp/**": true,
    "**/temp/**": true
  },
  "search.exclude": {
    "**/node_modules": true,
    "**/.next": true,
    "**/.nuxt": true,
    "**/dist": true,
    "**/build": true,
    "**/.turbo": true,
    "**/coverage": true,
    "**/.nyc_output": true,
    "**/tmp": true,
    "**/temp": true,
    "**/*.log": true
  },
  "files.exclude": {
    "**/.git": true,
    "**/.DS_Store": true,
    "**/node_modules": false,
    "**/.next": false,
    "**/dist": false,
    "**/.turbo": false
  },
  // Workspace-specific settings
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.patterns": {
    "package.json": "package-lock.json,yarn.lock,pnpm-lock.yaml,.npmrc,turbo.json"
  }
}
