'use client';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Monitor, Moon, Sun } from 'lucide-react';
import { useTheme } from 'next-themes';

export function ThemeToggle() {
  const { setTheme, theme } = useTheme();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild className="cursor-pointer">
        <Button variant="ghost" size="sm" className="w-8! p-0!">
          <Sun className="h-[1rem]! w-[1rem]! rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
          <Moon className="absolute h-[1rem]! w-[1rem]! rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
          <span className="sr-only">تغییر تم</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem
          onClick={() => setTheme('light')}
          className={
            theme === 'light'
              ? 'bg-accent text-white! '
              : 'text-green-800! hover:text-white!'
          }
        >
          <Sun
            className={
              theme === 'light'
                ? 'mr-2 h-4 w-4 text-yellow-200'
                : 'mr-2 h-4 w-4'
            }
          />
          <span>روشن</span>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => setTheme('dark')}
          className={
            theme === 'dark'
              ? 'bg-accent hover:text-white!'
              : 'text-green-800! hover:text-white!'
          }
        >
          <Moon
            className={
              theme === 'dark' ? 'mr-2 h-4 w-4 text-blue-200' : 'mr-2 h-4 w-4'
            }
          />
          <span>تاریک</span>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => setTheme('system')}
          className={
            theme === 'system'
              ? 'bg-accent hover:text-white!'
              : 'text-green-800! hover:text-white!'
          }
        >
          <Monitor
            className={
              theme === 'system' ? 'mr-2 h-4 w-4 text-gray-200' : 'mr-2 h-4 w-4'
            }
          />
          <span>سیستم</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
