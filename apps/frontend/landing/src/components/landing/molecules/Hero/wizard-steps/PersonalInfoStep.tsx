import { WizardStepProps } from '@/components/shared/wizard/wizard-types';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { PersonalInfoData } from '@/lib/validations/wizard';
import { User } from 'lucide-react';

export const PersonalInfoStep = ({
  data,
  onDataChange,
  context,
}: WizardStepProps<PersonalInfoData>) => {
  const handleChange = (field: keyof PersonalInfoData, value: string) => {
    onDataChange({ [field]: value });
  };

  const validationErrors = context.validationErrors;

  const getFieldError = (field: string) => {
    const error = validationErrors.find((error) => {
      if (field === 'fullName') {
        return error.includes('نام و نام خانوادگی') || error.includes('نام');
      }
      if (field === 'email') {
        return error.includes('ایمیل');
      }
      if (field === 'phone') {
        return error.includes('شماره تماس');
      }
      return false;
    });

    return error;
  };

  const fullNameError = getFieldError('fullName');
  const emailError = getFieldError('email');
  const phoneError = getFieldError('phone');

  return (
    <Card className="border-0 shadow-none bg-transparent">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <User className="w-4 h-4" />
          اطلاعات شخصی
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div>
          <Label htmlFor="fullName" className="text-sm">
            نام و نام خانوادگی *
          </Label>
          <Input
            id="fullName"
            value={data.fullName || ''}
            onChange={(e) => handleChange('fullName', e.target.value)}
            placeholder="نام کامل خود را وارد کنید"
            className={`mt-1 ${fullNameError ? 'border-destructive' : ''}`}
            aria-invalid={!!fullNameError}
            aria-describedby={fullNameError ? 'fullName-error' : undefined}
          />
          {fullNameError && (
            <p id="fullName-error" className="text-xs text-destructive mt-1">
              {fullNameError}
            </p>
          )}
        </div>
        <div>
          <Label htmlFor="email" className="text-sm">
            ایمیل *
          </Label>
          <Input
            id="email"
            type="email"
            value={data.email || ''}
            onChange={(e) => handleChange('email', e.target.value)}
            placeholder="<EMAIL>"
            className={`mt-1 ${emailError ? 'border-destructive' : ''}`}
            aria-invalid={!!emailError}
            aria-describedby={emailError ? 'email-error' : undefined}
          />
          {emailError && (
            <p id="email-error" className="text-xs text-destructive mt-1">
              {emailError}
            </p>
          )}
        </div>
        <div>
          <Label htmlFor="phone" className="text-sm">
            شماره تماس *
          </Label>
          <Input
            id="phone"
            type="tel"
            value={data.phone || ''}
            onChange={(e) => handleChange('phone', e.target.value)}
            placeholder="09123456789"
            pattern="[0-9]*"
            maxLength={11}
            className={`mt-1 ${phoneError ? 'border-destructive' : ''}`}
            aria-invalid={!!phoneError}
            aria-describedby={phoneError ? 'phone-error' : undefined}
          />
          {phoneError && (
            <p id="phone-error" className="text-xs text-destructive mt-1">
              {phoneError}
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
