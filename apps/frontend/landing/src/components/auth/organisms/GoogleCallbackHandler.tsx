'use client';

import { googleCallback } from '@/lib/auth/auth';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

export function GoogleCallbackHandler() {
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    let isProcessing = false;

    const handleCallback = async () => {
      if (isProcessing) return;
      isProcessing = true;

      try {
        const code = searchParams.get('code');
        const state = searchParams.get('state');
        const storedState = localStorage.getItem('google_oauth_state');
        const error = searchParams.get('error');

        // Handle OAuth errors from Google
        if (error) {
          throw new Error(`Google OAuth error: ${error}`);
        }

        if (!code) {
          throw new Error('کد احراز هویت یافت نشد');
        }

        // Skip state validation if no stored state (for direct navigation)
        if (storedState && state !== storedState) {
          throw new Error('پارامتر state نامعتبر است');
        }

        // Check if we're already processing or have processed this code
        const processedKey = `oauth_code_processed_${code}`;
        if (localStorage.getItem(processedKey)) {
          return;
        }
        localStorage.setItem(processedKey, 'true');

        try {
          // Get the role from localStorage
          const selectedRole =
            localStorage.getItem('google_oauth_role') || 'buyer';

          const response = await googleCallback({
            code,
            state: state || undefined,
            role: selectedRole,
          });

          // Clean up stored items
          localStorage.removeItem('google_oauth_state');
          localStorage.removeItem('google_oauth_role');
          localStorage.removeItem(processedKey);

          // Redirect to home page with tokens as URL params
          const redirectPath =
            localStorage.getItem('redirect_after_login') || '/';
          localStorage.removeItem('redirect_after_login');

          const url = new URL(redirectPath, window.location.origin);
          url.searchParams.set('access_token', response.access_token);
          url.searchParams.set('refresh_token', response.refresh_token);

          window.location.href = url.toString();
        } catch (apiError) {
          localStorage.removeItem(processedKey);
          // Redirect to home page with error
          const url = new URL('/', window.location.origin);
          url.searchParams.set(
            'error',
            encodeURIComponent(
              apiError instanceof Error
                ? apiError.message
                : 'احراز هویت ناموفق بود',
            ),
          );
          window.location.href = url.toString();
        }
      } catch (error) {
        console.error('Google OAuth callback error:', error);
        // Redirect to home page with error
        const url = new URL('/', window.location.origin);
        url.searchParams.set(
          'error',
          encodeURIComponent(
            error instanceof Error ? error.message : 'احراز هویت ناموفق بود',
          ),
        );
        window.location.href = url.toString();
      } finally {
        isProcessing = false;
      }
    };

    const timeoutId = setTimeout(() => {
      handleCallback();
    }, 100);

    return () => {
      clearTimeout(timeoutId);
      isProcessing = true;
    };
  }, [searchParams, router]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
        <p className="mt-4 text-gray-600">در حال پردازش احراز هویت...</p>
      </div>
    </div>
  );
}
