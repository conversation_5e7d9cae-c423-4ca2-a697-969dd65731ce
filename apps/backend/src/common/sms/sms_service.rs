use crate::common::sms::kavenegar_client::KavenegarClient;
use crate::common::sms::types::*;
use crate::common::logger::LoggerService;
use rand::Rng;

pub struct SmsService {
    client: KavenegarClient,
}

impl SmsService {
    pub fn new(api_key: String) -> Self {
        Self {
            client: KavenegarClient::new(api_key),
        }
    }

    pub async fn send_otp_sms(&self, phone: &str, template_name: Option<&str>) -> SmsResult<(String, SmsResponse)> {
        let otp_code = self.generate_otp_code();
        let persian_code = self.convert_to_persian(&otp_code)?;

        let template = template_name.unwrap_or("otp_verification");

        LoggerService::info(&format!("Sending OTP SMS to {phone} with code: {otp_code} (Persian: {persian_code})"));

        let response = match self.client.send_lookup_sms(phone, template, &persian_code).await {
            Ok(response) => response,
            Err(SmsError::Http(ref error)) if error.contains("426") => {
                LoggerService::info("Lookup SMS failed with 426, falling back to simple SMS");
                let message = format!("کد تایید شما: {persian_code}");
                let request = SmsRequest {
                    receptor: phone.to_string(),
                    message,
                    sender: None,
                    template: None,
                    token: None,
                };
                self.client.send_simple_sms(&request).await?
            }
            Err(SmsError::Api { status: 426, .. }) => {
                LoggerService::info("Lookup SMS failed with 426, falling back to simple SMS");
                let message = format!("کد تایید شما: {persian_code}");
                let request = SmsRequest {
                    receptor: phone.to_string(),
                    message,
                    sender: None,
                    template: None,
                    token: None,
                };
                self.client.send_simple_sms(&request).await?
            }
            Err(e) => return Err(e),
        };

        Ok((otp_code, response))
    }

    #[allow(dead_code)]
    pub async fn send_custom_sms(&self, phone: &str, message: &str, sender: Option<&str>) -> SmsResult<SmsResponse> {
        let persian_message = self.convert_numbers_to_persian(message)?;

        let request = SmsRequest {
            receptor: phone.to_string(),
            message: persian_message,
            sender: sender.map(|s| s.to_string()),
            template: None,
            token: None,
        };

        LoggerService::info(&format!("Sending custom SMS to {phone}"));

        self.client.send_simple_sms(&request).await
    }

    #[allow(dead_code)]
    pub async fn send_template_sms(&self, phone: &str, template: &str, token: &str) -> SmsResult<SmsResponse> {
        let persian_token = self.convert_numbers_to_persian(token)?;

        LoggerService::info(&format!("Sending template SMS to {phone} with template: {template}"));

        self.client
            .send_lookup_sms(phone, template, &persian_token)
            .await
    }

    fn generate_otp_code(&self) -> String {
        let mut rng = rand::rng();
        let code: u32 = rng.random_range(100000..999999);
        code.to_string()
    }

    fn convert_to_persian(&self, text: &str) -> SmsResult<String> {
        let english_digits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
        let persian_digits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];

        let mut result = String::new();

        for ch in text.chars() {
            if let Some(index) = english_digits.iter().position(|&x| x == ch) {
                result.push(persian_digits[index]);
            } else {
                result.push(ch);
            }
        }

        LoggerService::debug(&format!("Converted '{text}' to Persian: '{result}'"));
        Ok(result)
    }

    #[allow(dead_code)]
    fn convert_numbers_to_persian(&self, text: &str) -> SmsResult<String> {
        self.convert_to_persian(text)
    }

    pub fn validate_phone_number(&self, phone: &str) -> bool {
        let clean_phone = phone.trim().replace(" ", "").replace("-", "");

        if clean_phone.starts_with("09") && clean_phone.len() == 11 {
            return clean_phone.chars().all(|c| c.is_ascii_digit());
        }

        if clean_phone.starts_with("+989") && clean_phone.len() == 13 {
            return clean_phone[1..].chars().all(|c| c.is_ascii_digit());
        }

        if clean_phone.starts_with("00989") && clean_phone.len() == 14 {
            return clean_phone[2..].chars().all(|c| c.is_ascii_digit());
        }

        false
    }

    pub fn normalize_phone_number(&self, phone: &str) -> String {
        let clean_phone = phone.trim().replace(" ", "").replace("-", "");

        if clean_phone.starts_with("09") {
            return clean_phone;
        } else if let Some(stripped) = clean_phone.strip_prefix("+989") {
            return format!("0{stripped}");
        } else if let Some(stripped) = clean_phone.strip_prefix("00989") {
            return format!("0{stripped}");
        }

        clean_phone
    }
}
