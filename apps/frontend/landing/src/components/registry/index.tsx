import dynamic from 'next/dynamic';
import { LoadingPlaceholder } from '../shared/atoms/LoadingPlaceholder';

export const Components = {
  HeroSection: dynamic(() => import('../landing/molecules/Hero/HeroSection'), {
    ssr: true,
    loading: () => <LoadingPlaceholder height="min-h-[500px]" />,
  }),
  FeaturesSection: dynamic(
    () => import('../landing/molecules/FeaturesSection'),
    {
      ssr: true,
      loading: () => <LoadingPlaceholder height="min-h-[500px]" />,
    },
  ),
  ProductsSection: dynamic(() => import('../shop/molecules/ProductsSection'), {
    ssr: true,
    loading: () => <LoadingPlaceholder height="min-h-[500px]" />,
  }),
  ProductsOverviewSection: dynamic(
    () => import('../landing/molecules/ProductsOverviewSection'),
    {
      ssr: true,
      loading: () => <LoadingPlaceholder height="min-h-[400px]" />,
    },
  ),
  PricingSection: dynamic(() => import('../landing/molecules/PricingSection'), {
    ssr: true,
    loading: () => <LoadingPlaceholder height="min-h-[500px]" />,
  }),
  Footer: dynamic(() => import('../shared/molecules/Footer'), {
    ssr: true,
    loading: () => <LoadingPlaceholder height="min-h-[500px]" />,
  }),
  LandingPage: dynamic(() => import('../landing/organisms/LandingPage'), {
    ssr: true,
    loading: () => <LoadingPlaceholder height="min-h-[600px]" />,
  }),
  ShopPage: dynamic(() => import('../shop/organisms/ShopPage'), {
    ssr: true,
    loading: () => <LoadingPlaceholder height="min-h-[600px]" />,
  }),
  BlogPage: dynamic(() => import('../blog/organisms/BlogPage'), {
    ssr: true,
    loading: () => <LoadingPlaceholder height="min-h-[600px]" />,
  }),
  BlogPostPage: dynamic(() => import('../blog/organisms/BlogPostPage'), {
    ssr: true,
    loading: () => <LoadingPlaceholder height="min-h-[600px]" />,
  }),
  LoginPage: dynamic(() => import('../auth/organisms/LoginPage'), {
    ssr: true,
    loading: () => <LoadingPlaceholder height="min-h-[600px]" />,
  }),
  AuthModal: dynamic(() => import('../auth/organisms/AuthModal'), {
    loading: () => <LoadingPlaceholder height="min-h-[200px]" />,
  }),
  AuthErrorBoundary: dynamic(
    () =>
      import('../auth/molecules/auth-error-boundary').then((mod) => ({
        default: mod.AuthErrorBoundary,
      })),
    {
      ssr: true,
      loading: () => <LoadingPlaceholder height="min-h-[100px]" />,
    },
  ),
  BackButton: dynamic(() => import('../shared/atoms/BackButton'), {
    ssr: true,
    loading: () => <LoadingPlaceholder height="min-h-[50px]" />,
  }),
  LogoutButton: dynamic(
    () =>
      import('../auth/atoms/logout-button').then((mod) => ({
        default: mod.LogoutButton,
      })),
    {
      ssr: true,
      loading: () => <LoadingPlaceholder height="min-h-[50px]" />,
    },
  ),
  Header: dynamic(() => import('../shared/molecules/Header'), {
    ssr: true,
    loading: () => <LoadingPlaceholder height="min-h-[60px]" />,
  }),
  HeaderNavigation: dynamic(
    () => import('../shared/molecules/HeaderNavigation'),
    {
      ssr: true,
      loading: () => <LoadingPlaceholder height="min-h-[60px]" />,
    },
  ),
  ProductCard: dynamic(() => import('../shop/atoms/ProductCard'), {
    ssr: true,
    loading: () => <LoadingPlaceholder height="min-h-[300px]" />,
  }),
  NavItem: dynamic(() => import('../shared/atoms/NavItem'), {
    ssr: true,
    loading: () => <LoadingPlaceholder height="min-h-[40px]" />,
  }),
  FooterLink: dynamic(() => import('../shared/atoms/FooterLink'), {
    ssr: true,
    loading: () => <LoadingPlaceholder height="min-h-[30px]" />,
  }),
  Logo: dynamic(() => import('../shared/atoms/Logo'), {
    ssr: true,
    loading: () => <LoadingPlaceholder height="min-h-[40px]" />,
  }),
  MobileMenuToggle: dynamic(
    () => import('../shared/molecules/MobileMenuToggle'),
    {
      ssr: true,
      loading: () => <LoadingPlaceholder height="min-h-[40px]" />,
    },
  ),
  Navigation: dynamic(() => import('../shared/molecules/Navigation'), {
    ssr: true,
    loading: () => <LoadingPlaceholder height="min-h-[50px]" />,
  }),
  FeatureItem: dynamic(() => import('../landing/atoms/FeatureItem'), {
    ssr: true,
    loading: () => <LoadingPlaceholder height="min-h-[200px]" />,
  }),
  ServiceCard: dynamic(() => import('../landing/atoms/ServiceCard'), {
    ssr: true,
    loading: () => <LoadingPlaceholder height="min-h-[250px]" />,
  }),
  ServicesPage: dynamic(() => import('../services/organisms/ServicesPage'), {
    ssr: true,
    loading: () => <LoadingPlaceholder height="min-h-[600px]" />,
  }),
  ServicesHero: dynamic(() => import('../services/molecules/ServicesHero'), {
    ssr: true,
    loading: () => <LoadingPlaceholder height="min-h-[500px]" />,
  }),
  ServicesCategories: dynamic(
    () => import('../services/molecules/ServicesCategories'),
    {
      ssr: true,
      loading: () => <LoadingPlaceholder height="min-h-[400px]" />,
    },
  ),
  ServicesDetail: dynamic(
    () => import('../services/molecules/ServicesDetail'),
    {
      ssr: true,
      loading: () => <LoadingPlaceholder height="min-h-[800px]" />,
    },
  ),
  ServiceDetailCard: dynamic(
    () => import('../services/atoms/ServiceDetailCard'),
    {
      ssr: true,
      loading: () => <LoadingPlaceholder height="min-h-[350px]" />,
    },
  ),
  ServiceCategoryCard: dynamic(
    () => import('../services/atoms/ServiceCategoryCard'),
    {
      ssr: true,
      loading: () => <LoadingPlaceholder height="min-h-[200px]" />,
    },
  ),
  ContactPage: dynamic(() => import('../contact/organisms/ContactPage'), {
    ssr: true,
    loading: () => <LoadingPlaceholder height="min-h-[600px]" />,
  }),
};
