use serde::{Deserialize, Serialize};
use utoipa::ToSchema;
use crate::modules::users::dto::UserResponseDto;

#[derive(Debug, Deserialize, Serialize, ToSchema)]
pub struct AuthResponseDto {
    #[schema(example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")]
    pub access_token: String,
    #[schema(example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")]
    pub refresh_token: String,
    #[schema(example = "Bearer")]
    pub token_type: String,
    #[schema(example = 3600)]
    pub expires_in: u64,
    pub user: UserResponseDto,
}
