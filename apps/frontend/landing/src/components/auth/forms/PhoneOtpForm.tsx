'use client';

import { <PERSON><PERSON>, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from '@/components/ui/input-otp';
import { useAuth } from '@/hooks/auth/use-auth';
import { phoneLogin, sendOtp, verifyOtp } from '@/lib/auth/auth';
import { TokenManager } from '@/lib/auth/token-manager';
import {
  OtpFormData,
  PhoneFormData,
  otpSchema as validationOtpSchema,
  phoneSchema as validationPhoneSchema,
} from '@/lib/validations/auth';
import { zodResolver } from '@hookform/resolvers/zod';
import { AlertCircle, Smartphone } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { RoleSelector } from '../atoms/role-selector';

interface PhoneOtpFormProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export function PhoneOtpForm({ onSuccess, onError }: PhoneOtpFormProps) {
  const [step, setStep] = useState<'phone' | 'otp' | 'login'>('phone');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [selectedRole, setSelectedRole] = useState<string>('buyer');
  const { refreshUser } = useAuth();

  const phoneForm = useForm<PhoneFormData>({
    resolver: zodResolver(validationPhoneSchema),
    defaultValues: {
      phone: '',
    },
  });

  const otpForm = useForm<OtpFormData>({
    resolver: zodResolver(validationOtpSchema),
    defaultValues: {
      phone: '',
      otp_code: '',
    },
  });

  const handleSendOtp = async (data: PhoneFormData) => {
    try {
      setIsLoading(true);
      setError(null);

      await sendOtp({ phone: data.phone });
      setPhoneNumber(data.phone);
      otpForm.setValue('phone', data.phone);
      setStep('otp');
    } catch (error) {
      console.error('Send OTP error:', error);
      const errorMessage =
        error instanceof Error ? error.message : 'ارسال کد تأیید ناموفق بود';
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyOtp = async (data: OtpFormData) => {
    try {
      setIsLoading(true);
      setError(null);

      await verifyOtp({ phone: data.phone, otp_code: data.otp_code });
      setStep('login');
    } catch (error) {
      console.error('Verify OTP error:', error);
      const errorMessage =
        error instanceof Error ? error.message : 'کد تأیید نامعتبر است';
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePhoneLogin = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const otpCode = otpForm.getValues('otp_code');
      const response = await phoneLogin({
        phone: phoneNumber,
        otp_code: otpCode,
        role: selectedRole,
      });

      TokenManager.setTokens(response.access_token, response.refresh_token);
      TokenManager.setUser(response.user);

      await refreshUser();

      onSuccess?.();
    } catch (error) {
      console.error('Phone login error:', error);
      const errorMessage =
        error instanceof Error ? error.message : 'ورود ناموفق بود';
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setStep('phone');
    setError(null);
    setPhoneNumber('');
    phoneForm.reset();
    otpForm.reset();
  };

  if (step === 'phone') {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Smartphone className="w-5 h-5" />
            احراز هویت با تلفن
          </CardTitle>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Form {...phoneForm}>
            <form
              onSubmit={phoneForm.handleSubmit(handleSendOtp)}
              className="space-y-4"
              noValidate
            >
              <FormField
                control={phoneForm.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>شماره تلفن</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="09123456789"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <RoleSelector
                value={selectedRole}
                onValueChange={setSelectedRole}
              />

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? 'در حال ارسال...' : 'ارسال کد تأیید'}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    );
  }

  if (step === 'otp') {
    return (
      <Card>
        <CardHeader>
          <CardTitle>تأیید کد</CardTitle>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="text-sm text-muted-foreground mb-4">
            کد تأیید ارسال شده به {phoneNumber} را وارد کنید
          </div>

          <Form {...otpForm}>
            <form
              onSubmit={otpForm.handleSubmit(handleVerifyOtp)}
              className="space-y-4"
              noValidate
            >
              <FormField
                control={otpForm.control}
                name="otp_code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>کد تأیید</FormLabel>
                    <FormControl>
                      <InputOTP maxLength={6} {...field}>
                        <InputOTPGroup>
                          <InputOTPSlot index={0} />
                          <InputOTPSlot index={1} />
                          <InputOTPSlot index={2} />
                          <InputOTPSlot index={3} />
                          <InputOTPSlot index={4} />
                          <InputOTPSlot index={5} />
                        </InputOTPGroup>
                      </InputOTP>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={resetForm}
                  disabled={isLoading}
                  className="flex-1"
                >
                  بازگشت
                </Button>
                <Button type="submit" disabled={isLoading} className="flex-1">
                  {isLoading ? 'در حال تأیید...' : 'تأیید کد'}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>تکمیل ورود</CardTitle>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="text-sm text-muted-foreground mb-4">
          کد تأیید با موفقیت تأیید شد! برای تکمیل ورود کلیک کنید.
        </div>

        <Button
          onClick={handlePhoneLogin}
          disabled={isLoading}
          className="w-full"
        >
          {isLoading ? 'در حال ورود...' : 'تکمیل ورود'}
        </Button>
      </CardContent>
    </Card>
  );
}
