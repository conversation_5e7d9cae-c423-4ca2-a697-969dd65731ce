import { Components } from '@/components/registry';
import React from 'react';

interface FooterProps {
  className?: string;
}

const Footer: React.FC<FooterProps> = ({ className }) => {
  const services = [
    { href: '/shop', text: 'داشبوردهای مدیریتی' },
    { href: '/shop', text: 'طراحی سایت و اپلیکیشن' },
  ];

  const usefulLinks = [
    { href: '/contact', text: 'تماس با ما' },
    { href: '/blog', text: 'وبلاگ' },
  ];

  const contactInfo = ['تلفن: ۰۹۱۱۲۱۹۱۷۴۶', 'ایمیل: <EMAIL>'];
  //"آدرس: تهران، خیابان ولیعصر",
  return (
    <footer
      className={`bg-gradient-to-br from-background via-card to-secondary text-foreground pt-4 pb-2 relative overflow-hidden border-t border-primary/20 ${className || ''}`}
    >
      {/* Matrix rain background */}
      <div className="absolute inset-0 overflow-hidden">
        {Array.from({ length: 10 }).map((_, i) => (
          <div
            key={i}
            className="absolute text-primary/10 font-mono text-xs matrix-rain"
            style={{
              left: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 8}s`,
              animationDuration: `${8 + Math.random() * 4}s`,
            }}
          >
            {Math.random().toString(36).substring(2, 8)}
          </div>
        ))}
      </div>

      {/* Top border line - subtle */}
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary via-accent to-primary"></div>
      <div className="absolute -top-40 -right-40 w-[clamp(12rem,25vw,20rem)] h-[clamp(12rem,25vw,20rem)] rounded-full bg-primary/5 mix-blend-screen filter blur-3xl opacity-30"></div>
      <div className="absolute -bottom-40 -left-40 w-[clamp(12rem,25vw,20rem)] h-[clamp(12rem,25vw,20rem)] rounded-full bg-accent/5 mix-blend-screen filter blur-3xl opacity-30"></div>

      <div className=" relative z-10 mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 md:gap-8 lg:gap-10 mb-2 md:mb-3 lg:mb-4">
          <div>
            <div className="flex items-center mb-2">
              <div className="bg-gradient-to-r from-primary to-accent text-lg font-bold px-3 py-1 rounded-lg shadow-sm font-mono text-white!">
                {'{ دلفک }'}
              </div>
            </div>
            <div className=" mb-3 leading-relaxed font-mono bg-card/30 border border-primary/10 rounded-lg p-2">
              <div className="text-accent text-xs mb-1">// About us</div>
              <div className="text-sm">
                ارائه‌دهنده راهکارهای هوشمند برنامه‌نویسی برای کسب‌وکارها و
                توسعه‌دهندگان با بیش از ۵ سال تجربه در صنعت IT
              </div>
            </div>
            <div className="mb-3">
              <div
                dangerouslySetInnerHTML={{
                  __html: `<a referrerpolicy='origin' target='_blank' href='https://trustseal.enamad.ir/?id=597827&Code=fnQFjRmCTS704Yu0PE6BzZC22txuQtPY'><img referrerpolicy='origin' src='https://trustseal.enamad.ir/logo.aspx?id=597827&Code=fnQFjRmCTS704Yu0PE6BzZC22txuQtPY' alt='' style='cursor:pointer' code='fnQFjRmCTS704Yu0PE6BzZC22txuQtPY'></a>`,
                }}
              />
            </div>
            <div className="flex flex-wrap gap-2">
              <a
                href="#"
                aria-label="Website"
                className="p-1 min-w-[36px] min-h-[36px] rounded-lg bg-card/50 hover:bg-primary/20 flex items-center justify-center transition-all duration-300 border border-primary/20 shadow-sm hover:scale-110"
              >
                <span className="text-sm">🌐</span>
              </a>
              <a
                href="#"
                aria-label="Phone"
                className="p-1 min-w-[36px] min-h-[36px] rounded-lg bg-card/50 hover:bg-primary/20 flex items-center justify-center transition-all duration-300 border border-primary/20 shadow-sm hover:scale-110"
              >
                <span className="text-sm">📱</span>
              </a>
              <a
                href="#"
                aria-label="Email"
                className="p-1 min-w-[36px] min-h-[36px] rounded-lg bg-card/50 hover:bg-primary/20 flex items-center justify-center transition-all duration-300 border border-primary/20 shadow-sm hover:scale-110"
              >
                <span className="text-sm">📧</span>
              </a>
            </div>
          </div>
          <div>
            <h3 className="text-lg font-bold text-primary mb-2 relative inline-block font-mono">
              <span className="relative z-10">
                {'<'} خدمات {'/>'}
              </span>
            </h3>
            <ul className="space-y-2">
              {services.map((link, index) => (
                <Components.FooterLink key={index} href={link.href}>
                  {link.text}
                </Components.FooterLink>
              ))}
            </ul>
          </div>
          <div>
            <h3 className="text-lg font-bold text-primary mb-2 relative inline-block font-mono">
              <span className="relative z-10">
                {'['} لینک‌های مفید {']'}
              </span>
            </h3>
            <ul className="space-y-2">
              {usefulLinks.map((link, index) => (
                <Components.FooterLink key={index} href={link.href}>
                  {link.text}
                </Components.FooterLink>
              ))}
            </ul>
          </div>
          <div>
            <h3 className="text-lg font-bold text-primary mb-2 relative inline-block font-mono">
              <span className="relative z-10">
                {'('} تماس با ما {')'}
              </span>
            </h3>
            <ul className="space-y-3">
              {contactInfo.map((info, index) => (
                <li key={index} className="flex items-start gap-2 font-mono">
                  <span className="text-primary mt-1 text-xs">{'>'}</span>
                  <span className="tbg-card/20 border border-primary/10 px-2 py-1 rounded text-xs">
                    {info}
                  </span>
                </li>
              ))}
            </ul>
          </div>
        </div>
        <div className="pt-2 border-t border-primary/20 text-center text-muted-foreground font-mono">
          <div className="bg-card/30 border border-primary/10 rounded-lg p-2 inline-block">
            <span className="text-accent text-sm">console.log(</span>
            <span className="text-foreground text-sm">
              "© {new Date().getFullYear()} دلفک. تمامی حقوق محفوظ است."
            </span>
            <span className="text-accent text-sm">);</span>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
