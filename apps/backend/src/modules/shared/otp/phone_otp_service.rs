use chrono::{Duration, Utc};
use crate::common::{CommonModule, errors::{AppError, CustomErrors}, sms::SmsService};
use crate::modules::shared::otp::{PhoneOtpRepository, NewPhoneOtpEntity, PhoneOtpEntity};

const OTP_EXPIRY_MINUTES: i64 = 10;
const MAX_OTP_ATTEMPTS: i32 = 3;

pub struct PhoneOtpService {
    phone_otp_repository: PhoneOtpRepository,
    sms_service: SmsService,
    common_module: CommonModule,
}

impl PhoneOtpService {
    pub fn new(common_module: &CommonModule) -> Self {
        let sms_service = SmsService::new(common_module.config.sms.kavenegar_api_key.clone());

        Self {
            phone_otp_repository: PhoneOtpRepository::new(common_module),
            sms_service,
            common_module: common_module.clone(),
        }
    }

    pub async fn send_otp(&self, phone: &str) -> Result<(), AppError> {
        let normalized_phone = self.sms_service.normalize_phone_number(phone);

        if !self.sms_service.validate_phone_number(&normalized_phone) {
            return Err(CustomErrors::invalid_phone_number());
        }

        let (otp_code, _) = self.sms_service
            .send_otp_sms(&normalized_phone, Some(&self.common_module.config.sms.otp_template))
            .await
            .map_err(|e| AppError::InternalServer(format!("Failed to send SMS: {e}")))?;

        let expires_at = Utc::now() + Duration::minutes(OTP_EXPIRY_MINUTES);
        let new_otp = NewPhoneOtpEntity::new(normalized_phone, otp_code, expires_at);

        self.phone_otp_repository.create(&new_otp).await?;

        Ok(())
    }

    pub async fn verify_otp(&self, phone: &str, provided_code: &str) -> Result<PhoneOtpEntity, AppError> {
        let normalized_phone = self.sms_service.normalize_phone_number(phone);

        let otp_entry = self.phone_otp_repository
            .find_valid_by_phone_and_code(&normalized_phone, provided_code)
            .await?
            .ok_or_else(CustomErrors::invalid_otp)?;

        if otp_entry.verified.unwrap_or(false) {
            return Err(CustomErrors::otp_already_used());
        }

        if otp_entry.expires_at < Utc::now() {
            return Err(CustomErrors::otp_expired());
        }

        let current_attempts = otp_entry.attempts.unwrap_or(0);
        if current_attempts >= MAX_OTP_ATTEMPTS {
            return Err(CustomErrors::otp_too_many_attempts());
        }

        if otp_entry.otp_code != provided_code {
            self.phone_otp_repository.increment_attempts(otp_entry.id).await?;
            return Err(CustomErrors::invalid_otp());
        }

        let verified_otp = self.phone_otp_repository.mark_as_verified(otp_entry.id).await?;

        Ok(verified_otp)
    }

    #[allow(dead_code)]
    pub async fn is_phone_verified(&self, phone: &str) -> Result<bool, AppError> {
        let normalized_phone = self.sms_service.normalize_phone_number(phone);

        let latest_otp = self.phone_otp_repository
            .find_latest_by_phone(&normalized_phone)
            .await?;

        match latest_otp {
            Some(otp) => Ok(otp.verified.unwrap_or(false)),
            None => Ok(false),
        }
    }

    #[allow(dead_code)]
    pub async fn cleanup_expired_otps(&self) -> Result<usize, AppError> {
        self.phone_otp_repository.cleanup_expired().await
    }
}
