'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Project } from '@/lib/types/project';
import { formatCurrency, formatDate, getStatusColor, getStatusLabel } from '@/lib/utils/project';
import { Calendar, Clock, DollarSign, User } from 'lucide-react';

interface ProjectCardProps {
  project: Project;
  onViewDetails?: (projectId: string) => void;
  onEdit?: (projectId: string) => void;
  onApply?: (projectId: string) => void;
  showActions?: boolean;
  variant?: 'buyer' | 'coder' | 'public';
}

export function ProjectCard({
  project,
  onViewDetails,
  onEdit,
  onApply,
  showActions = true,
  variant = 'public'
}: ProjectCardProps) {
  const isExpired = new Date(project.deadline) < new Date();
  const daysLeft = Math.ceil((new Date(project.deadline).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));

  return (
    <Card className="h-full flex flex-col hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between gap-3">
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-lg leading-tight mb-2 line-clamp-2">
              {project.title}
            </h3>
            <div className="flex items-center gap-2 mb-2">
              <Badge variant="secondary" className="text-xs">
                {project.category}
              </Badge>
              <Badge 
                variant={getStatusColor(project.status)} 
                className="text-xs"
              >
                {getStatusLabel(project.status)}
              </Badge>
            </div>
          </div>
        </div>
        
        <p className="text-sm text-muted-foreground line-clamp-3">
          {project.description}
        </p>
      </CardHeader>

      <CardContent className="flex-1 pb-3">
        <div className="space-y-3">
          <div className="flex items-center gap-4 text-sm">
            <div className="flex items-center gap-1">
              <DollarSign className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">
                {formatCurrency(project.budget.min)} - {formatCurrency(project.budget.max)}
              </span>
            </div>
            <div className="flex items-center gap-1">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className={isExpired ? 'text-destructive' : ''}>
                {formatDate(project.deadline)}
              </span>
            </div>
          </div>

          {daysLeft > 0 && (
            <div className="flex items-center gap-1 text-sm">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span className={daysLeft <= 7 ? 'text-orange-600' : 'text-muted-foreground'}>
                {daysLeft} روز باقی‌مانده
              </span>
            </div>
          )}

          {variant !== 'buyer' && (
            <div className="flex items-center gap-1 text-sm">
              <User className="h-4 w-4 text-muted-foreground" />
              <span>{project.buyer.username}</span>
              <span className="text-muted-foreground">
                (امتیاز: {project.buyer.rating})
              </span>
            </div>
          )}

          <div className="flex flex-wrap gap-1">
            {project.skills.slice(0, 4).map((skill, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {skill}
              </Badge>
            ))}
            {project.skills.length > 4 && (
              <Badge variant="outline" className="text-xs">
                +{project.skills.length - 4}
              </Badge>
            )}
          </div>

          {project.proposals.length > 0 && variant === 'buyer' && (
            <div className="text-sm text-muted-foreground">
              {project.proposals.length} پیشنهاد دریافت شده
            </div>
          )}
        </div>
      </CardContent>

      {showActions && (
        <CardFooter className="pt-3">
          <div className="flex gap-2 w-full">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onViewDetails?.(project.id)}
              className="flex-1"
            >
              مشاهده جزئیات
            </Button>
            
            {variant === 'buyer' && (
              <Button
                variant="secondary"
                size="sm"
                onClick={() => onEdit?.(project.id)}
              >
                ویرایش
              </Button>
            )}
            
            {variant === 'coder' && project.status === 'published' && (
              <Button
                size="sm"
                onClick={() => onApply?.(project.id)}
                disabled={isExpired}
              >
                ارسال پیشنهاد
              </Button>
            )}
          </div>
        </CardFooter>
      )}
    </Card>
  );
}
