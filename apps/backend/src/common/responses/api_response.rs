use serde::{Deserialize, Serialize};
use utoipa::ToSchema;

/// Standard API response wrapper for all endpoints
#[derive(Debug, Serialize, Deserialize, ToSchema)]
pub struct ApiResponse<T> {
    /// Indicates whether the request was successful
    pub success: bool,
    /// The response data (present on success)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub data: Option<T>,
    /// Error message (present on failure)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub error: Option<String>,
    /// Request timestamp
    pub timestamp: chrono::DateTime<chrono::Utc>,
    /// Request ID for tracing
    #[serde(skip_serializing_if = "Option::is_none")]
    pub request_id: Option<String>,
}

impl<T> ApiResponse<T> {
    /// Create a successful response
    #[allow(dead_code)]
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
            timestamp: chrono::Utc::now(),
            request_id: None,
        }
    }

    /// Create a successful response with request ID
    #[allow(dead_code)]
    pub fn success_with_id(data: T, request_id: String) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
            timestamp: chrono::Utc::now(),
            request_id: Some(request_id),
        }
    }

    /// Create an error response
    #[allow(dead_code)]
    pub fn error(message: String) -> ApiResponse<()> {
        ApiResponse {
            success: false,
            data: None,
            error: Some(message),
            timestamp: chrono::Utc::now(),
            request_id: None,
        }
    }

    /// Create an error response with request ID
    #[allow(dead_code)]
    pub fn error_with_id(message: String, request_id: String) -> ApiResponse<()> {
        ApiResponse {
            success: false,
            data: None,
            error: Some(message),
            timestamp: chrono::Utc::now(),
            request_id: Some(request_id),
        }
    }
}

/// Pagination metadata for list responses
#[derive(Debug, Serialize, Deserialize, ToSchema)]
pub struct PaginationMeta {
    /// Current page number (1-based)
    pub page: u32,
    /// Number of items per page
    pub per_page: u32,
    /// Total number of items
    pub total: u64,
    /// Total number of pages
    pub total_pages: u32,
    /// Whether there is a next page
    pub has_next: bool,
    /// Whether there is a previous page
    pub has_prev: bool,
}

impl PaginationMeta {
    #[allow(dead_code)]
    pub fn new(page: u32, per_page: u32, total: u64) -> Self {
        let total_pages = ((total as f64) / (per_page as f64)).ceil() as u32;

        Self {
            page,
            per_page,
            total,
            total_pages,
            has_next: page < total_pages,
            has_prev: page > 1,
        }
    }
}

/// Paginated response wrapper
#[derive(Debug, Serialize, Deserialize, ToSchema)]
pub struct PaginatedResponse<T> {
    /// The list of items
    pub items: Vec<T>,
    /// Pagination metadata
    pub meta: PaginationMeta,
}

impl<T> PaginatedResponse<T> {
    #[allow(dead_code)]
    pub fn new(items: Vec<T>, page: u32, per_page: u32, total: u64) -> Self {
        Self {
            items,
            meta: PaginationMeta::new(page, per_page, total),
        }
    }
}

/// Standard query parameters for pagination
#[derive(Debug, Serialize, Deserialize, ToSchema)]
pub struct PaginationQuery {
    /// Page number (1-based, default: 1)
    #[serde(default = "default_page")]
    pub page: u32,
    /// Items per page (default: 20, max: 100)
    #[serde(default = "default_per_page")]
    pub per_page: u32,
}

fn default_page() -> u32 {
    1
}

fn default_per_page() -> u32 {
    20
}

impl Default for PaginationQuery {
    fn default() -> Self {
        Self {
            page: default_page(),
            per_page: default_per_page(),
        }
    }
}

impl PaginationQuery {
    /// Validate and normalize pagination parameters
    #[allow(dead_code)]
    pub fn validate(&mut self) {
        if self.page == 0 {
            self.page = 1;
        }
        if self.per_page == 0 {
            self.per_page = default_per_page();
        }
        if self.per_page > 100 {
            self.per_page = 100;
        }
    }

    /// Get the offset for database queries
    #[allow(dead_code)]
    pub fn offset(&self) -> u64 {
        ((self.page - 1) * self.per_page) as u64
    }

    /// Get the limit for database queries
    #[allow(dead_code)]
    pub fn limit(&self) -> u64 {
        self.per_page as u64
    }
}
