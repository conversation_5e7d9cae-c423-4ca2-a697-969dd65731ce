export interface AuthResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  user: User;
}

export interface User {
  id: string;
  email: string;
  username: string;
  created_at: string;
  updated_at: string;
  role: string;
}

export interface LoginPayload {
  email: string;
  password: string;
}

export interface RegisterPayload {
  email: string;
  username: string;
  password: string;
  confirm_password: string;
  role: string;
}

export interface SendOtpPayload {
  phone: string;
}

export interface VerifyOtpPayload {
  phone: string;
  otp_code: string;
}

export interface PhoneLoginPayload {
  phone: string;
  otp_code: string;
  role?: string;
}

export interface GoogleAuthUrlResponse {
  auth_url: string;
  state: string;
}

export interface GoogleAuthCallbackPayload {
  code: string;
  state?: string;
  role?: string;
}

export interface RefreshTokenPayload {
  refresh_token: string;
}
