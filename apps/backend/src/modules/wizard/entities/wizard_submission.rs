use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use uuid::Uuid;
use diesel::prelude::*;
use crate::schema::wizard_submissions;

#[derive(Debug, Clone, Serialize, Deserialize, Queryable, Selectable, Identifiable)]
#[diesel(table_name = wizard_submissions)]
pub struct WizardSubmission {
    pub id: Uuid,
    pub full_name: String,
    pub email: String,
    pub phone: String,
    pub project_type: String,
    pub description: String,
    pub status: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Insertable)]
#[diesel(table_name = wizard_submissions)]
pub struct NewWizardSubmission {
    pub id: Uuid,
    pub full_name: String,
    pub email: String,
    pub phone: String,
    pub project_type: String,
    pub description: String,
    pub status: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum WizardSubmissionStatus {
    Pending,
    InReview,
    Contacted,
    Completed,
    Rejected,
}

impl WizardSubmission {
    pub fn create_new(
        full_name: String,
        email: String,
        phone: String,
        project_type: String,
        description: String,
    ) -> NewWizardSubmission {
        let now = Utc::now();
        NewWizardSubmission {
            id: Uuid::new_v4(),
            full_name,
            email,
            phone,
            project_type,
            description,
            status: WizardSubmissionStatus::Pending.to_string(),
            created_at: now,
            updated_at: now,
        }
    }

    pub fn update_status(&mut self, status: WizardSubmissionStatus) {
        self.status = status.to_string();
        self.updated_at = Utc::now();
    }

    pub fn get_status(&self) -> WizardSubmissionStatus {
        parse_status(&self.status)
    }
}

impl std::fmt::Display for WizardSubmissionStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let status_str = match self {
            WizardSubmissionStatus::Pending => "pending",
            WizardSubmissionStatus::InReview => "in_review",
            WizardSubmissionStatus::Contacted => "contacted",
            WizardSubmissionStatus::Completed => "completed",
            WizardSubmissionStatus::Rejected => "rejected",
        };
        write!(f, "{status_str}")
    }
}

pub fn parse_status(status: &str) -> WizardSubmissionStatus {
    match status {
        "pending" => WizardSubmissionStatus::Pending,
        "in_review" => WizardSubmissionStatus::InReview,
        "contacted" => WizardSubmissionStatus::Contacted,
        "completed" => WizardSubmissionStatus::Completed,
        "rejected" => WizardSubmissionStatus::Rejected,
        _ => WizardSubmissionStatus::Pending,
    }
}
