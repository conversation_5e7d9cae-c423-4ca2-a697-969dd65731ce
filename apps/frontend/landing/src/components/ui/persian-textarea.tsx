'use client';

import * as React from 'react';
import { cn } from '@/lib/core/utils';
import { getTextDirection } from '@/lib/utils/persian';

export interface PersianTextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  persianOnly?: boolean;
  autoDirection?: boolean;
  showCharCount?: boolean;
  maxLength?: number;
}

const PersianTextarea = React.forwardRef<HTMLTextAreaElement, PersianTextareaProps>(
  ({ 
    className, 
    persianOnly = false, 
    autoDirection = true, 
    showCharCount = false,
    maxLength,
    onChange, 
    ...props 
  }, ref) => {
    const [direction, setDirection] = React.useState<'rtl' | 'ltr'>('rtl');
    const [charCount, setCharCount] = React.useState(0);

    const handleChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
      let value = event.target.value;

      // Auto-detect direction
      if (autoDirection) {
        const newDirection = getTextDirection(value) || 'rtl';
        setDirection(newDirection);
      }

      // Filter non-Persian characters if persianOnly is true
      if (persianOnly) {
        const persianRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s\n\r]/g;
        value = value.match(persianRegex)?.join('') || '';
        event.target.value = value;
      }

      // Update character count
      setCharCount(value.length);

      onChange?.(event);
    };

    React.useEffect(() => {
      if (props.value || props.defaultValue) {
        const initialValue = (props.value || props.defaultValue) as string;
        setCharCount(initialValue.length);
        if (autoDirection) {
          setDirection(getTextDirection(initialValue) || 'rtl');
        }
      }
    }, [props.value, props.defaultValue, autoDirection]);

    return (
      <div className="relative">
        <textarea
          className={cn(
            'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
            autoDirection && direction === 'rtl' && 'text-right',
            autoDirection && direction === 'ltr' && 'text-left',
            showCharCount && 'pb-8',
            className
          )}
          dir={autoDirection ? direction : 'rtl'}
          onChange={handleChange}
          maxLength={maxLength}
          ref={ref}
          {...props}
        />
        
        {(showCharCount || maxLength) && (
          <div className="absolute bottom-2 left-3 text-xs text-muted-foreground">
            {maxLength ? (
              <span className={charCount > maxLength * 0.9 ? 'text-orange-500' : ''}>
                {charCount} / {maxLength}
              </span>
            ) : (
              <span>{charCount} کاراکتر</span>
            )}
          </div>
        )}
      </div>
    );
  }
);
PersianTextarea.displayName = 'PersianTextarea';

export { PersianTextarea };
