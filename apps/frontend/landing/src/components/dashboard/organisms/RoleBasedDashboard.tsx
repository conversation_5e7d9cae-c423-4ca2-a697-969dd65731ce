'use client';

import { LoadingPlaceholder } from '@/components/shared/atoms/LoadingPlaceholder';
import { useDashboardAuth } from '@/hooks/auth/use-dashboard-auth';
import { DashboardLayout } from '../organisms/DashboardLayout';

interface RoleBasedDashboardProps {
  requiredRole: 'buyer' | 'coder';
}

export function RoleBasedDashboard({ requiredRole }: RoleBasedDashboardProps) {
  const { user, isLoading, isAuthenticated, isAuthorized } =
    useDashboardAuth(requiredRole);

  if (isLoading) {
    return <LoadingPlaceholder />;
  }

  if (!isAuthenticated || !isAuthorized || !user) {
    return null;
  }

  return <DashboardLayout user={user} />;
}
