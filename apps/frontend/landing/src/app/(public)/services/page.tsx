import { Components } from '@/components/registry';

export default function ServicesPage() {
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'Service',
    name: 'Dolfak Services',
    provider: {
      '@type': 'Organization',
      name: '<PERSON>lf<PERSON> Apps',
      url: 'https://dolfak.com',
    },
    description: 'خدمات جامع توسعه نرم‌افزار و راهکارهای فناوری اطلاعات',
    url: 'https://dolfak.com/services',
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <Components.ServicesPage />
    </>
  );
}
