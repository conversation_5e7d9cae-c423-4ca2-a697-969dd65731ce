# Dolfak Backend

A high-performance, modular backend application built with Rust and Actix-web, following NestJS-inspired architecture patterns.

## 🚀 Features

- **Modern Architecture**: Clean, modular design inspired by NestJS
- **High Performance**: Built with Rust and Actix-web for maximum throughput
- **Type Safety**: Comprehensive type safety with SQLx and strong typing
- **Authentication**: JWT-based authentication with secure password hashing
- **Database**: PostgreSQL with automated migrations and connection pooling
- **Caching**: Redis integration for optimal performance
- **File Uploads**: Support for local and MinIO (S3-compatible) storage
- **API Documentation**: Auto-generated Swagger/OpenAPI documentation
- **Health Monitoring**: Comprehensive health checks for production deployment
- **Error Handling**: Centralized error handling with detailed error responses
- **Input Validation**: Robust request validation with custom validators
- **Logging**: Structured logging with request/response tracking

## 🏗️ Architecture

```
src/
├── main.rs                 # Application entry point
├── main_module.rs          # Main application module
├── common/                 # Shared utilities and services
│   ├── config/            # Configuration management
│   ├── database/          # Database connection and migrations
│   ├── cache/             # Redis and memory caching
│   ├── events/            # Event bus system
│   ├── exceptions/        # Error handling
│   ├── interceptors/      # Middleware
│   ├── file_uploads/      # File handling
│   ├── logger/            # Logging utilities
│   ├── swagger/           # API documentation
│   └── validators/        # Input validation
└── modules/               # Feature modules
    ├── auth/              # Authentication
    ├── users/             # User management
    └── health/            # Health monitoring
```

## 🛠️ Quick Start

### Prerequisites

- Rust 1.75+
- PostgreSQL 12+
- Redis 6+
- Docker & Docker Compose (optional)

### Setup

1. **Clone and navigate to the project:**

   ```bash
   cd apps/backend
   ```

2. **Run the setup script:**

   ```bash
   chmod +x scripts/setup.sh
   ./scripts/setup.sh
   ```

3. **Start dependencies:**

   ```bash
   docker-compose up -d postgres redis minio
   ```

4. **Configure environment:**

   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. **Setup MinIO bucket:**

   ```bash
   ./scripts/create-minio-bucket.sh
   ```

6. **Run migrations:**

   ```bash
   ./scripts/migrate.sh
   ```

7. **Seed database (optional):**

   ```bash
   ./scripts/seed.sh
   ```

8. **Start the application:**
   ```bash
   cargo run
   ```

The API will be available at `http://localhost:8080`
MinIO Console will be available at `http://localhost:9001` (admin:minioadmin123)

## 📚 API Endpoints

### Authentication

- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `POST /api/auth/refresh` - Refresh access token
- `POST /api/auth/logout` - User logout

### Users

- `GET /api/users` - List users (authenticated)
- `POST /api/users` - Create user (authenticated)
- `GET /api/users/{id}` - Get user by ID (authenticated)
- `PUT /api/users/{id}` - Update user (authenticated)
- `DELETE /api/users/{id}` - Delete user (authenticated)
- `GET /api/users/profile` - Get current user profile (authenticated)

### Health

- `GET /api/health` - Application health
- `GET /api/health/ready` - Readiness probe
- `GET /api/health/live` - Liveness probe

## 🔧 Development

### Available Scripts

```bash
# Setup development environment
./scripts/setup.sh

# Database operations
./scripts/migrate.sh run        # Run migrations
./scripts/migrate.sh create     # Create new migration
./scripts/migrate.sh status     # Check migration status

# Database seeding
./scripts/seed.sh              # Seed with sample data
./scripts/seed.sh clean        # Remove sample data
```

### Testing

```bash
# Run all tests
cargo test

# Run with output
cargo test -- --nocapture

# Run integration tests
cargo test --test integration
```

### Code Quality

```bash
# Format code
cargo fmt

# Check linting
cargo clippy

# Check for security issues
cargo audit
```

## 🐳 Docker

### Development with Docker

```bash
# Start all services
docker-compose up

# Start only dependencies
docker-compose up -d postgres redis

# View logs
docker-compose logs -f backend
```

### Production Build

```bash
# Build image
docker build -t dolfak-backend .

# Run container
docker run -p 8080:8080 dolfak-backend
```

## 🔐 Authentication

The API uses JWT tokens for authentication. Include the token in requests:

```bash
curl -H "Authorization: Bearer <jwt-token>" http://localhost:8080/api/users/profile
```

### Sample Login

```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password123"}'
```

## 📊 Monitoring

### Health Checks

- **Health**: `GET /api/health` - General application status
- **Readiness**: `GET /api/health/ready` - Database and Redis connectivity
- **Liveness**: `GET /api/health/live` - Application responsiveness

### Logging

The application provides structured logging with:

- Request/response logging
- Error tracking with correlation IDs
- Performance metrics
- Security events

## 🌍 Environment Configuration

Key environment variables:

| Variable       | Description           | Default                  |
| -------------- | --------------------- | ------------------------ |
| `DATABASE_URL` | PostgreSQL connection | Required                 |
| `REDIS_URL`    | Redis connection      | `redis://localhost:6379` |
| `JWT_SECRET`   | JWT signing secret    | Required                 |
| `SERVER_PORT`  | HTTP server port      | `8080`                   |
| `LOG_LEVEL`    | Logging verbosity     | `info`                   |

See `.env.example` for complete configuration options.

## 🚀 Production Deployment

### Security Checklist

- [ ] Change default JWT secret
- [ ] Use strong database passwords
- [ ] Enable HTTPS/TLS
- [ ] Configure CORS origins
- [ ] Set up monitoring and alerting
- [ ] Configure log aggregation

### Performance Tuning

- Adjust database connection pool size
- Configure Redis memory limits
- Tune worker thread counts
- Enable response compression
- Set up CDN for static assets

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

- **Documentation**: See `docs/` directory
- **Issues**: GitHub Issues
- **API Documentation**: Available at `/swagger-ui` when running

---

Built with ❤️ using Rust and Actix-web
