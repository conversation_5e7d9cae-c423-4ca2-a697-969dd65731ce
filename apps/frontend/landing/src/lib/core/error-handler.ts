import { toast } from 'sonner';
import { ErrorTranslator } from './error-translator';

export interface ApiError {
  error?: string;
  message?: string;
}

export interface AxiosErrorResponse {
  response?: {
    data?: ApiError;
    status?: number;
  };
  message?: string;
  code?: string;
}

export class ErrorHandler {
  public static handleApiError(error: AxiosErrorResponse): string {
    if (!error.response) {
      return ErrorTranslator.translateNetworkError(error);
    }

    return ErrorTranslator.translate(
      error.response.data || {},
      error.response.status,
    );
  }

  public static showErrorToast(error: AxiosErrorResponse): void {
    const errorMessage = this.handleApiError(error);
    toast.error(errorMessage);
  }

  public static showErrorMessage(
    message: string | ApiError,
    statusCode?: number,
  ): void {
    let errorMessage: string;

    if (typeof message === 'string') {
      errorMessage = message;
    } else {
      errorMessage = ErrorTranslator.translate(message, statusCode);
    }

    toast.error(errorMessage);
  }

  public static getErrorMessage(error: AxiosErrorResponse): string {
    return this.handleApiError(error);
  }
}
