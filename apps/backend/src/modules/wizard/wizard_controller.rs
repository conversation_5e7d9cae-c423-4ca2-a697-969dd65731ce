use actix_web::{web, HttpResponse, Result, ResponseError};
use uuid::Uuid;
use crate::common::{CommonModule, utils::pagination::{PaginationParams, PaginatedResponse}, logger::LoggerService};
use super::{
    wizard_service::WizardService,
    dto::{CreateWizardSubmissionDto, WizardSubmissionResponseDto},
    entities::WizardSubmissionStatus
};
use crate::common::validators::DtoValidator;

pub struct WizardController {
    wizard_service: WizardService,
}

#[utoipa::path(
    post,
    path = "/api/wizard/submit",
    request_body = CreateWizardSubmissionDto,
    responses(
        (status = 201, description = "Wizard submission created successfully", body = WizardSubmissionResponseDto),
        (status = 400, description = "Invalid input data", body = crate::common::errors::ErrorResponse),
        (status = 500, description = "Internal server error", body = crate::common::errors::ErrorResponse)
    ),
    tag = "wizard"
)]
pub async fn submit_wizard(
    wizard_controller: web::Data<WizardController>,
    req: web::Json<CreateWizardSubmissionDto>,
) -> Result<HttpResponse> {
    let dto = req.into_inner();

    if let Err(validation_errors) = dto.validate_dto() {
        return Ok(HttpResponse::BadRequest().json(validation_errors.to_string()));
    }

    match wizard_controller.wizard_service.submit_wizard(dto).await {
        Ok(submission) => Ok(HttpResponse::Created().json(submission)),
        Err(e) => {
            let error_msg = e.to_string();
            LoggerService::log_error(&format!("Failed to submit wizard: {error_msg}"), Some("WizardController"));
            Ok(e.error_response())
        },
    }
}

#[utoipa::path(
    get,
    path = "/api/wizard/submissions",
    params(
        ("page" = Option<i64>, Query, description = "Page number (starts from 1)", example = 1),
        ("limit" = Option<i64>, Query, description = "Number of items per page (1-100)", example = 10),
    ),
    responses(
        (status = 200, description = "Paginated list of wizard submissions", body = PaginatedResponse<WizardSubmissionResponseDto>),
        (status = 500, description = "Internal server error", body = crate::common::errors::ErrorResponse)
    ),
    tag = "wizard"
)]
pub async fn get_all_submissions(
    wizard_controller: web::Data<WizardController>,
    query: web::Query<PaginationParams>,
) -> Result<HttpResponse> {
    match wizard_controller.wizard_service.find_paginated(&query).await {
        Ok(paginated_submissions) => Ok(HttpResponse::Ok().json(paginated_submissions)),
        Err(e) => {
            let error_msg = e.to_string();
            LoggerService::log_error(&format!("Failed to fetch wizard submissions: {error_msg}"), Some("WizardController"));
            Ok(e.error_response())
        },
    }
}

#[utoipa::path(
    get,
    path = "/api/wizard/submissions/{id}",
    params(
        ("id" = String, Path, description = "Submission ID")
    ),
    responses(
        (status = 200, description = "Submission found", body = WizardSubmissionResponseDto),
        (status = 404, description = "Submission not found", body = crate::common::errors::ErrorResponse),
        (status = 400, description = "Invalid UUID format", body = crate::common::errors::ErrorResponse)
    ),
    tag = "wizard"
)]
pub async fn get_submission_by_id(
    wizard_controller: web::Data<WizardController>,
    path: web::Path<String>,
) -> Result<HttpResponse> {
    let id = Uuid::parse_str(&path.into_inner())
        .map_err(|_| actix_web::error::ErrorBadRequest("Invalid UUID format"))?;

    match wizard_controller.wizard_service.find_by_id(&id.to_string()).await {
        Ok(Some(submission)) => Ok(HttpResponse::Ok().json(submission)),
        Ok(None) => Ok(HttpResponse::NotFound().json(serde_json::json!({
            "error": "Submission not found"
        }))),
        Err(e) => {
            let error_msg = e.to_string();
            LoggerService::log_error(&format!("Failed to fetch wizard submission {id}: {error_msg}"), Some("WizardController"));
            Ok(e.error_response())
        },
    }
}

#[utoipa::path(
    patch,
    path = "/api/wizard/submissions/{id}/status",
    params(
        ("id" = String, Path, description = "Submission ID")
    ),
    request_body = UpdateStatusRequest,
    responses(
        (status = 200, description = "Status updated successfully", body = WizardSubmissionResponseDto),
        (status = 404, description = "Submission not found", body = crate::common::errors::ErrorResponse),
        (status = 400, description = "Invalid UUID format or status", body = crate::common::errors::ErrorResponse)
    ),
    tag = "wizard"
)]
pub async fn update_submission_status(
    wizard_controller: web::Data<WizardController>,
    path: web::Path<String>,
    req: web::Json<UpdateStatusRequest>,
) -> Result<HttpResponse> {
    let id = Uuid::parse_str(&path.into_inner())
        .map_err(|_| actix_web::error::ErrorBadRequest("Invalid UUID format"))?;

    let status = match req.status.as_str() {
        "pending" => WizardSubmissionStatus::Pending,
        "in_review" => WizardSubmissionStatus::InReview,
        "contacted" => WizardSubmissionStatus::Contacted,
        "completed" => WizardSubmissionStatus::Completed,
        "rejected" => WizardSubmissionStatus::Rejected,
        _ => return Ok(HttpResponse::BadRequest().json(serde_json::json!({
            "error": "Invalid status"
        }))),
    };

    match wizard_controller.wizard_service.update_status(&id.to_string(), status).await {
        Ok(submission) => Ok(HttpResponse::Ok().json(submission)),
        Err(e) => {
            let error_msg = e.to_string();
            LoggerService::log_error(&format!("Failed to update wizard submission {id} status: {error_msg}"), Some("WizardController"));
            Ok(e.error_response())
        },
    }
}

impl WizardController {
    pub fn new(common_module: &CommonModule) -> Self {
        Self {
            wizard_service: WizardService::new(common_module),
        }
    }
}

#[derive(serde::Deserialize, utoipa::ToSchema)]
pub struct UpdateStatusRequest {
    #[schema(example = "in_review")]
    pub status: String,
}
