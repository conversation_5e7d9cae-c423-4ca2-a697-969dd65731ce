'use client';

import { Button } from '@/components/ui/button';
import { cn } from '@/lib/core/utils';
import * as LucideIcons from 'lucide-react';

interface SidebarMenuItemProps {
  label: string;
  icon: string;
  isActive?: boolean;
  onClick: () => void;
  className?: string;
}

export function SidebarMenuItem({
  label,
  icon,
  isActive = false,
  onClick,
  className,
}: SidebarMenuItemProps) {
  const IconComponent = (LucideIcons as any)[icon] || LucideIcons.Square;

  return (
    <Button
      variant={isActive ? 'default' : 'ghost'}
      className={cn(
        'w-full justify-start gap-3 text-right font-normal',
        isActive
          ? 'bg-primary text-primary-foreground shadow-sm'
          : 'text-muted-foreground hover:text-foreground hover:bg-accent',
        className,
      )}
      onClick={onClick}
    >
      <IconComponent className="h-4 w-4" />
      <span>{label}</span>
    </Button>
  );
}
