import React from 'react';
import BlogCard from '../atoms/BlogCard';

interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  author: string;
  readTime: string;
  tags: string[];
  slug: string;
  date: string;
}

interface BlogGridProps {
  posts: BlogPost[];
  className?: string;
}

const BlogGrid: React.FC<BlogGridProps> = ({ posts, className }) => {
  return (
    <div
      className={`grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 ${className || ''}`}
    >
      {posts.map((post) => (
        <BlogCard
          key={post.id}
          title={post.title}
          excerpt={post.excerpt}
          author={post.author}
          readTime={post.readTime}
          tags={post.tags}
          slug={post.slug}
          date={post.date}
        />
      ))}
    </div>
  );
};

export default BlogGrid;
