use serde::{Deserialize, Serialize};
use validator::Validate;
use utoipa::ToSchema;

#[derive(Debug, Deserialize, Serialize, Validate, ToSchema)]
pub struct CreateWizardSubmissionDto {
    #[validate(length(min = 2, message = "Full name must be at least 2 characters"))]
    #[schema(example = "محمد احمدی")]
    pub full_name: String,

    #[validate(email(message = "Invalid email format"))]
    #[schema(example = "<EMAIL>")]
    pub email: String,

    #[validate(length(min = 10, max = 15, message = "Phone number must be between 10 and 15 characters"))]
    #[schema(example = "09123456789")]
    pub phone: String,

    #[validate(custom(function = "validate_project_type"))]
    #[schema(example = "webapp")]
    pub project_type: String,

    #[validate(length(min = 10, message = "Description must be at least 10 characters"))]
    #[schema(example = "توضیحات پروژه")]
    pub description: String,
}

fn validate_project_type(project_type: &str) -> Result<(), validator::ValidationError> {
    match project_type {
        "webapp" | "mobile" | "startup" => Ok(()),
        _ => {
            let mut error = validator::ValidationError::new("invalid_project_type");
            error.message = Some("Project type must be webapp, mobile, or startup".into());
            Err(error)
        }
    }
}
