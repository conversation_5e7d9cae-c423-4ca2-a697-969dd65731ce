interface Logger {
  log: (...args: any[]) => void;
  warn: (...args: any[]) => void;
  error: (...args: any[]) => void;
  debug: (...args: any[]) => void;
  info: (...args: any[]) => void;
}

const logger: Logger = {
  log: (...args: any[]) => process.env.NODE_ENV !== "production" && console.log(...args),
  warn: (...args: any[]) => process.env.NODE_ENV !== "production" && console.warn(...args),
  error: (...args: any[]) => process.env.NODE_ENV !== "production" && console.error(...args),
  debug: (...args: any[]) => process.env.NODE_ENV !== "production" && console.debug(...args),
  info: (...args: any[]) => process.env.NODE_ENV !== "production" && console.info(...args),
};

export default logger;
