import { atom } from 'jotai';

export type BuyerSection =
  | 'overview'
  | 'projects'
  | 'requests'
  | 'wallet'
  | 'messages'
  | 'settings'
  | 'create-project'
  | 'browse-developers'
  | 'project-details'
  | 'developer-profile';

export type CoderSection =
  | 'overview'
  | 'active-projects'
  | 'new-requests'
  | 'portfolio'
  | 'earnings'
  | 'messages'
  | 'settings'
  | 'profile-edit'
  | 'project-details'
  | 'proposal-create';

export type DashboardSection = BuyerSection | CoderSection;

export interface DashboardState {
  isLoading: boolean;
  currentSection: DashboardSection;
  sidebarCollapsed: boolean;
  selectedProjectId?: string;
  selectedUserId?: string;
  breadcrumbs: Array<{ label: string; section?: DashboardSection }>;
}

export const dashboardStateAtom = atom<DashboardState>({
  isLoading: false,
  currentSection: 'overview',
  sidebarCollapsed: false,
  breadcrumbs: [{ label: 'داشبورد' }],
});

export const currentSectionAtom = atom(
  (get) => get(dashboardStateAtom).currentSection,
  (get, set, section: DashboardSection) => {
    const currentState = get(dashboardStateAtom);
    set(dashboardStateAtom, {
      ...currentState,
      currentSection: section,
    });
  },
);

export const sidebarCollapsedAtom = atom(
  (get) => get(dashboardStateAtom).sidebarCollapsed,
  (get, set, collapsed: boolean) => {
    const currentState = get(dashboardStateAtom);
    set(dashboardStateAtom, {
      ...currentState,
      sidebarCollapsed: collapsed,
    });
  },
);

export const isLoadingAtom = atom(
  (get) => get(dashboardStateAtom).isLoading,
  (get, set, loading: boolean) => {
    const currentState = get(dashboardStateAtom);
    set(dashboardStateAtom, {
      ...currentState,
      isLoading: loading,
    });
  },
);

export const selectedProjectIdAtom = atom(
  (get) => get(dashboardStateAtom).selectedProjectId,
  (get, set, projectId: string | undefined) => {
    const currentState = get(dashboardStateAtom);
    set(dashboardStateAtom, {
      ...currentState,
      selectedProjectId: projectId,
    });
  },
);

export const selectedUserIdAtom = atom(
  (get) => get(dashboardStateAtom).selectedUserId,
  (get, set, userId: string | undefined) => {
    const currentState = get(dashboardStateAtom);
    set(dashboardStateAtom, {
      ...currentState,
      selectedUserId: userId,
    });
  },
);

export const breadcrumbsAtom = atom(
  (get) => get(dashboardStateAtom).breadcrumbs,
  (
    get,
    set,
    breadcrumbs: Array<{ label: string; section?: DashboardSection }>,
  ) => {
    const currentState = get(dashboardStateAtom);
    set(dashboardStateAtom, {
      ...currentState,
      breadcrumbs,
    });
  },
);
