import React from 'react';
import BlogSearch from '../atoms/BlogSearch';
import BlogTag from '../atoms/BlogTag';

interface BlogFiltersProps {
  availableTags: string[];
  selectedTags: string[];
  searchTerm: string;
  onTagToggle: (tag: string) => void;
  onSearchChange: (value: string) => void;
  className?: string;
}

const BlogFilters: React.FC<BlogFiltersProps> = ({
  availableTags,
  selectedTags,
  searchTerm,
  onTagToggle,
  onSearchChange,
  className,
}) => {
  return (
    <div className={`space-y-4 sm:space-y-6 ${className || ''}`}>
      <BlogSearch searchTerm={searchTerm} onSearchChange={onSearchChange} />

      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <span className="text-sm sm:text-base font-mono text-foreground">
            دسته‌بندی‌ها:
          </span>
          <div className="h-px bg-primary/20 flex-1"></div>
        </div>

        <div className="flex flex-wrap gap-2 sm:gap-3">
          {availableTags.map((tag) => (
            <BlogTag
              key={tag}
              tag={tag}
              isActive={selectedTags.includes(tag)}
              onClick={() => onTagToggle(tag)}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default BlogFilters;
