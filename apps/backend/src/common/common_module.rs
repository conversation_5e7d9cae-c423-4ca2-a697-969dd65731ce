use crate::common::{
    config::AppConfig,
    database::DatabaseConnection,
    cache::CacheService,
};

#[derive(Clone)]
#[allow(dead_code)]
pub struct CommonModule {
    pub config: AppConfig,
    pub database: DatabaseConnection,
    pub cache: CacheService,
}

impl CommonModule {
    pub async fn new() -> Self {
        let config = AppConfig::new();
        let database = DatabaseConnection::new(&config.database).await;
        let cache = CacheService::new(&config.redis).await;
        Self {
            config,
            database,
            cache,
        }
    }
}
