'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useAuth } from '@/hooks/auth/use-auth';
import { getCurrentUser } from '@/lib/dashboard/auth';
import { ChevronDown, Code2, LogOut, Settings, User } from 'lucide-react';
import { useRouter } from 'next/navigation';

export function DashboardDropdown() {
  const { isAuthenticated, logout } = useAuth();
  const user = getCurrentUser();
  const router = useRouter();

  if (!isAuthenticated || !user) return null;

  const handleNavigateToDashboard = () => {
    const dashboardPath = user.role === 'buyer' ? '/buyers' : '/coders';
    router.push(dashboardPath);
  };

  const getRoleLabel = (role: string) => {
    return role === 'buyer' ? 'خریدار' : 'توسعه‌دهنده';
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="gap-2 px-3">
          <Avatar className="h-6 w-6">
            <AvatarImage src={`https://avatar.vercel.sh/${user.username}`} />
            <AvatarFallback className="text-xs">
              {user.username.slice(0, 2).toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div className="text-right hidden sm:block">
            <p className="text-sm font-medium">{user.username}</p>
            <p className="text-xs text-muted-foreground">
              {getRoleLabel(user.role)}
            </p>
          </div>
          <ChevronDown className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuItem onClick={handleNavigateToDashboard}>
          {user.role === 'buyer' ? (
            <User className="ml-2 h-4 w-4" />
          ) : (
            <Code2 className="ml-2 h-4 w-4" />
          )}
          <span>داشبورد من</span>
        </DropdownMenuItem>
        <DropdownMenuItem>
          <Settings className="ml-2 h-4 w-4" />
          <span>تنظیمات</span>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => logout()}>
          <LogOut className="ml-2 h-4 w-4" />
          <span>خروج</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
