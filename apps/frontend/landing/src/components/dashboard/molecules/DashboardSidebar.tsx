'use client';

import { But<PERSON> } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { useAuth } from '@/hooks/auth/use-auth';
import { cn } from '@/lib/core/utils';
import { DashboardUser } from '@/lib/dashboard/auth';
import { getMenuItemsForRole } from '@/lib/dashboard/menu';
import { currentSectionAtom } from '@/lib/store/dashboard';
import { useAtom } from 'jotai';
import { LogOut } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { SidebarMenuItem } from '../atoms/SidebarMenuItem';

interface DashboardSidebarProps {
  user: DashboardUser;
  isCollapsed?: boolean;
  className?: string;
}

export function DashboardSidebar({
  user,
  isCollapsed = false,
  className,
}: DashboardSidebarProps) {
  const [currentSection, setCurrentSection] = useAtom(currentSectionAtom);
  const { logout } = useAuth();
  const menuItems = getMenuItemsForRole(user.role);
  const router = useRouter();
  const handleLogoClick = () => {
    router.push('/');
  };
  return (
    <div
      className={cn(
        'flex h-full flex-col border-r bg-muted/10',
        isCollapsed ? 'w-16' : 'w-64',
        className,
      )}
    >
      <div className="p-2">
        <div
          className="flex items-center gap-2 cursor-pointer hover:shadow-lg w-fit p-2 rounded-2xl"
          onClick={handleLogoClick}
        >
          <div className="hover:bg-primary/70 h-8 w-8 rounded-lg bg-primary text-primary-foreground flex items-center justify-center">
            <span className="font-bold text-sm">د</span>
          </div>
          {!isCollapsed && (
            <h2 className="text-lg font-semibold text-foreground">دلفک</h2>
          )}
        </div>
      </div>

      <Separator />

      <ScrollArea className="flex-1 p-4">
        <div className="space-y-2">
          {menuItems.map((item) => (
            <SidebarMenuItem
              key={item.section}
              label={isCollapsed ? '' : item.label}
              icon={item.icon}
              isActive={currentSection === item.section}
              onClick={() => setCurrentSection(item.section as any)}
            />
          ))}
        </div>
      </ScrollArea>

      <Separator />

      <div className="p-4">
        <Button
          variant="ghost"
          className={cn(
            'w-full justify-start gap-3 text-right text-muted-foreground hover:text-destructive hover:bg-destructive/10',
            isCollapsed && 'justify-center',
          )}
          onClick={() => logout()}
        >
          <LogOut className="h-4 w-4" />
          {!isCollapsed && <span>خروج</span>}
        </Button>
      </div>
    </div>
  );
}
