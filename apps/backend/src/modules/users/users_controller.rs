use actix_web::{web, HttpRequest, HttpResponse, Result, ResponseError};
use uuid::Uuid;
use crate::common::{CommonModule, utils::pagination::{PaginationParams, PaginatedResponse}, logger::LoggerService};
use crate::modules::auth::{auth_guard::AuthGuard, jwt_strategy::JwtStrategy};
use super::{users_service::UsersService, dto::{CreateUserDto, UpdateUserDto, UserResponseDto}};
use crate::common::validators::DtoValidator;

pub struct UsersController {
    users_service: UsersService,
    auth_guard: AuthGuard,
}

#[utoipa::path(
    get,
    path = "/api/users",
    params(
        ("page" = Option<i64>, Query, description = "Page number (starts from 1)", example = 1),
        ("limit" = Option<i64>, Query, description = "Number of items per page (1-100)", example = 10),
    ),
    responses(
        (status = 200, description = "Paginated list of users", body = PaginatedResponse<UserResponseDto>),
        (status = 401, description = "Unauthorized", body = crate::common::errors::ErrorResponse)
    ),
    security(
        ("bearer_auth" = [])
    ),
    tag = "users"
)]
pub async fn get_all(
    users_controller: web::Data<UsersController>,
    req: HttpRequest,
    query: web::Query<PaginationParams>,
) -> Result<HttpResponse> {
    let _user_id = users_controller.auth_guard.extract_user_id_from_request(&req)
        .map_err(actix_web::error::ErrorUnauthorized)?;

    match users_controller.users_service.find_paginated(&query).await {
        Ok(paginated_users) => Ok(HttpResponse::Ok().json(paginated_users)),
        Err(e) => {
            let error_msg = e.to_string();
            LoggerService::log_error(&format!("Failed to fetch users: {error_msg}"), Some("UsersController"));
            Ok(e.error_response())
        },
    }
}

#[utoipa::path(
    get,
    path = "/api/users/{id}",
    params(
        ("id" = String, Path, description = "User ID")
    ),
    responses(
        (status = 200, description = "User found", body = UserResponseDto),
        (status = 404, description = "User not found", body = crate::common::errors::ErrorResponse),
        (status = 401, description = "Unauthorized", body = crate::common::errors::ErrorResponse),
        (status = 400, description = "Invalid UUID format", body = crate::common::errors::ErrorResponse)
    ),
    security(
        ("bearer_auth" = [])
    ),
    tag = "users"
)]
pub async fn get_by_id(
    users_controller: web::Data<UsersController>,
    req: HttpRequest,
    path: web::Path<String>,
) -> Result<HttpResponse> {
    let _user_id = users_controller.auth_guard.extract_user_id_from_request(&req)
        .map_err(actix_web::error::ErrorUnauthorized)?;

    let id = Uuid::parse_str(&path.into_inner())
        .map_err(|_| actix_web::error::ErrorBadRequest("Invalid UUID format"))?;

    match users_controller.users_service.find_by_id(&id.to_string()).await {
        Ok(Some(user)) => Ok(HttpResponse::Ok().json(user)),
        Ok(None) => Ok(HttpResponse::NotFound().json(serde_json::json!({
            "error": "User not found"
        }))),
        Err(e) => {
            let error_msg = e.to_string();
            LoggerService::log_error(&format!("Failed to fetch user {id}: {error_msg}"), Some("UsersController"));
            Ok(e.error_response())
        },
    }
}

#[utoipa::path(
    post,
    path = "/api/users",
    request_body = CreateUserDto,
    responses(
        (status = 201, description = "User created successfully", body = UserResponseDto),
        (status = 400, description = "Bad request", body = crate::common::errors::ErrorResponse),
        (status = 401, description = "Unauthorized", body = crate::common::errors::ErrorResponse),
        (status = 422, description = "Validation error", body = crate::common::errors::ErrorResponse)
    ),
    security(
        ("bearer_auth" = [])
    ),
    tag = "users"
)]
pub async fn create(
    users_controller: web::Data<UsersController>,
    req: HttpRequest,
    create_dto: web::Json<CreateUserDto>,
) -> Result<HttpResponse> {
    let _user_id = users_controller.auth_guard.extract_user_id_from_request(&req)
        .map_err(actix_web::error::ErrorUnauthorized)?;

    create_dto.validate_dto().map_err(actix_web::error::ErrorBadRequest)?;

    match users_controller.users_service.create_user(&create_dto).await {
        Ok(user) => Ok(HttpResponse::Created().json(user)),
        Err(e) => {
            let error_msg = e.to_string();
            LoggerService::log_error(&format!("Failed to create user: {error_msg}"), Some("UsersController"));
            Ok(e.error_response())
        },
    }
}

#[utoipa::path(
    put,
    path = "/api/users/{id}",
    params(
        ("id" = String, Path, description = "User ID")
    ),
    request_body = UpdateUserDto,
    responses(
        (status = 200, description = "User updated successfully", body = UserResponseDto),
        (status = 404, description = "User not found", body = crate::common::errors::ErrorResponse),
        (status = 401, description = "Unauthorized", body = crate::common::errors::ErrorResponse),
        (status = 400, description = "Invalid UUID format", body = crate::common::errors::ErrorResponse),
        (status = 422, description = "Validation error", body = crate::common::errors::ErrorResponse)
    ),
    security(
        ("bearer_auth" = [])
    ),
    tag = "users"
)]
pub async fn update(
    users_controller: web::Data<UsersController>,
    req: HttpRequest,
    path: web::Path<String>,
    update_dto: web::Json<UpdateUserDto>,
) -> Result<HttpResponse> {
    let _user_id = users_controller.auth_guard.extract_user_id_from_request(&req)
        .map_err(actix_web::error::ErrorUnauthorized)?;

    update_dto.validate_dto().map_err(actix_web::error::ErrorBadRequest)?;

    let id = Uuid::parse_str(&path.into_inner())
        .map_err(|_| actix_web::error::ErrorBadRequest("Invalid UUID format"))?;

    match users_controller.users_service.update_user(&id.to_string(), &update_dto).await {
        Ok(user) => Ok(HttpResponse::Ok().json(user)),
        Err(e) => {
            let error_msg = e.to_string();
            LoggerService::log_error(&format!("Failed to update user {id}: {error_msg}"), Some("UsersController"));
            Ok(e.error_response())
        },
    }
}

#[utoipa::path(
    delete,
    path = "/api/users/{id}",
    params(
        ("id" = String, Path, description = "User ID")
    ),
    responses(
        (status = 204, description = "User deleted successfully"),
        (status = 404, description = "User not found", body = crate::common::errors::ErrorResponse),
        (status = 401, description = "Unauthorized", body = crate::common::errors::ErrorResponse),
        (status = 400, description = "Invalid UUID format", body = crate::common::errors::ErrorResponse)
    ),
    security(
        ("bearer_auth" = [])
    ),
    tag = "users"
)]
pub async fn delete(
    users_controller: web::Data<UsersController>,
    req: HttpRequest,
    path: web::Path<String>,
) -> Result<HttpResponse> {
    let _user_id = users_controller.auth_guard.extract_user_id_from_request(&req)
        .map_err(actix_web::error::ErrorUnauthorized)?;

    let id = Uuid::parse_str(&path.into_inner())
        .map_err(|_| actix_web::error::ErrorBadRequest("Invalid UUID format"))?;

    match users_controller.users_service.delete_user(&id.to_string()).await {
        Ok(_) => Ok(HttpResponse::NoContent().finish()),
        Err(e) => {
            let error_msg = e.to_string();
            LoggerService::log_error(&format!("Failed to delete user {id}: {error_msg}"), Some("UsersController"));
            Ok(e.error_response())
        },
    }
}

#[utoipa::path(
    get,
    path = "/api/users/me",
    responses(
        (status = 200, description = "Current user information", body = UserResponseDto),
        (status = 401, description = "Unauthorized", body = crate::common::errors::ErrorResponse),
        (status = 404, description = "User not found", body = crate::common::errors::ErrorResponse)
    ),
    security(
        ("bearer_auth" = [])
    ),
    tag = "users"
)]
pub async fn get_me(
    users_controller: web::Data<UsersController>,
    req: HttpRequest,
) -> Result<HttpResponse> {
    let user_id = users_controller.auth_guard.extract_user_id_from_request(&req)
        .map_err(actix_web::error::ErrorUnauthorized)?;

    match users_controller.users_service.find_by_id(&user_id).await {
        Ok(Some(user)) => Ok(HttpResponse::Ok().json(user)),
        Ok(None) => Ok(HttpResponse::NotFound().json(serde_json::json!({
            "error": "User not found"
        }))),
        Err(e) => {
            let error_msg = e.to_string();
            LoggerService::log_error(&format!("Failed to fetch current user {user_id}: {error_msg}"), Some("UsersController"));
            Ok(e.error_response())
        },
    }
}

#[utoipa::path(
    get,
    path = "/api/users/profile",
    responses(
        (status = 200, description = "User profile", body = UserResponseDto),
        (status = 404, description = "User not found", body = crate::common::errors::ErrorResponse),
        (status = 401, description = "Unauthorized", body = crate::common::errors::ErrorResponse)
    ),
    security(
        ("bearer_auth" = [])
    ),
    tag = "users"
)]
pub async fn get_profile(
    users_controller: web::Data<UsersController>,
    req: HttpRequest,
) -> Result<HttpResponse> {
    let user_id = users_controller.auth_guard.extract_user_id_from_request(&req)
        .map_err(actix_web::error::ErrorUnauthorized)?;

    match users_controller.users_service.find_by_id(&user_id).await {
        Ok(Some(user)) => Ok(HttpResponse::Ok().json(user)),
        Ok(None) => Ok(HttpResponse::NotFound().json(serde_json::json!({
            "error": "User not found"
        }))),
        Err(e) => {
            let error_msg = e.to_string();
            LoggerService::log_error(&format!("Failed to fetch profile for user {user_id}: {error_msg}"), Some("UsersController"));
            Ok(e.error_response())
        },
    }
}

impl UsersController {
    pub fn new(common_module: &CommonModule) -> Self {
        let jwt_strategy = JwtStrategy::new(&common_module.config.jwt);
        let auth_guard = AuthGuard::new(jwt_strategy);

        Self {
            users_service: UsersService::new(common_module),
            auth_guard,
        }
    }

    pub async fn get_all(
        users_controller: web::Data<UsersController>,
        req: HttpRequest,
        query: web::Query<PaginationParams>,
    ) -> Result<HttpResponse> {
        get_all(users_controller, req, query).await
    }

    pub async fn get_by_id(
        users_controller: web::Data<UsersController>,
        req: HttpRequest,
        path: web::Path<String>,
    ) -> Result<HttpResponse> {
        get_by_id(users_controller, req, path).await
    }

    pub async fn create(
        users_controller: web::Data<UsersController>,
        req: HttpRequest,
        create_dto: web::Json<CreateUserDto>,
    ) -> Result<HttpResponse> {
        create(users_controller, req, create_dto).await
    }

    pub async fn update(
        users_controller: web::Data<UsersController>,
        req: HttpRequest,
        path: web::Path<String>,
        update_dto: web::Json<UpdateUserDto>,
    ) -> Result<HttpResponse> {
        update(users_controller, req, path, update_dto).await
    }

    pub async fn delete(
        users_controller: web::Data<UsersController>,
        req: HttpRequest,
        path: web::Path<String>,
    ) -> Result<HttpResponse> {
        delete(users_controller, req, path).await
    }

    pub async fn get_me(
        users_controller: web::Data<UsersController>,
        req: HttpRequest,
    ) -> Result<HttpResponse> {
        get_me(users_controller, req).await
    }

    pub async fn get_profile(
        users_controller: web::Data<UsersController>,
        req: HttpRequest,
    ) -> Result<HttpResponse> {
        get_profile(users_controller, req).await
    }
}
