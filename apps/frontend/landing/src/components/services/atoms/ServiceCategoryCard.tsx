import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { LucideIcon } from 'lucide-react';
import React from 'react';

interface ServiceCategoryCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
  count: number;
  className?: string;
}

const ServiceCategoryCard: React.FC<ServiceCategoryCardProps> = ({
  icon: Icon,
  title,
  description,
  count,
  className,
}) => {
  return (
    <Card
      className={`group p-6 text-center transition-all duration-300 hover:shadow-md hover:-translate-y-1 border border-primary/20 hover:border-primary/40 bg-card/80 backdrop-blur-sm hover:scale-105 relative overflow-hidden ${className || ''}`}
    >
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-accent/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      <div className="absolute top-4 left-4">
        <span className="text-xs font-mono bg-accent/10 text-accent border border-accent/20 px-2 py-1 rounded-md">
          {count}+ خدمت
        </span>
      </div>

      <div className="relative z-10">
        <div className="flex justify-center mb-4">
          <div className="p-4 bg-gradient-to-br from-primary to-accent text-background rounded-xl w-16 h-16 flex items-center justify-center transform transition-transform group-hover:scale-110 shadow-sm">
            <Icon className="w-8 h-8" />
          </div>
        </div>

        <CardHeader className="pb-2">
          <h3 className="text-lg font-bold text-primary font-mono mb-2">
            {'{'} {title} {'}'}
          </h3>
        </CardHeader>

        <CardContent>
          <p className="text-sm text-muted-foreground leading-relaxed">
            {description}
          </p>
        </CardContent>
      </div>
    </Card>
  );
};

export default ServiceCategoryCard;
