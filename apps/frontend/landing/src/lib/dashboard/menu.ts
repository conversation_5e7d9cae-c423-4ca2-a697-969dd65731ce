export interface MenuItem {
  label: string;
  icon: string;
  section: string;
  href?: string;
  roles: Array<'buyer' | 'coder'>;
}

export const buyerMenuItems: MenuItem[] = [
  {
    label: 'داشبورد',
    icon: 'LayoutDashboard',
    section: 'overview',
    roles: ['buyer'],
  },
  {
    label: 'پروژه‌های من',
    icon: 'FolderOpen',
    section: 'projects',
    roles: ['buyer'],
  },
  {
    label: 'درخواست‌ها',
    icon: 'MessageSquare',
    section: 'requests',
    roles: ['buyer'],
  },
  {
    label: 'کیف پول',
    icon: 'Wallet',
    section: 'wallet',
    roles: ['buyer'],
  },
  {
    label: 'تنظیمات',
    icon: 'Settings',
    section: 'settings',
    roles: ['buyer'],
  },
];

export const coderMenuItems: MenuItem[] = [
  {
    label: 'داشبورد',
    icon: 'LayoutDashboard',
    section: 'overview',
    roles: ['coder'],
  },
  {
    label: 'پروژه‌های فعال',
    icon: 'Code',
    section: 'active-projects',
    roles: ['coder'],
  },
  {
    label: 'درخواست‌های جدید',
    icon: 'Bell',
    section: 'new-requests',
    roles: ['coder'],
  },
  {
    label: 'نمونه کارها',
    icon: 'Briefcase',
    section: 'portfolio',
    roles: ['coder'],
  },
  {
    label: 'درآمد',
    icon: 'TrendingUp',
    section: 'earnings',
    roles: ['coder'],
  },
  {
    label: 'تنظیمات',
    icon: 'Settings',
    section: 'settings',
    roles: ['coder'],
  },
];

export const getMenuItemsForRole = (role: 'buyer' | 'coder'): MenuItem[] => {
  return role === 'buyer' ? buyerMenuItems : coderMenuItems;
};
