import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ArrowR<PERSON>, Rocket } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';
interface ProductCardProps {
  imageAlt: string;
  imageUrl?: string;
  title: string;
  description: string;
  price: string;
  category?: string;
  className?: string;
}

const ProductCard: React.FC<ProductCardProps> = ({
  imageAlt,
  imageUrl,
  title,
  description,
  price,
  category,
  className,
}) => {
  const getCategoryLabel = (cat: string) => {
    switch (cat) {
      case 'web':
        return 'وب اپلیکیشن';
      case 'mobile':
        return 'موبایل';
      case 'desktop':
        return 'دسکتاپ';
      default:
        return '';
    }
  };
  return (
    <Card
      className={`group overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1 border border-primary/20 hover:border-primary/40 bg-card/80 backdrop-blur-sm hover:scale-105 ${className || ''}`}
    >
      <div className="h-[clamp(6rem,15vw,10rem)] bg-gradient-to-br from-card to-secondary flex items-center justify-center text-muted-foreground overflow-hidden group-hover:from-primary/10 group-hover:to-accent/10 transition-all duration-300 relative">
        <Image
          src={imageUrl || `/images/products/${imageAlt}.jpg`}
          alt={imageAlt}
          className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-110"
          width={300}
          height={200}
        />
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-accent/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      </div>
      <CardContent className="p-[clamp(1rem,2vw,1.5rem)]">
        {category && (
          <div className="mb-3">
            <span className="inline-block px-2 py-1 text-xs font-mono bg-accent/20 text-accent rounded border border-accent/30">
              {getCategoryLabel(category)}
            </span>
          </div>
        )}
        <h3 className="text-[clamp(1rem,2vw,1.25rem)] font-semibold mb-2 text-primary font-mono glitch">
          {'{'} {title} {'}'}
        </h3>
        <p className=" mb-4 text-[clamp(0.875rem,1.5vw,1rem)] leading-relaxed">
          {description}
        </p>
        <div className="flex items-center justify-between mb-4 font-mono">
          <span className="block font-bold text-[clamp(0.875rem,1.5vw,1rem)] bg-primary/10 px-2 py-1 rounded border border-primary/20">
            {price}
          </span>
          <div className="h-px flex-grow mx-4 bg-gradient-to-r from-transparent via-primary/30 to-transparent"></div>
        </div>
        <Button
          className="w-full bg-gradient-to-r from-primary to-accent hover:from-primary/80 hover:to-accent/80 transition-all duration-300 text-[clamp(0.875rem,1.5vw,1rem)] font-mono shadow-lg hover:scale-105 group neo-brutal flex items-center justify-center gap-3"
          asChild
        >
          <Link href="#" className="flex items-center justify-center gap-3">
            <ArrowRight className="w-5 h-5 group-hover:-translate-x-1 transition-transform order-2" />
            <span className="order-1">مشاهده جزئیات</span>
            <Rocket className="w-5 h-5 group-hover:animate-pulse order-0" />
          </Link>
        </Button>
      </CardContent>
    </Card>
  );
};

export default ProductCard;
