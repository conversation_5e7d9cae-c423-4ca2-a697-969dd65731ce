use diesel::prelude::*;
use diesel_async::RunQueryDsl;
use uuid::Uuid;
use crate::common::{CommonModule, errors::{AppError, CustomErrors}, database::connection::DbPool, utils::pagination::PaginationParams};
use crate::schema::users::dsl::*;
use super::{entities::{UserEntity, NewUserEntity}, dto::UpdateUserDto};

pub struct UsersRepository {
    pool: DbPool,
}

impl UsersRepository {
    pub fn new(common_module: &CommonModule) -> Self {
        Self {
            pool: common_module.database.pool.clone(),
        }
    }

    pub async fn find_paginated(&self, params: &PaginationParams) -> Result<(Vec<UserEntity>, i64), AppError> {
        let mut conn = self.pool.get().await?;

        let total: i64 = users.count().get_result(&mut conn).await?;

        let results = users
            .order(created_at.desc())
            .limit(params.get_limit())
            .offset(params.get_offset())
            .load::<UserEntity>(&mut conn)
            .await?;

        Ok((results, total))
    }

    pub async fn find_by_id(&self, user_id_str: &str) -> Result<Option<UserEntity>, AppError> {
        let user_id = Uuid::parse_str(user_id_str)
            .map_err(|_| AppError::BadRequest("Invalid user ID format".to_string()))?;

        let mut conn = self.pool.get().await?;

        let result = users
            .filter(id.eq(user_id))
            .first::<UserEntity>(&mut conn)
            .await
            .optional()?;

        Ok(result)
    }

    pub async fn find_by_email(&self, user_email: &str) -> Result<Option<UserEntity>, AppError> {
        let mut conn = self.pool.get().await?;

        let result = users
            .filter(email.eq(user_email))
            .first::<UserEntity>(&mut conn)
            .await
            .optional()?;

        Ok(result)
    }

    pub async fn find_by_username(&self, user_username: &str) -> Result<Option<UserEntity>, AppError> {
        let mut conn = self.pool.get().await?;

        let result = users
            .filter(username.eq(user_username))
            .first::<UserEntity>(&mut conn)
            .await
            .optional()?;

        Ok(result)
    }

    pub async fn find_by_phone(&self, user_phone: &str) -> Result<Option<UserEntity>, AppError> {
        let mut conn = self.pool.get().await?;

        let result = users
            .filter(phone.eq(user_phone))
            .first::<UserEntity>(&mut conn)
            .await
            .optional()?;

        Ok(result)
    }

    pub async fn create(&self, new_user: &NewUserEntity) -> Result<UserEntity, AppError> {
        let mut conn = self.pool.get().await?;

        let result = diesel::insert_into(users)
            .values(new_user)
            .get_result::<UserEntity>(&mut conn)
            .await?;

        Ok(result)
    }

    pub async fn update(&self, user_id_str: &str, update_dto: &UpdateUserDto) -> Result<UserEntity, AppError> {
        let user_id = Uuid::parse_str(user_id_str)
            .map_err(|_| AppError::BadRequest("Invalid user ID format".to_string()))?;

        let mut conn = self.pool.get().await?;

        use chrono::Utc;
        let now = Utc::now();

        let update_query = diesel::update(users.filter(id.eq(user_id)));

        let result = if let Some(user_email) = &update_dto.email {
            if let Some(user_username) = &update_dto.username {
                update_query
                    .set((
                        email.eq(user_email),
                        username.eq(user_username),
                        updated_at.eq(now),
                    ))
                    .get_result::<UserEntity>(&mut conn)
                    .await
            } else {
                update_query
                    .set((
                        email.eq(user_email),
                        updated_at.eq(now),
                    ))
                    .get_result::<UserEntity>(&mut conn)
                    .await
            }
        } else if let Some(user_username) = &update_dto.username {
            update_query
                .set((
                    username.eq(user_username),
                    updated_at.eq(now),
                ))
                .get_result::<UserEntity>(&mut conn)
                .await
        } else {
            update_query
                .set(updated_at.eq(now))
                .get_result::<UserEntity>(&mut conn)
                .await
        };

        let user = result.optional()?.ok_or_else(CustomErrors::user_not_found)?;
        Ok(user)
    }

    pub async fn delete(&self, user_id_str: &str) -> Result<(), AppError> {
        let user_id = Uuid::parse_str(user_id_str)
            .map_err(|_| AppError::BadRequest("Invalid user ID format".to_string()))?;

        let mut conn = self.pool.get().await?;

        let rows_deleted = diesel::delete(users.filter(id.eq(user_id)))
            .execute(&mut conn)
            .await?;

        if rows_deleted == 0 {
            return Err(CustomErrors::user_not_found());
        }

        Ok(())
    }
}
