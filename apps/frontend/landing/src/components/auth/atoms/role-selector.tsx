'use client';

import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface RoleSelectorProps {
  value: string;
  onValueChange: (value: string) => void;
  showLabel?: boolean;
  error?: boolean;
}

export function RoleSelector({
  value,
  onValueChange,
  showLabel = true,
  error,
}: RoleSelectorProps) {
  return (
    <div className="flex justify-center items-center gap-2">
      {showLabel && (
        <Label className="mt-1">خریدار هستید یا برنامه نویس؟</Label>
      )}
      <Select value={value} onValueChange={onValueChange}>
        <SelectTrigger className={error ? 'border-red-500' : ''}>
          <SelectValue placeholder="نوع کاربر را انتخاب کنید" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="buyer">خریدار</SelectItem>
          <SelectItem value="coder">برنامه نویس</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
}
