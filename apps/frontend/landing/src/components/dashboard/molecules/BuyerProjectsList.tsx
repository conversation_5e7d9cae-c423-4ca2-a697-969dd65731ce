'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { getProjectsByBuyer } from '@/lib/data/mock-projects';
import { Project } from '@/lib/types/project';
import { filterProjects, sortProjects } from '@/lib/utils/project';
import { Plus, Search } from 'lucide-react';
import { useState } from 'react';
import { ProjectCard } from '../atoms/ProjectCard';

interface BuyerProjectsListProps {
  buyerId: string;
  onCreateProject?: () => void;
  onViewProject?: (projectId: string) => void;
  onEditProject?: (projectId: string) => void;
}

export function BuyerProjectsList({
  buyerId,
  onCreateProject,
  onViewProject,
  onEditProject
}: BuyerProjectsListProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'budget_high' | 'budget_low' | 'deadline'>('newest');
  const [activeTab, setActiveTab] = useState('all');

  const allProjects = getProjectsByBuyer(buyerId);

  const getFilteredProjects = (status?: string) => {
    let projects = allProjects;
    
    if (status && status !== 'all') {
      projects = projects.filter(p => p.status === status);
    }

    if (searchTerm) {
      projects = filterProjects(projects, { search: searchTerm });
    }

    return sortProjects(projects, sortBy);
  };

  const projectCounts = {
    all: allProjects.length,
    draft: allProjects.filter(p => p.status === 'draft').length,
    published: allProjects.filter(p => p.status === 'published').length,
    in_progress: allProjects.filter(p => p.status === 'in_progress').length,
    completed: allProjects.filter(p => p.status === 'completed').length,
    cancelled: allProjects.filter(p => p.status === 'cancelled').length,
  };

  const renderProjectGrid = (projects: Project[]) => {
    if (projects.length === 0) {
      return (
        <div className="text-center py-12">
          <div className="text-muted-foreground mb-4">
            {searchTerm ? 'هیچ پروژه‌ای با این جستجو یافت نشد' : 'هنوز پروژه‌ای ندارید'}
          </div>
          {!searchTerm && (
            <Button onClick={onCreateProject}>
              <Plus className="h-4 w-4 ml-2" />
              ایجاد پروژه جدید
            </Button>
          )}
        </div>
      );
    }

    return (
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {projects.map((project) => (
          <ProjectCard
            key={project.id}
            project={project}
            variant="buyer"
            onViewDetails={onViewProject}
            onEdit={onEditProject}
          />
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">پروژه‌های من</h2>
        <Button onClick={onCreateProject}>
          <Plus className="h-4 w-4 ml-2" />
          پروژه جدید
        </Button>
      </div>

      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="جستجو در پروژه‌ها..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pr-10"
          />
        </div>
        <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder="مرتب‌سازی" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="newest">جدیدترین</SelectItem>
            <SelectItem value="oldest">قدیمی‌ترین</SelectItem>
            <SelectItem value="budget_high">بودجه (زیاد به کم)</SelectItem>
            <SelectItem value="budget_low">بودجه (کم به زیاد)</SelectItem>
            <SelectItem value="deadline">نزدیک‌ترین ددلاین</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="all" className="text-xs">
            همه ({projectCounts.all})
          </TabsTrigger>
          <TabsTrigger value="draft" className="text-xs">
            پیش‌نویس ({projectCounts.draft})
          </TabsTrigger>
          <TabsTrigger value="published" className="text-xs">
            منتشر شده ({projectCounts.published})
          </TabsTrigger>
          <TabsTrigger value="in_progress" className="text-xs">
            در حال انجام ({projectCounts.in_progress})
          </TabsTrigger>
          <TabsTrigger value="completed" className="text-xs">
            تکمیل شده ({projectCounts.completed})
          </TabsTrigger>
          <TabsTrigger value="cancelled" className="text-xs">
            لغو شده ({projectCounts.cancelled})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-6">
          {renderProjectGrid(getFilteredProjects())}
        </TabsContent>
        <TabsContent value="draft" className="mt-6">
          {renderProjectGrid(getFilteredProjects('draft'))}
        </TabsContent>
        <TabsContent value="published" className="mt-6">
          {renderProjectGrid(getFilteredProjects('published'))}
        </TabsContent>
        <TabsContent value="in_progress" className="mt-6">
          {renderProjectGrid(getFilteredProjects('in_progress'))}
        </TabsContent>
        <TabsContent value="completed" className="mt-6">
          {renderProjectGrid(getFilteredProjects('completed'))}
        </TabsContent>
        <TabsContent value="cancelled" className="mt-6">
          {renderProjectGrid(getFilteredProjects('cancelled'))}
        </TabsContent>
      </Tabs>
    </div>
  );
}
