[package]
name = "dolfak-backend"
version = "0.1.0"
edition = "2021"

[dependencies]
actix-web = "4.0"
actix-cors = "0.7.1"
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0.141"
diesel = { version = "2.2.12", features = ["postgres", "chrono", "uuid", "r2d2"] }
diesel-async = { version = "0.6.1", features = ["postgres", "bb8", "r2d2"] }
pq-sys = { version = "0.7.2", features = ["bundled"] }
redis = { version = "0.32.4", features = ["tokio-comp"] }
jsonwebtoken = "9.0"
bcrypt = "0.17.0"
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
env_logger = "0.11.8"
log = "0.4"
dotenv = "0.15"
validator = { version = "0.20.0", features = ["derive"] }
thiserror = "2.0.12"
anyhow = "1.0"
futures = "0.3"
rust-s3 = { version = "0.35", default-features = false, features = ["tokio-rustls-tls"] }
mime = "0.3"
multer = "3.0"
utoipa = { version = "5.4.0", features = ["actix_extras", "chrono", "uuid"] }
utoipa-swagger-ui = { version = "9.0.2", features = ["actix-web"] }
reqwest = { version = "0.12.22", features = ["json", "multipart"] }
rand = "0.9.2"
urlencoding = "2.1"
futures-util = "0.3.31"
async-trait = "0.1.88"

[dev-dependencies]
actix-rt = "2.0"
tokio-test = "0.4"
cargo-watch = "8.5"
