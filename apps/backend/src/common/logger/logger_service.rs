use log::{info, warn, error, debug};
use chrono::Utc;
use serde_json::{json, Value};

#[derive(<PERSON><PERSON>, Default)]
pub struct LoggerService;

#[allow(dead_code)]
impl LoggerService {
    pub fn new() -> Self {
        Self
    }

    pub fn info(message: &str) {
        info!("🔵 {message}");
    }

    pub fn warn(message: &str) {
        warn!("🟡 {message}");
    }

    pub fn error(message: &str) {
        error!("🔴 {message}");
    }

    pub fn debug(message: &str) {
        debug!("🟣 {message}");
    }

    pub fn success(message: &str) {
        info!("🟢 {message}");
    }

    pub fn log_request(
        method: &str,
        path: &str,
        status_code: u16,
        duration_ms: f64,
        remote_addr: Option<&str>,
        _user_agent: Option<&str>,
        user_id: Option<&str>,
    ) {
        let status_emoji = match status_code {
            200..=299 => "✅",
            300..=399 => "🔄",
            400..=499 => "⚠️",
            500..=599 => "❌",
            _ => "❓",
        };

        let remote = remote_addr.unwrap_or("unknown");
        let duration_str = if duration_ms < 1.0 {
            format!("{duration_ms:.3}ms")
        } else if duration_ms < 1000.0 {
            format!("{duration_ms:.1}ms")
        } else {
            format!("{:.1}s", duration_ms / 1000.0)
        };

        let base_log = format!(
            "{status_emoji} {method} {path} {status_code} - {duration_str} ({remote})"
        );

        let full_log = if let Some(uid) = user_id {
            format!("{base_log} [user:{uid}]")
        } else {
            base_log
        };

        match status_code {
            200..=399 => info!("{full_log}"),
            400..=499 => warn!("{full_log}"),
            500..=599 => error!("{full_log}"),
            _ => info!("{full_log}"),
        }
    }

    pub fn log_error(error: &str, context: Option<&str>) {
        match context {
            Some(ctx) => error!("🔴 Error in {ctx}: {error}"),
            None => error!("🔴 Error: {error}"),
        }
    }

    pub fn log_database_query(query: &str, duration_ms: f64, rows_affected: Option<u64>) {
        let duration_str = if duration_ms < 1.0 {
            format!("{duration_ms:.3}ms")
        } else {
            format!("{duration_ms:.1}ms")
        };

        let rows_str = if let Some(rows) = rows_affected {
            format!(" ({rows} rows)")
        } else {
            String::new()
        };

        debug!("🗃️  DB Query: {query} ({duration_str}){rows_str}");
    }

    pub fn log_auth_event(event: &str, user_id: Option<&str>, success: bool) {
        let status = if success { "✅" } else { "❌" };
        let user_str = user_id.map(|id| format!(" [user:{id}]")).unwrap_or_default();
        info!("{status} Auth: {event}{user_str}");
    }

    pub fn log_cache_operation(operation: &str, key: &str, hit: bool, duration_ms: Option<f64>) {
        let status = if hit { "🎯" } else { "🔍" };
        let duration_str = duration_ms
            .map(|d| format!(" ({d:.3}ms)"))
            .unwrap_or_default();
        debug!("{status} Cache {operation}: {key}{duration_str}");
    }

    pub fn log_startup(message: &str) {
        info!("🚀 {message}");
    }

    pub fn log_shutdown(message: &str) {
        info!("🛑 {message}");
    }

    pub fn log_health_check(service: &str, healthy: bool, response_time_ms: Option<f64>) {
        let status = if healthy { "💚" } else { "💔" };
        let time_str = response_time_ms
            .map(|t| format!(" ({t:.1}ms)"))
            .unwrap_or_default();
        info!("{} Health check - {}: {}{}", status, service, if healthy { "OK" } else { "FAILED" }, time_str);
    }

    pub fn log_structured(level: &str, message: &str, context: Option<Value>) {
        let timestamp = Utc::now().to_rfc3339();
        let log_entry = json!({
            "timestamp": timestamp,
            "level": level,
            "message": message,
            "context": context
        });

        match level {
            "error" => error!("{log_entry}"),
            "warn" => warn!("{log_entry}"),
            "info" => info!("{log_entry}"),
            "debug" => debug!("{log_entry}"),
            _ => info!("{log_entry}"),
        }
    }
}
