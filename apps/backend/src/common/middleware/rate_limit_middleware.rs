use actix_web::{
    dev::{forward_ready, Service, ServiceRequest, ServiceResponse, Transform},
    Error, HttpResponse,
};
use futures_util::future::LocalBoxFuture;
use std::future::{ready, Ready};
use std::rc::Rc;
use std::collections::HashMap;
use std::time::{Duration, Instant};
use tokio::sync::Mutex;
use std::sync::Arc;

#[derive(Debug, Clone)]
struct RateLimitEntry {
    count: u32,
    reset_time: Instant,
}

/// Rate limiting middleware
pub struct RateLimitMiddleware {
    max_requests: u32,
    window_duration: Duration,
    storage: Arc<Mutex<HashMap<String, RateLimitEntry>>>,
}

impl RateLimitMiddleware {
    pub fn new(max_requests: u32, window_duration: Duration) -> Self {
        Self {
            max_requests,
            window_duration,
            storage: Arc::new(Mutex::new(HashMap::new())),
        }
    }
}

impl<S, B> Transform<S, ServiceRequest> for RateLimitMiddleware
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Transform = RateLimitMiddlewareService<S>;
    type InitError = ();
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ready(Ok(RateLimitMiddlewareService {
            service: Rc::new(service),
            max_requests: self.max_requests,
            window_duration: self.window_duration,
            storage: Arc::clone(&self.storage),
        }))
    }
}

pub struct RateLimitMiddlewareService<S> {
    service: Rc<S>,
    max_requests: u32,
    window_duration: Duration,
    storage: Arc<Mutex<HashMap<String, RateLimitEntry>>>,
}

impl<S, B> Service<ServiceRequest> for RateLimitMiddlewareService<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Future = LocalBoxFuture<'static, Result<Self::Response, Self::Error>>;

    forward_ready!(service);

    fn call(&self, req: ServiceRequest) -> Self::Future {
        let service = Rc::clone(&self.service);
        let storage = Arc::clone(&self.storage);
        let max_requests = self.max_requests;
        let window_duration = self.window_duration;

        Box::pin(async move {
            // Get client IP address
            let client_ip = req
                .connection_info()
                .peer_addr()
                .unwrap_or("unknown")
                .to_string();

            let now = Instant::now();
            let mut storage_guard = storage.lock().await;

            // Clean up expired entries
            storage_guard.retain(|_, entry| now < entry.reset_time);

            // Check rate limit
            if let Some(entry) = storage_guard.get_mut(&client_ip) {
                if entry.count >= max_requests {
                    drop(storage_guard);
                    return Ok(ServiceResponse::new(
                        req.into_parts().0,
                        HttpResponse::TooManyRequests()
                            .json(serde_json::json!({
                                "error": "Rate limit exceeded",
                                "message": format!("Too many requests. Limit: {} per {:?}", max_requests, window_duration)
                            }))
                    ));
                } else {
                    entry.count += 1;
                }
            } else {
                storage_guard.insert(client_ip, RateLimitEntry {
                    count: 1,
                    reset_time: now + window_duration,
                });
            }

            drop(storage_guard);
            service.call(req).await
        })
    }
}
