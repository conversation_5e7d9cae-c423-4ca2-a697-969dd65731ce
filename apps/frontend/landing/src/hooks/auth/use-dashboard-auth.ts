'use client';

import { getCurrentUser, isAuthorizedForRole } from '@/lib/dashboard/auth';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useAuth } from './use-auth';

export function useDashboardAuth(requiredRole?: 'buyer' | 'coder') {
  const { isAuthenticated, isLoading } = useAuth();
  const [user, setUser] = useState(getCurrentUser());
  const [isAuthorized, setIsAuthorized] = useState(false);
  const router = useRouter();

  useEffect(() => {
    if (!isLoading) {
      if (!isAuthenticated) {
        router.push('/');
        return;
      }

      const currentUser = getCurrentUser();
      setUser(currentUser);

      if (requiredRole) {
        const authorized = isAuthorizedForRole(requiredRole);
        setIsAuthorized(authorized);

        if (!authorized && currentUser) {
          const correctPath =
            currentUser.role === 'buyer' ? '/buyers' : '/coders';
          router.push(correctPath);
        }
      } else {
        setIsAuthorized(!!currentUser);
      }
    }
  }, [isAuthenticated, isLoading, requiredRole, router]);

  return {
    user,
    isLoading,
    isAuthenticated,
    isAuthorized,
  };
}
