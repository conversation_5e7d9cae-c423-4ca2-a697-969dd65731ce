import { BuyerProfile, CoderProfile, Review } from '@/lib/types/user';

export const mockCoders: CoderProfile[] = [
  {
    id: 'coder1',
    username: 'محمد رضایی',
    email: '<EMAIL>',
    role: 'coder',
    avatar: 'https://avatar.vercel.sh/mohammad',
    firstName: 'محمد',
    lastName: 'رضایی',
    phone: '09123456789',
    bio: 'توسعه‌دهنده فول‌استک با بیش از ۵ سال تجربه در React، Node.js و MongoDB. علاقه‌مند به توسعه اپلیکیشن‌های مدرن و کارآمد.',
    location: 'تهران، ایران',
    website: 'https://mohammadrezaei.dev',
    socialLinks: {
      github: 'https://github.com/mohammadrezaei',
      linkedin: 'https://linkedin.com/in/mohammadrezaei',
      telegram: '@mohammadrezaei'
    },
    createdAt: '2023-01-15T10:00:00Z',
    updatedAt: '2025-01-20T14:30:00Z',
    isVerified: true,
    rating: 4.8,
    totalReviews: 24,
    skills: ['React', 'Node.js', 'MongoDB', 'TypeScript', 'Next.js', 'Express', 'PostgreSQL'],
    experience: 'senior',
    hourlyRate: {
      min: 500000,
      max: 800000,
      currency: 'IRR'
    },
    availability: 'available',
    languages: ['فارسی', 'انگلیسی'],
    education: [
      {
        id: 'edu1',
        institution: 'دانشگاه تهران',
        degree: 'کارشناسی',
        field: 'مهندسی کامپیوتر',
        startDate: '2015-09-01',
        endDate: '2019-06-01',
        description: 'تخصص در نرم‌افزار'
      }
    ],
    certifications: [
      {
        id: 'cert1',
        name: 'AWS Certified Developer',
        issuer: 'Amazon Web Services',
        issueDate: '2023-05-15',
        credentialId: 'AWS-DEV-2023-001'
      }
    ],
    portfolio: [
      {
        id: 'port1',
        title: 'سیستم مدیریت فروشگاه آنلاین',
        description: 'توسعه سیستم کامل مدیریت فروشگاه آنلاین با React و Node.js',
        category: 'وب سایت',
        skills: ['React', 'Node.js', 'MongoDB', 'Stripe'],
        images: ['https://via.placeholder.com/600x400'],
        liveUrl: 'https://shop-demo.example.com',
        sourceUrl: 'https://github.com/mohammadrezaei/shop-system',
        completedAt: '2024-12-01',
        client: 'شرکت تجارت الکترونیک پارس',
        testimonial: {
          text: 'کار فوق‌العاده‌ای انجام داد. سیستم کاملاً مطابق انتظارات ما بود.',
          rating: 5,
          clientName: 'علی احمدی'
        }
      }
    ],
    completedProjects: 18,
    totalEarnings: 450000000,
    responseTime: 2,
    successRate: 95
  },
  {
    id: 'coder2',
    username: 'سارا احمدی',
    email: '<EMAIL>',
    role: 'coder',
    avatar: 'https://avatar.vercel.sh/sara',
    firstName: 'سارا',
    lastName: 'احمدی',
    bio: 'طراح UI/UX با تخصص در طراحی اپلیکیشن‌های موبایل و وب. علاقه‌مند به ایجاد تجربه‌های کاربری بهینه.',
    location: 'اصفهان، ایران',
    createdAt: '2023-03-20T09:00:00Z',
    updatedAt: '2025-01-18T11:15:00Z',
    isVerified: true,
    rating: 4.9,
    totalReviews: 31,
    skills: ['UI/UX Design', 'Figma', 'Adobe XD', 'Prototyping', 'User Research'],
    experience: 'mid',
    hourlyRate: {
      min: 300000,
      max: 600000,
      currency: 'IRR'
    },
    availability: 'available',
    languages: ['فارسی', 'انگلیسی'],
    portfolio: [
      {
        id: 'port2',
        title: 'طراحی اپلیکیشن بانکی',
        description: 'طراحی کامل رابط کاربری اپلیکیشن موبایل بانکی',
        category: 'طراحی UI/UX',
        skills: ['Figma', 'Prototyping', 'User Research'],
        images: ['https://via.placeholder.com/600x400'],
        completedAt: '2024-11-15',
        client: 'بانک ملی',
        testimonial: {
          text: 'طراحی بسیار زیبا و کاربردی. کاربران ما بسیار راضی هستند.',
          rating: 5,
          clientName: 'رضا صادقی'
        }
      }
    ],
    completedProjects: 25,
    totalEarnings: *********,
    responseTime: 1,
    successRate: 98
  }
];

export const mockBuyers: BuyerProfile[] = [
  {
    id: 'buyer1',
    username: 'علی احمدی',
    email: '<EMAIL>',
    role: 'buyer',
    avatar: 'https://avatar.vercel.sh/ali',
    firstName: 'علی',
    lastName: 'احمدی',
    phone: '09121234567',
    bio: 'مدیر فناوری شرکت تجارت الکترونیک پارس',
    location: 'تهران، ایران',
    company: 'شرکت تجارت الکترونیک پارس',
    industry: 'تجارت الکترونیک',
    createdAt: '2023-02-10T08:00:00Z',
    updatedAt: '2025-01-19T16:45:00Z',
    isVerified: true,
    rating: 4.8,
    totalReviews: 12,
    totalSpent: *********,
    projectsPosted: 15,
    hireRate: 80
  },
  {
    id: 'buyer2',
    username: 'مریم کریمی',
    email: '<EMAIL>',
    role: 'buyer',
    avatar: 'https://avatar.vercel.sh/maryam',
    firstName: 'مریم',
    lastName: 'کریمی',
    bio: 'کارآفرین و بنیان‌گذار استارتاپ حمل و نقل هوشمند',
    location: 'مشهد، ایران',
    company: 'استارتاپ حمل و نقل هوشمند',
    industry: 'حمل و نقل',
    createdAt: '2023-04-05T12:30:00Z',
    updatedAt: '2025-01-21T09:20:00Z',
    isVerified: true,
    rating: 4.9,
    totalReviews: 8,
    totalSpent: 1200000000,
    projectsPosted: 10,
    hireRate: 90
  }
];

export const mockReviews: Review[] = [
  {
    id: 'review1',
    projectId: '1',
    reviewerId: 'buyer1',
    revieweeId: 'coder1',
    rating: 5,
    comment: 'کار فوق‌العاده‌ای انجام داد. پروژه در زمان مقرر و با کیفیت بالا تحویل داده شد.',
    createdAt: '2024-12-05T14:30:00Z',
    projectTitle: 'توسعه سایت فروشگاهی',
    reviewer: {
      id: 'buyer1',
      username: 'علی احمدی',
      avatar: 'https://avatar.vercel.sh/ali'
    }
  },
  {
    id: 'review2',
    projectId: '5',
    reviewerId: 'buyer5',
    revieweeId: 'coder2',
    rating: 5,
    comment: 'طراحی بسیار زیبا و کاربردی. کاملاً مطابق انتظارات ما بود.',
    createdAt: '2025-01-01T10:15:00Z',
    projectTitle: 'طراحی UI/UX اپلیکیشن بانکی',
    reviewer: {
      id: 'buyer5',
      username: 'رضا صادقی',
      avatar: 'https://avatar.vercel.sh/reza'
    }
  }
];

export function getCoderById(id: string): CoderProfile | undefined {
  return mockCoders.find(coder => coder.id === id);
}

export function getBuyerById(id: string): BuyerProfile | undefined {
  return mockBuyers.find(buyer => buyer.id === id);
}

export function getReviewsByUserId(userId: string): Review[] {
  return mockReviews.filter(review => review.revieweeId === userId);
}

export function getTopCoders(limit: number = 10): CoderProfile[] {
  return mockCoders
    .sort((a, b) => b.rating - a.rating)
    .slice(0, limit);
}

export function searchCoders(filters: {
  skills?: string[];
  experience?: string;
  availability?: string;
  location?: string;
  minRating?: number;
}): CoderProfile[] {
  return mockCoders.filter(coder => {
    if (filters.skills && filters.skills.length > 0) {
      const hasRequiredSkills = filters.skills.some(skill => 
        coder.skills.includes(skill)
      );
      if (!hasRequiredSkills) return false;
    }

    if (filters.experience && coder.experience !== filters.experience) {
      return false;
    }

    if (filters.availability && coder.availability !== filters.availability) {
      return false;
    }

    if (filters.location && !coder.location?.includes(filters.location)) {
      return false;
    }

    if (filters.minRating && coder.rating < filters.minRating) {
      return false;
    }

    return true;
  });
}
