import { Search } from 'lucide-react';
import React from 'react';

interface BlogSearchProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  className?: string;
}

const BlogSearch: React.FC<BlogSearchProps> = ({
  searchTerm,
  onSearchChange,
  className,
}) => {
  return (
    <div className={`relative max-w-md ${className || ''}`}>
      <div className="relative">
        <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 sm:w-5 sm:h-5 text-muted-foreground" />
        <input
          type="text"
          placeholder="جستجو در مقالات..."
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          className="w-full pr-10 pl-4 py-3 bg-card/50 border border-primary/20 rounded-lg text-sm sm:text-base font-mono text-foreground placeholder-muted-foreground focus:outline-none focus:border-primary/50 focus:bg-card/80 transition-all duration-300 hover:border-primary/30"
          dir="rtl"
        />
      </div>
      <div className="absolute -inset-1 bg-gradient-to-r from-primary/20 to-accent/20 rounded-lg opacity-0 hover:opacity-30 transition-opacity duration-300 -z-10"></div>
    </div>
  );
};

export default BlogSearch;
