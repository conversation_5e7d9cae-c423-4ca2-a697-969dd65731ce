'use client';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/core/utils';
import {
  ArrowLeft,
  ArrowRight,
  Loader2,
  RotateCcw,
  Save,
  SkipForward,
} from 'lucide-react';
import React from 'react';
import { WizardTheme } from './wizard-types';

interface WizardNavigationProps {
  canGoNext: boolean;
  canGoPrevious: boolean;
  isFirstStep: boolean;
  isLastStep: boolean;
  isValidating: boolean;
  onNext: () => void;
  onPrevious: () => void;
  onSkip?: () => void;
  onSave?: () => void;
  onReset?: () => void;
  nextLabel?: string;
  previousLabel?: string;
  submitLabel?: string;
  skipLabel?: string;
  saveLabel?: string;
  resetLabel?: string;
  showSkip?: boolean;
  showSave?: boolean;
  showReset?: boolean;
  position?: WizardTheme['navigationPosition'];
  hasUnsavedChanges?: boolean;
  customActions?: React.ReactNode;
  variant?: 'default' | 'minimal' | 'expanded';
}

export const WizardNavigation: React.FC<WizardNavigationProps> = ({
  canGoNext,
  canGoPrevious,
  isFirstStep,
  isLastStep,
  isValidating,
  onNext,
  onPrevious,
  onSkip,
  onSave,
  onReset,
  nextLabel = 'بعدی',
  previousLabel = 'قبلی',
  submitLabel = 'تایید نهایی',
  skipLabel = 'رد کردن',
  saveLabel = 'ذخیره',
  resetLabel = 'شروع مجدد',
  showSkip = false,
  showSave = false,
  showReset = false,
  position = 'bottom',
  hasUnsavedChanges = false,
  customActions,
  variant = 'default',
}) => {
  const getContainerStyles = () => {
    const baseStyles = 'flex items-center gap-3';

    switch (position) {
      case 'floating':
        return cn(
          baseStyles,
          'fixed bottom-6 left-1/2 transform -translate-x-1/2 bg-background/95 backdrop-blur-sm border rounded-lg px-4 py-3 shadow-lg z-50',
        );
      case 'sidebar':
        return cn(baseStyles, 'flex-col space-y-3');
      default:
        return cn(baseStyles, 'justify-between pt-6 border-t');
    }
  };

  const getButtonVariant = (type: 'primary' | 'secondary' | 'ghost') => {
    if (variant === 'minimal') return type === 'primary' ? 'default' : 'ghost';
    if (variant === 'expanded')
      return type === 'primary' ? 'default' : 'outline';
    return type === 'primary'
      ? 'default'
      : type === 'secondary'
        ? 'outline'
        : 'ghost';
  };

  const renderPrimaryButton = () => {
    const isSubmit = isLastStep;
    const label = isSubmit ? submitLabel : nextLabel;

    return (
      <Button
        type="button"
        onClick={onNext}
        disabled={!canGoNext || isValidating}
        variant={getButtonVariant('primary')}
        size={variant === 'expanded' ? 'lg' : 'default'}
        className={cn(
          'transition-all duration-300 hover:scale-105 group neo-brutal text-primary-foreground bg-gradient-to-r from-primary to-accent hover:from-primary/80 hover:to-accent/80 shadow-sm font-mono text-[clamp(0.4rem,3vw,0.8rem)]',
          isValidating && 'pointer-events-none',
        )}
      >
        {isValidating ? (
          <>
            <Loader2 className="w-4 h-4 animate-spin" />
            {isSubmit ? 'در حال ارسال...' : 'در حال بررسی...'}
          </>
        ) : (
          <>
            {label}
            {!isSubmit && <ArrowRight className="w-4 h-4" />}
          </>
        )}
      </Button>
    );
  };

  const renderSecondaryButton = () => {
    if (isFirstStep && !showSave && !showReset && !customActions) return null;

    return (
      <div className="flex items-center gap-2">
        {!isFirstStep && (
          <Button
            type="button"
            variant={getButtonVariant('secondary')}
            onClick={onPrevious}
            disabled={!canGoPrevious || isValidating}
            size={variant === 'expanded' ? 'lg' : 'default'}
            className="transition-all duration-300 hover:scale-105 group neo-brutal text-primary-foreground bg-gradient-to-r from-primary to-accent hover:from-primary/80 hover:to-accent/80 shadow-sm font-mono text-[clamp(0.4rem,3vw,0.8rem)]"
          >
            <ArrowLeft className="w-4 h-4" />
            {previousLabel}
          </Button>
        )}

        {showReset && onReset && (
          <Button
            type="button"
            variant="outline"
            onClick={onReset}
            disabled={isValidating}
            size={variant === 'expanded' ? 'lg' : 'sm'}
            className="transition-all duration-300 hover:scale-105 group neo-brutal text-secondary-foreground bg-secondary hover:bg-secondary/80 shadow-sm font-mono text-[clamp(0.4rem,3vw,0.8rem)]"
          >
            <RotateCcw className="w-4 h-4" />
            {resetLabel}
          </Button>
        )}

        {showSave && onSave && (
          <Button
            type="button"
            variant="ghost"
            onClick={onSave}
            disabled={isValidating}
            size={variant === 'expanded' ? 'lg' : 'sm'}
            className={cn(
              'transition-all duration-300 hover:scale-105 group neo-brutal text-muted-foreground bg-muted hover:bg-muted/80 shadow-sm font-mono text-[clamp(0.4rem,3vw,0.8rem)]',
              hasUnsavedChanges &&
                'text-amber-600 dark:text-amber-400 hover:text-amber-700 dark:hover:text-amber-300',
            )}
          >
            <Save className="w-4 h-4" />
            {hasUnsavedChanges && variant === 'expanded'
              ? 'ذخیره تغییرات'
              : saveLabel}
          </Button>
        )}

        {customActions}
      </div>
    );
  };

  const renderActions = () => {
    if (position === 'sidebar') {
      return (
        <div className="flex flex-col space-y-2 w-full">
          {renderPrimaryButton()}
          {renderSecondaryButton()}
          {showSkip && onSkip && (
            <Button
              type="button"
              variant="ghost"
              onClick={onSkip}
              disabled={isValidating}
              size="sm"
            >
              <SkipForward className="w-4 h-4" />
              {skipLabel}
            </Button>
          )}
        </div>
      );
    }

    return (
      <>
        <div className="flex items-center gap-2">
          {renderSecondaryButton()}
          {showSkip && onSkip && !isLastStep && (
            <Button
              type="button"
              variant="ghost"
              onClick={onSkip}
              disabled={isValidating}
              size="sm"
              className="flex items-center gap-2"
            >
              <SkipForward className="w-4 h-4" />
              {skipLabel}
            </Button>
          )}
        </div>
        <div>{renderPrimaryButton()}</div>
      </>
    );
  };

  if (variant === 'minimal' && position === 'floating') {
    return (
      <div className={getContainerStyles()}>
        <Button
          type="button"
          onClick={onNext}
          disabled={!canGoNext || isValidating}
          variant="default"
          size="sm"
          className="rounded-full"
        >
          {isValidating ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : isLastStep ? (
            submitLabel
          ) : (
            <ArrowRight className="w-4 h-4" />
          )}
        </Button>
      </div>
    );
  }

  return (
    <div className={getContainerStyles()}>
      {renderActions()}

      {hasUnsavedChanges && variant === 'expanded' && (
        <div className="text-xs text-amber-600 dark:text-amber-400 flex items-center gap-1">
          <div className="w-2 h-2 bg-amber-400 rounded-full animate-pulse" />
          تغییرات ذخیره نشده
        </div>
      )}
    </div>
  );
};
