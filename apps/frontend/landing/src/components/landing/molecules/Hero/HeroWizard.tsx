'use client';
import { useSectionModeContext } from '@/components/shared/section-mode/SectionModeWrapper';
import { MultiStepWizard } from '@/components/shared/wizard/multi-step-wizard';
import { WizardConfig } from '@/components/shared/wizard/wizard-types';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/core/error-handler';
import {
  personalInfoValidationSchema,
  projectTypeValidationSchema,
  wizardSubmissionSchema,
} from '@/lib/validations/wizard';
import { submitWizard } from '@/lib/wizard/wizard';
import { ArrowLeft } from 'lucide-react';
import React, { useCallback, useMemo } from 'react';
import { toast } from 'sonner';
import { ConfirmationStep } from './wizard-steps/ConfirmationStep';
import { PersonalInfoStep } from './wizard-steps/PersonalInfoStep';
import { ProjectTypeStep } from './wizard-steps/ProjectTypeStep';

interface HeroWizardProps {
  className?: string;
}

export const HeroWizard: React.FC<HeroWizardProps> = ({ className }) => {
  const { resetMode } = useSectionModeContext();

  const handleWizardComplete = useCallback(
    async (allData: any, context: any) => {
      console.log('Wizard completed with data:', allData);
      console.log('Context:', context);

      const hasRequiredData = Object.values(allData).some(
        (stepData) =>
          stepData &&
          typeof stepData === 'object' &&
          Object.keys(stepData).length > 0,
      );

      if (!hasRequiredData) {
        toast.error('خطا: اطلاعات لازم یافت نشد. لطفاً مجدد تلاش کنید.', {
          className: 'bg-red-500! text-white! font-yekanBakhFaNum',
        });
        return;
      }

      try {
        // Handle both indexed (0, 1, 2) and named keys format
        const dataArray = Object.values(allData);
        const personalInfo = dataArray[0] || allData['personal-info'] || {};
        const projectInfo = dataArray[1] || allData['project-type'] || {};

        const submissionData = {
          full_name: personalInfo.fullName,
          email: personalInfo.email,
          phone: personalInfo.phone,
          project_type: projectInfo.projectType,
          description: projectInfo.description,
        };

        console.log('Submitting data:', submissionData);
        const validatedData = wizardSubmissionSchema.parse(submissionData);

        await submitWizard(validatedData);

        toast.success(
          'درخواست شما با موفقیت ارسال شد! تیم ما به زودی با شما تماس خواهد گرفت.',
          {
            className: 'bg-green-500! text-white! font-yekanBakhFaNum',
          },
        );

        resetMode();
      } catch (error) {
        console.error('Error submitting form:', error);
        if (error instanceof Error && error.name === 'ZodError') {
          console.error('Validation errors:', (error as any).errors);
        }
        const errorMessage = ErrorHandler.getErrorMessage(error as any);
        toast.error(`خطا در ارسال درخواست: ${errorMessage}`, {
          className: 'bg-red-500! text-white! font-yekanBakhFaNum',
        });
      }
    },
    [resetMode],
  );

  const wizardConfig: WizardConfig = useMemo(
    () => ({
      wizardId: 'hero-section-wizard',
      steps: [
        {
          id: 'personal-info',
          title: 'اطلاعات شما',
          description: 'لطفاً اطلاعات تماس خود را وارد کنید',
          component: PersonalInfoStep,
          validate: (data: any) => {
            console.log('Validating personal info step with data:', data);
            try {
              personalInfoValidationSchema.parse({
                fullName: data.fullName || '',
                email: data.email || '',
                phone: data.phone || '',
              });
              return true;
            } catch (error: any) {
              console.log('Personal info validation error:', error);
              console.log('Error.issues:', error.issues);
              const errors: string[] = [];
              if (error.issues && Array.isArray(error.issues)) {
                error.issues.forEach((issue: any) => {
                  if (issue.message) {
                    errors.push(issue.message);
                  }
                });
              }
              console.log('Personal info final errors:', errors);
              return errors.length > 0 ? errors : ['خطا در اعتبارسنجی اطلاعات'];
            }
          },
        },
        {
          id: 'project-type',
          title: 'نوع پروژه',
          description: 'درباره پروژه‌تان به ما بگویید',
          component: ProjectTypeStep,
          validate: (data: any) => {
            console.log('Validating project type step with data:', data);

            try {
              projectTypeValidationSchema.parse({
                projectType: data.projectType || '',
                description: data.description || '',
              });
              return true;
            } catch (error: any) {
              console.log('Validation error caught:', error);
              console.log('Error.issues:', error.issues);

              const errors: string[] = [];
              if (error.issues && Array.isArray(error.issues)) {
                error.issues.forEach((issue: any) => {
                  console.log('Processing issue:', issue);
                  if (issue.message) {
                    errors.push(issue.message);
                  }
                });
              }

              console.log('Final errors array:', errors);

              return errors.length > 0
                ? errors
                : ['لطفاً اطلاعات مورد نیاز را کامل کنید'];
            }
          },
        },
        {
          id: 'confirmation',
          title: 'تایید نهایی',
          description: 'بررسی اطلاعات و ارسال درخواست',
          component: ConfirmationStep,
        },
      ],
      onComplete: handleWizardComplete,
      resetOnComplete: true,
      onStepChange: (stepIndex, stepData, direction) => {
        console.log(
          `Step changed to ${stepIndex}, direction: ${direction}`,
          stepData,
        );
      },
      onValidationError: (stepIndex, errors) => {
        console.log(`Validation errors on step ${stepIndex}:`, errors);
      },
      persistProgress: true,
      allowStepSkipping: false,
      autoSave: true,
      autoSaveInterval: 30000,
      confirmOnExit: true,
      theme: {
        progressVariant: 'steps',
        navigationPosition: 'bottom',
        animationType: 'slide',
      },
    }),
    [handleWizardComplete],
  );

  return (
    <section className={`relative py-4 ${className || ''}`}>
      {/* Simplified background */}
      <div className="absolute inset-0 bg-gradient-to-br from-background via-card to-secondary" />

      <div className="relative z-10 container mx-auto px-4">
        <div className="text-center mb-4">
          <h1 className="text-xl md:text-2xl font-bold mb-2 font-mono">
            {/* Compact back button */}

            <span className="text-primary">شروع همکاری</span>
            <span className="text-accent block">با دلفک</span>
            <span className="text-muted-foreground text-sm max-w-lg mx-auto">
              در چند قدم ساده، درخواست خود را ثبت کنید
            </span>
          </h1>
          <Button
            variant="default"
            onClick={resetMode}
            className="mb-4 transition-all duration-300 hover:scale-105 group neo-brutal text-green-200! bg-gradient-to-r from-primary to-accent hover:from-primary/80 hover:to-accent/80 shadow-sm font-mono text-[clamp(0.4rem,3vw,0.8rem)]!"
          >
            <ArrowLeft className="w-4 h-4" />
            بازگشت به صفحه اصلی
          </Button>
        </div>

        <MultiStepWizard
          {...wizardConfig}
          className="max-w-lg mx-auto"
          showStepProgress={true}
          showNavigation={true}
          allowStepClick={false}
        />
      </div>
    </section>
  );
};
