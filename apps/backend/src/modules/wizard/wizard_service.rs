use crate::common::{CommonModule, errors::AppError, utils::pagination::{PaginationParams, PaginatedResponse}};
use super::{
    wizard_repository::WizardRepository,
    entities::{WizardSubmission, WizardSubmissionStatus},
    dto::{CreateWizardSubmissionDto, WizardSubmissionResponseDto}
};

pub struct WizardService {
    repository: WizardRepository,
}

impl WizardService {
    pub fn new(common_module: &CommonModule) -> Self {
        Self {
            repository: WizardRepository::new(common_module),
        }
    }

    pub async fn submit_wizard(&self, dto: CreateWizardSubmissionDto) -> Result<WizardSubmissionResponseDto, AppError> {
        let submission = self.repository.create(dto).await?;
        Ok(submission.into())
    }

    pub async fn find_paginated(&self, params: &PaginationParams) -> Result<PaginatedResponse<WizardSubmissionResponseDto>, AppError> {
        let (submissions, total) = self.repository.find_paginated(params).await?;
        let submission_dtos: Vec<WizardSubmissionResponseDto> = submissions.into_iter().map(|submission| submission.into()).collect();
        Ok(PaginatedResponse::new(submission_dtos, params, total))
    }

    pub async fn find_by_id(&self, id: &str) -> Result<Option<WizardSubmissionResponseDto>, AppError> {
        let submission = self.repository.find_by_id(id).await?;
        Ok(submission.map(|s| s.into()))
    }

    pub async fn update_status(&self, id: &str, status: WizardSubmissionStatus) -> Result<WizardSubmissionResponseDto, AppError> {
        let submission = self.repository.update_status(id, status).await?;
        Ok(submission.into())
    }
}

impl From<WizardSubmission> for WizardSubmissionResponseDto {
    fn from(submission: WizardSubmission) -> Self {
        let status_str = submission.get_status().to_string();
        Self {
            id: submission.id,
            full_name: submission.full_name,
            email: submission.email,
            phone: submission.phone,
            project_type: submission.project_type,
            description: submission.description,
            status: status_str,
            created_at: submission.created_at,
            updated_at: submission.updated_at,
        }
    }
}
