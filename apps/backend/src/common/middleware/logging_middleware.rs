use actix_web::{
    dev::{forward_ready, Service, ServiceRequest, ServiceResponse, Transform},
    Error, HttpMessage,
};
use futures_util::future::LocalBoxFuture;
use std::future::{ready, Ready};
use std::rc::Rc;
use std::time::Instant;
use uuid::Uuid;

use crate::common::logger::LoggerService;

/// Request logging middleware with comprehensive logging capabilities
pub struct LoggingMiddleware;

impl<S, B> Transform<S, ServiceRequest> for LoggingMiddleware
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Transform = LoggingMiddlewareService<S>;
    type InitError = ();
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ready(Ok(LoggingMiddlewareService {
            service: Rc::new(service),
        }))
    }
}

pub struct LoggingMiddlewareService<S> {
    service: Rc<S>,
}

impl<S, B> Service<ServiceRequest> for LoggingMiddlewareService<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Future = LocalBoxFuture<'static, Result<Self::Response, Self::Error>>;

    forward_ready!(service);

    fn call(&self, req: ServiceRequest) -> Self::Future {
        let service = Rc::clone(&self.service);
        let start_time = Instant::now();
        let request_id = Uuid::new_v4().to_string();

        // Extract request details
        let method = req.method().to_string();
        let path = req.path().to_string();
        let query = req.query_string().to_string();
        let remote_addr = req
            .connection_info()
            .realip_remote_addr()
            .map(|addr| addr.to_string());
        let user_agent = req
            .headers()
            .get("user-agent")
            .and_then(|h| h.to_str().ok())
            .map(|s| s.to_string())
            .unwrap_or_else(|| "Unknown".to_string());

        // Get user ID from extensions if available
        let user_id = req.extensions().get::<String>().cloned();

        // Log request start
        log::info!(
            "[{}] {} {} {} - User-Agent: {} - IP: {}",
            request_id,
            method,
            path,
            if query.is_empty() { String::new() } else { format!("?{query}") },
            user_agent,
            remote_addr.as_deref().unwrap_or("Unknown")
        );

        Box::pin(async move {
            let result = service.call(req).await;
            let duration = start_time.elapsed();
            let duration_ms = duration.as_secs_f64() * 1000.0;

            match &result {
                Ok(response) => {
                    let status_code = response.status().as_u16();

                    // Use LoggerService for structured logging
                    LoggerService::log_request(
                        &method,
                        &path,
                        status_code,
                        duration_ms,
                        remote_addr.as_deref(),
                        Some(&user_agent),
                        user_id.as_deref(),
                    );

                    log::info!(
                        "[{}] Response: {} - Duration: {:.2}ms",
                        request_id,
                        status_code,
                        duration_ms
                    );
                },
                Err(error) => {
                    log::error!(
                        "[{}] Error: {} - Duration: {:.2}ms",
                        request_id,
                        error,
                        duration_ms
                    );
                }
            }

            result
        })
    }
}
