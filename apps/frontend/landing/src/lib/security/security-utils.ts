export class SecurityUtils {
  static readonly SUSPICIOUS_PATTERNS = [
    /<script[\s\S]*?>[\s\S]*?<\/script>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /<iframe[\s\S]*?>[\s\S]*?<\/iframe>/gi,
    /eval\s*\(/gi,
    /document\.cookie/gi,
    /document\.write/gi,
  ];

  static sanitizeInput(input: string): string {
    if (typeof input !== 'string') return '';

    return input
      .trim()
      .replace(/[<>]/g, '') // Remove angle brackets
      .replace(/javascript:/gi, '') // Remove javascript protocol
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .substring(0, 1000); // Limit length
  }

  static detectSuspiciousContent(input: string): boolean {
    if (typeof input !== 'string') return false;

    return this.SUSPICIOUS_PATTERNS.some((pattern) => pattern.test(input));
  }

  static validateCSRFToken(): boolean {
    if (typeof window === 'undefined') return true;

    // Basic referrer check
    const referrer = document.referrer;
    const currentOrigin = window.location.origin;

    if (referrer && !referrer.startsWith(currentOrigin)) {
      console.warn('Potential CSRF attack detected');
      return false;
    }

    return true;
  }

  static generateNonce(): string {
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    return Array.from(array, (byte) => byte.toString(16).padStart(2, '0')).join(
      '',
    );
  }

  static isSecureContext(): boolean {
    if (typeof window === 'undefined') return true;

    return (
      window.location.protocol === 'https:' ||
      window.location.hostname === 'localhost' ||
      window.location.hostname === '127.0.0.1'
    );
  }

  static checkPasswordStrength(password: string): {
    score: number;
    feedback: string[];
    isStrong: boolean;
  } {
    const feedback: string[] = [];
    let score = 0;

    if (password.length >= 8) {
      score += 1;
    } else {
      feedback.push('رمز عبور باید حداقل 8 کاراکتر باشد');
    }

    if (/[a-z]/.test(password)) {
      score += 1;
    } else {
      feedback.push('رمز عبور باید شامل حروف کوچک باشد');
    }

    if (/[A-Z]/.test(password)) {
      score += 1;
    } else {
      feedback.push('رمز عبور باید شامل حروف بزرگ باشد');
    }

    if (/\d/.test(password)) {
      score += 1;
    } else {
      feedback.push('رمز عبور باید شامل اعداد باشد');
    }

    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      score += 1;
    } else {
      feedback.push('رمز عبور باید شامل کاراکترهای خاص باشد');
    }

    const isStrong = score >= 4;

    return { score, feedback, isStrong };
  }

  static isCommonPassword(password: string): boolean {
    const commonPasswords = [
      'password',
      '123456',
      '123456789',
      'qwerty',
      'abc123',
      'password123',
      'admin',
      '12345678',
      'letmein',
      'welcome',
    ];

    return commonPasswords.includes(password.toLowerCase());
  }

  static logSecurityEvent(event: string, details?: any): void {
    if (process.env.NODE_ENV === 'production') {
      // In production, you might want to send this to your security monitoring service
      console.warn('Security Event:', event, details);
    } else {
      console.log('Security Event:', event, details);
    }
  }

  static checkEnvironment(): {
    isSecure: boolean;
    issues: string[];
  } {
    const issues: string[] = [];

    if (!this.isSecureContext()) {
      issues.push('غیرامن - اتصال HTTPS مورد نیاز است');
    }

    if (typeof window !== 'undefined') {
      // Check for development mode indicators
      if (
        window.location.hostname !== 'localhost' &&
        window.location.hostname !== '127.0.0.1' &&
        process.env.NODE_ENV === 'development'
      ) {
        issues.push('حالت توسعه در محیط غیرمحلی');
      }

      // Check for debug mode
      if ((window as any).debug || (window as any).DEBUG) {
        issues.push('حالت دیباگ فعال است');
      }
    }

    return {
      isSecure: issues.length === 0,
      issues,
    };
  }

  static clearSensitiveData(): void {
    if (typeof window === 'undefined') return;

    try {
      // Clear any potentially sensitive form data
      const forms = document.querySelectorAll('form');
      forms.forEach((form) => {
        const inputs = form.querySelectorAll(
          'input[type="password"], input[type="email"]',
        );
        inputs.forEach((input: Element) => {
          (input as HTMLInputElement).value = '';
        });
      });

      // Clear clipboard if possible (limited browser support)
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText('').catch(() => {
          // Ignore errors
        });
      }
    } catch (error) {
      console.warn('Could not clear sensitive data:', error);
    }
  }
}
