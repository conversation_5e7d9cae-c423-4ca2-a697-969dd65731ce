'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/core/utils';
import * as LucideIcons from 'lucide-react';

interface SidebarMenuItemProps {
  label: string;
  icon: string;
  isActive?: boolean;
  onClick: () => void;
  className?: string;
  badge?: string;
  isCollapsed?: boolean;
}

export function SidebarMenuItem({
  label,
  icon,
  isActive = false,
  onClick,
  className,
  badge,
  isCollapsed = false,
}: SidebarMenuItemProps) {
  const IconComponent = (LucideIcons as any)[icon] || LucideIcons.Square;

  return (
    <Button
      variant={isActive ? 'default' : 'ghost'}
      className={cn(
        'w-full justify-start gap-3 text-right font-normal relative',
        isActive
          ? 'bg-primary text-primary-foreground shadow-sm'
          : 'text-muted-foreground hover:text-foreground hover:bg-accent',
        isCollapsed && 'justify-center',
        className,
      )}
      onClick={onClick}
    >
      <IconComponent className="h-4 w-4" />
      {!isCollapsed && <span className="flex-1">{label}</span>}
      {!isCollapsed && badge && (
        <Badge variant="destructive" className="text-xs px-1.5 py-0.5">
          {badge}
        </Badge>
      )}
      {isCollapsed && badge && (
        <Badge
          variant="destructive"
          className="absolute -top-1 -right-1 text-xs px-1.5 py-0.5 min-w-[18px] h-[18px] flex items-center justify-center"
        >
          {badge}
        </Badge>
      )}
    </Button>
  );
}
