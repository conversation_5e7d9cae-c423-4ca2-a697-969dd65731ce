use diesel_async::{AsyncPgConnection, pooled_connection::{AsyncDieselConnectionManager, bb8::Pool}};
use crate::common::config::DatabaseConfig;

pub type DbPool = Pool<AsyncPgConnection>;

#[derive(Clone)]
pub struct DatabaseConnection {
    pub pool: DbPool,
}

impl DatabaseConnection {
    pub async fn new(config: &DatabaseConfig) -> Self {
        let manager = AsyncDieselConnectionManager::<AsyncPgConnection>::new(&config.url);

        let pool = Pool::builder()
            .max_size(config.max_connections)
            .build(manager)
            .await
            .expect("Failed to create connection pool");

        Self { pool }
    }

    #[allow(dead_code)]
    pub fn get_pool(&self) -> &DbPool {
        &self.pool
    }
}
