use std::env;

#[derive(<PERSON><PERSON>, Debug)]
#[allow(dead_code)]
pub struct SmsConfig {
    pub kavenegar_api_key: String,
    pub default_sender: Option<String>,
    pub otp_template: String,
}

impl SmsConfig {
    pub fn from_env() -> Self {
        Self {
            kavenegar_api_key: env::var("KAVENEGAR_API_KEY")
                .expect("KAVENEGAR_API_KEY must be set in environment variables"),
            default_sender: env::var("SMS_DEFAULT_SENDER").ok(),
            otp_template: env::var("SMS_OTP_TEMPLATE")
                .expect("SMS_OTP_TEMPLATE must be set in environment variables"),
        }
    }
}
