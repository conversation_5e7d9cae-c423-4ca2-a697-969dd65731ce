use std::env;
use super::{database_config::DatabaseConfig, redis_config::RedisConfig, jwt_config::JwtConfig, sms_config::SmsConfig, google_oauth_config::GoogleOAuthConfig};

#[derive(Clone)]
#[allow(dead_code)]
pub struct AppConfig {
    pub database: DatabaseConfig,
    pub redis: RedisConfig,
    pub jwt: JwtConfig,
    pub sms: SmsConfig,
    pub google_oauth: GoogleOAuthConfig,
    pub server_host: String,
    pub server_port: u16,
    pub cors_allowed_origins: Vec<String>,
    pub log_level: String,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self::new()
    }
}

impl AppConfig {
    pub fn new() -> Self {
        Self {
            database: DatabaseConfig::new(),
            redis: RedisConfig::new(),
            jwt: JwtConfig::new(),
            sms: SmsConfig::from_env(),
            google_oauth: GoogleOAuthConfig::from_env(),
            server_host: env::var("SERVER_HOST").unwrap_or_else(|_| "127.0.0.1".to_string()),
            server_port: env::var("SERVER_PORT")
                .unwrap_or_else(|_| "8080".to_string())
                .parse()
                .expect("Invalid port number"),
            cors_allowed_origins: env::var("CORS_ALLOWED_ORIGINS")
                .unwrap_or_else(|_| "http://localhost:3000".to_string())
                .split(',')
                .map(|s| s.trim().to_string())
                .collect(),
            log_level: env::var("LOG_LEVEL").unwrap_or_else(|_| "info".to_string()),
        }
    }
}
