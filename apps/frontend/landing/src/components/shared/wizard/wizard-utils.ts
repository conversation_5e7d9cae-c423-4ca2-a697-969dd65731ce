import {
  WizardStep,
  WizardStepContext,
  WizardStepData,
  WizardValidationResult,
} from './wizard-types';

export const normalizeValidationResult = (
  result: WizardValidationResult,
): string[] => {
  if (result === true) return [];
  if (typeof result === 'string') return [result];
  return result;
};

export const createStepContext = <T = Record<string, any>>(
  stepIndex: number,
  totalSteps: number,
  wizardId: string,
  allStepData: Record<number, WizardStepData>,
  validationErrors: Record<number, string[]>,
): WizardStepContext<T> => ({
  stepIndex,
  totalSteps,
  wizardId,
  allStepData,
  currentStepData: (allStepData[stepIndex] || {}) as T,
  isFirstStep: stepIndex === 0,
  isLastStep: stepIndex === totalSteps - 1,
  isValid: !validationErrors[stepIndex]?.length,
  validationErrors: validationErrors[stepIndex] || [],
});

export const getVisibleSteps = (
  steps: WizardStep[],
  allStepData: Record<number, WizardStepData>,
  wizardId: string,
): { step: WizardStep; originalIndex: number }[] => {
  return steps
    .map((step, index) => ({ step, originalIndex: index }))
    .filter(({ step, originalIndex }) => {
      if (!step.isVisible) return true;

      const context = createStepContext(
        originalIndex,
        steps.length,
        wizardId,
        allStepData,
        {},
      );

      return step.isVisible(context);
    });
};

export const checkStepDependencies = (
  step: WizardStep,
  allStepData: Record<number, WizardStepData>,
  steps: WizardStep[],
): boolean => {
  if (!step.dependencies?.length) return true;

  return step.dependencies.every((dep) => {
    const depStepIndex = steps.findIndex((s) => s.id === dep.stepId);
    if (depStepIndex === -1) return true;

    const depStepData = allStepData[depStepIndex];
    return dep.condition(depStepData || {});
  });
};

export const validateAllSteps = async (
  steps: WizardStep[],
  allStepData: Record<number, WizardStepData>,
  wizardId: string,
): Promise<Record<number, string[]>> => {
  const errors: Record<number, string[]> = {};

  await Promise.all(
    steps.map(async (step, index) => {
      if (step.validate) {
        const context = createStepContext(
          index,
          steps.length,
          wizardId,
          allStepData,
          {},
        );
        const stepData = allStepData[index] || {};

        try {
          const result = await step.validate(stepData, context);
          errors[index] = normalizeValidationResult(result);
        } catch (error) {
          errors[index] = [
            error instanceof Error ? error.message : 'Validation error',
          ];
        }
      } else {
        errors[index] = [];
      }
    }),
  );

  return errors;
};

export const canNavigateToStep = (
  targetStep: number,
  currentStep: number,
  completedSteps: Set<number>,
  allowStepSkipping: boolean,
  steps: WizardStep[],
): boolean => {
  if (targetStep < 0 || targetStep >= steps.length) return false;
  if (currentStep < 0 || currentStep >= steps.length) return false;

  if (targetStep === currentStep) return true;

  if (targetStep < currentStep) return true;

  if (allowStepSkipping) return true;

  if (targetStep === currentStep + 1) {
    return true;
  }

  return false;
};

export const getStepProgress = (
  currentStep: number,
  totalSteps: number,
  completedSteps: Set<number>,
): {
  current: number;
  total: number;
  percentage: number;
  completed: number;
} => {
  if (totalSteps <= 0) {
    return { current: 0, total: 0, percentage: 0, completed: 0 };
  }

  const completed = completedSteps.size;
  const current = Math.min(Math.max(currentStep + 1, 0), totalSteps);
  const percentage = Math.round((completed / totalSteps) * 100);

  return {
    current,
    total: totalSteps,
    percentage,
    completed,
  };
};

export const createWizardFormUtils = <T extends Record<string, any>>() => {
  const createFieldUpdater = (
    onDataChange: (data: Partial<T> | ((prev: T) => T)) => void,
  ) => {
    return <K extends keyof T>(field: K) =>
      (value: T[K]) => {
        onDataChange((prev) => ({ ...prev, [field]: value }));
      };
  };

  const createMultiFieldUpdater = (
    onDataChange: (data: Partial<T> | ((prev: T) => T)) => void,
  ) => {
    return (fields: Partial<T>) => {
      onDataChange((prev) => ({ ...prev, ...fields }));
    };
  };

  const createFieldValidator = <K extends keyof T>(
    validators: Partial<Record<K, (value: T[K]) => string | null>>,
  ) => {
    return (data: T): string[] => {
      const errors: string[] = [];

      (Object.keys(validators) as K[]).forEach((field) => {
        const validator = validators[field];
        if (validator) {
          const error = validator(data[field]);
          if (error) errors.push(error);
        }
      });

      return errors;
    };
  };

  return {
    createFieldUpdater,
    createMultiFieldUpdater,
    createFieldValidator,
  };
};

export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number,
): ((...args: Parameters<T>) => void) & { cancel: () => void } => {
  let timeoutId: NodeJS.Timeout;

  const debouncedFn = (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };

  debouncedFn.cancel = () => {
    clearTimeout(timeoutId);
  };

  return debouncedFn;
};
