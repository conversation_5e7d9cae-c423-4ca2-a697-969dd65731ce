use actix_web::web;
use crate::common::CommonModule;
use super::auth_controller::AuthController;

pub struct AuthModule;

impl AuthModule {
    pub fn configure(cfg: &mut web::ServiceConfig, common_module: &CommonModule) {
        let auth_controller = AuthController::new(common_module);

        cfg.service(
            web::scope("/api/auth")
                .app_data(web::Data::new(auth_controller))
                .route("/login", web::post().to(AuthController::login))
                .route("/register", web::post().to(AuthController::register))
                .route("/refresh", web::post().to(AuthController::refresh_token))
                .route("/logout", web::post().to(AuthController::logout))
                .route("/send-otp", web::post().to(AuthController::send_otp))
                .route("/verify-otp", web::post().to(AuthController::verify_otp))
                .route("/phone-login", web::post().to(AuthController::phone_login))
                .route("/google/url", web::get().to(AuthController::google_auth_url))
                .route("/google/callback", web::get().to(AuthController::google_callback_redirect))
                .route("/google/callback", web::post().to(AuthController::google_callback))
        );
    }
}
