import { Components } from '@/components/registry';
import {
  BookOpen,
  Briefcase,
  Home,
  Phone,
  ShoppingCart,
  Star,
} from 'lucide-react';
import React from 'react';

interface NavigationProps {
  className?: string;
}

const Navigation: React.FC<NavigationProps> = ({ className }) => {
  const navItems = [
    { href: '/', label: 'خانه', icon: Home },
    { href: '/services', label: 'خدمات', icon: Briefcase },
    { href: '/blog', label: 'بلاگ', icon: BookOpen },
    { href: '/shop', label: 'فروشگاه', icon: ShoppingCart },
    { href: '#features', label: 'مزایا', icon: Star },
    { href: '/contact', label: 'تماس با ما', icon: Phone },
  ];

  return (
    <div className="flex flex-col md:flex-row items-center justify-center gap-2 md:gap-4 w-full">
      {navItems.map((item) => (
        <Components.NavItem key={item.href} href={item.href}>
          <span className="flex-shrink-0 text-sm font-medium">
            {item.label}
          </span>
          <item.icon className="w-4 h-4 flex-shrink-0" />
        </Components.NavItem>
      ))}
    </div>
  );
};

export default Navigation;
