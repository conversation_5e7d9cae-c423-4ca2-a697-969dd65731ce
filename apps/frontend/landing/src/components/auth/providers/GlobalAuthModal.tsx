'use client';

import { Components } from '@/components/registry';
import { AuthModalProvider, useAuthModal } from '@/hooks/auth/use-auth-modal';
import { useSearchParams } from 'next/navigation';
import { ReactNode, useEffect } from 'react';

function AuthModalContainer() {
  const { isOpen, openModal, closeModal } = useAuthModal();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Auto-open modal if there are auth tokens or errors in the URL
    const hasAuthTokens =
      searchParams.get('access_token') && searchParams.get('refresh_token');
    const hasAuthError = searchParams.get('error');

    if ((hasAuthTokens || hasAuthError) && !isOpen) {
      openModal();
    }
  }, [searchParams, openModal, isOpen]);

  return <Components.AuthModal isOpen={isOpen} onClose={closeModal} />;
}

export function GlobalAuthModal({ children }: { children: ReactNode }) {
  return (
    <AuthModalProvider>
      {children}
      <AuthModalContainer />
    </AuthModalProvider>
  );
}
