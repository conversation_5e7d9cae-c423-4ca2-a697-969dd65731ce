import { Components } from '@/components/registry';
import {
  Bot,
  Briefcase,
  Code2,
  Headphones,
  LayoutDashboard,
  Palette,
  Server,
  ShoppingCart,
  Smartphone,
} from 'lucide-react';
import React from 'react';

interface ServicesDetailProps {
  className?: string;
}

const ServicesDetail: React.FC<ServicesDetailProps> = ({ className }) => {
  const services = [
    {
      icon: Code2,
      title: 'توسعه وب‌سایت',
      description:
        'طراحی و توسعه وب‌سایت‌های مدرن و واکنش‌گرا با بهترین تکنولوژی‌ها',
      features: [
        'طراحی واکنش‌گرا (Responsive Design)',
        'بهینه‌سازی برای موتورهای جستجو (SEO)',
        'سرعت بارگذاری بالا',
        'امنیت پیشرفته',
        'پنل مدیریت کاربرپسند',
      ],
      technologies: ['React', 'Next.js', 'TypeScript', 'Tailwind CSS'],
    },
    {
      icon: Smartphone,
      title: 'اپلیکیشن موبایل',
      description: 'توسعه اپلیکیشن‌های موبایل حرفه‌ای برای iOS و Android',
      features: [
        'طراحی UI/UX حرفه‌ای',
        'سازگاری با همه دستگاه‌ها',
        'عملکرد بهینه و سریع',
        'یکپارچگی با سرویس‌های وب',
        'پشتیبانی و به‌روزرسانی',
      ],
      technologies: ['React Native', 'Flutter', 'Swift', 'Kotlin'],
    },
    {
      icon: Server,
      title: 'توسعه بک‌اند',
      description: 'سرویس‌های بک‌اند قدرتمند و مقیاس‌پذیر برای کسب و کار شما',
      features: [
        'API های RESTful و GraphQL',
        'پایگاه داده بهینه‌شده',
        'احراز هویت و امنیت',
        'مقیاس‌پذیری بالا',
        'مانیتورینگ و لاگ‌گیری',
      ],
      technologies: ['Node.js', 'Python', 'Rust', 'PostgreSQL', 'Redis'],
    },
    {
      icon: ShoppingCart,
      title: 'فروشگاه آنلاین',
      description: 'راه‌اندازی فروشگاه آنلاین کامل با امکانات پیشرفته',
      features: [
        'سیستم مدیریت محصولات',
        'درگاه پرداخت امن',
        'مدیریت سفارشات',
        'سیستم موجودی',
        'گزارش‌گیری فروش',
      ],
      technologies: ['Shopify', 'WooCommerce', 'Magento', 'Custom Solution'],
    },
    {
      icon: LayoutDashboard,
      title: 'داشبورد مدیریتی',
      description:
        'ایجاد داشبوردهای مدیریتی سفارشی برای نظارت و مدیریت کسب و کار',
      features: [
        'گزارش‌گیری و تحلیل داده‌ها',
        'مدیریت کاربران و دسترسی‌ها',
        'نظارت بر عملکرد سیستم',
        'پشتیبانی از چندین زبان',
        'یکپارچگی با سرویس‌های دیگر',
      ],
      technologies: ['React', 'Vue.js', 'Angular', 'Django'],
    },
    {
      icon: Bot,
      title: 'هوش مصنوعی',
      description:
        'پیاده‌سازی راهکارهای هوش مصنوعی برای بهبود عملکرد کسب و کار',
      features: [
        'چت‌بات هوشمند',
        'تحلیل داده با AI',
        'تشخیص تصویر',
        'پردازش زبان طبیعی',
        'پیش‌بینی و تحلیل ترند',
      ],
      technologies: ['OpenAI', 'TensorFlow', 'PyTorch', 'Langchain'],
    },
    {
      icon: Palette,
      title: 'طراحی UI/UX',
      description: 'طراحی رابط کاربری زیبا و تجربه کاربری بی‌نظیر',
      features: [
        'تحقیق و تحلیل کاربران',
        'طراحی واکنش‌گرا',
        'پروتوتایپ تعاملی',
        'تست کاربری',
        'راهنمای طراحی (Design System)',
      ],
      technologies: ['Figma', 'Adobe XD', 'Sketch', 'Principle'],
    },
    {
      icon: Headphones,
      title: 'پشتیبانی فنی',
      description: 'پشتیبانی مستمر و حل مشکلات فنی سیستم‌های شما',
      features: [
        'پشتیبانی 24/7',
        'حل سریع مشکلات',
        'نگهداری پیشگیرانه',
        'به‌روزرسانی سیستم‌ها',
        'مشاوره فنی',
      ],
      technologies: ['Remote Support', 'Monitoring Tools', 'Ticketing System'],
    },
    {
      icon: Briefcase,
      title: 'مشاوره IT',
      description: 'مشاوره استراتژیک فناوری اطلاعات برای رشد کسب و کار',
      features: [
        'بررسی وضعیت فعلی',
        'طراحی استراتژی IT',
        'انتخاب تکنولوژی مناسب',
        'برنامه‌ریزی پیاده‌سازی',
        'نظارت بر اجرا',
      ],
      technologies: [
        'Strategic Planning',
        'Technology Assessment',
        'Best Practices',
      ],
    },
  ];

  return (
    <section
      id="services-detail"
      className={`py-12 lg:py-16 bg-gradient-to-br from-background via-card/10 to-secondary/5 relative overflow-hidden ${className || ''}`}
    >
      <div className="absolute -top-40 -left-40 w-80 h-80 rounded-full bg-primary/5 mix-blend-screen filter blur-3xl opacity-70"></div>
      <div className="absolute -bottom-20 right-20 w-64 h-64 rounded-full bg-accent/5 mix-blend-screen filter blur-3xl opacity-60"></div>

      <div className="container relative z-10 mx-auto px-4">
        <div className="text-center mb-12">
          <div className="inline-block mb-4 px-4 py-2 bg-card/80 text-primary rounded-lg text-sm font-mono border border-primary/20">
            <span className="text-accent">export</span> {'{ AllServices } from'}{' '}
            <span className="text-primary">'@/dolfak'</span>
          </div>
          <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-4 font-mono text-foreground">
            خدمات تخصصی ما
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            با بیش از ۵ سال تجربه در صنعت فناوری، تیم متخصص ما آماده ارائه
            راهکارهای نوآورانه و کیفی برای کسب و کار شما است
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 lg:gap-8">
          {services.map((service, index) => (
            <Components.ServiceDetailCard
              key={index}
              icon={service.icon}
              title={service.title}
              description={service.description}
              features={service.features}
              technologies={service.technologies}
            />
          ))}
        </div>

        <div className="mt-16 text-center">
          <div className="bg-card/80 backdrop-blur-sm border border-primary/20 rounded-2xl p-8 max-w-4xl mx-auto">
            <h3 className="text-xl font-bold text-primary font-mono mb-4">
              سرویس مورد نظر خود را نیافتید؟
            </h3>
            <p className="text-muted-foreground mb-6 leading-relaxed">
              تیم ما آماده بررسی نیازهای خاص شما و ارائه راهکارهای سفارشی است.
              با ما تماس بگیرید و درباره پروژه خود صحبت کنید.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/contact"
                className="inline-flex items-center justify-center px-6 py-3 bg-primary text-background rounded-lg font-mono text-sm hover:bg-primary/90 transition-colors duration-300 shadow-lg hover:shadow-xl"
              >
                تماس با ما
              </a>
              <a
                href="tel:+989123456789"
                className="inline-flex items-center justify-center px-6 py-3 border border-primary/30 text-primary rounded-lg font-mono text-sm hover:bg-primary/10 transition-colors duration-300"
              >
                ۰۹۱۱-۲۱۹-۱۷۴۶
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ServicesDetail;
