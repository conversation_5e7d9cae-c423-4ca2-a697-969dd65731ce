import api from '../core/api';
import {
  PaginatedResponse,
  PaginationParams,
  WizardSubmissionRequest,
  WizardSubmissionResponse,
} from './types';

export const submitWizard = async (
  payload: WizardSubmissionRequest,
): Promise<WizardSubmissionResponse> => {
  const { data } = await api.post<WizardSubmissionResponse>(
    '/wizard/submit',
    payload,
  );
  return data;
};

export const getWizardSubmissions = async (
  params?: PaginationParams,
): Promise<PaginatedResponse<WizardSubmissionResponse>> => {
  const { data } = await api.get<PaginatedResponse<WizardSubmissionResponse>>(
    '/wizard/submissions',
    { params },
  );
  return data;
};

export const getWizardSubmissionById = async (
  id: string,
): Promise<WizardSubmissionResponse> => {
  const { data } = await api.get<WizardSubmissionResponse>(
    `/wizard/submissions/${id}`,
  );
  return data;
};
