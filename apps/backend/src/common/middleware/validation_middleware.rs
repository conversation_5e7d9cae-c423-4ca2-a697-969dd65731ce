use actix_web::{
    dev::{forward_ready, Service, ServiceRequest, ServiceResponse, Transform},
    Error,
    http::header::{CONTENT_TYPE, CONTENT_LENGTH},
};
use futures_util::future::LocalBoxFuture;
use std::future::{ready, Ready};
use std::rc::Rc;

/// Request validation middleware
pub struct ValidationMiddleware;

impl<S, B> Transform<S, ServiceRequest> for ValidationMiddleware
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Transform = ValidationMiddlewareService<S>;
    type InitError = ();
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ready(Ok(ValidationMiddlewareService {
            service: Rc::new(service),
        }))
    }
}

pub struct ValidationMiddlewareService<S> {
    service: Rc<S>,
}

impl<S, B> Service<ServiceRequest> for ValidationMiddlewareService<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Future = LocalBoxFuture<'static, Result<Self::Response, Self::Error>>;

    forward_ready!(service);

    fn call(&self, req: ServiceRequest) -> Self::Future {
        let service = Rc::clone(&self.service);

        Box::pin(async move {
            // Validate content type for POST/PUT requests
            let method = req.method();
            if method == actix_web::http::Method::POST || method == actix_web::http::Method::PUT {
                if let Some(content_type) = req.headers().get(CONTENT_TYPE) {
                    if let Ok(content_type_str) = content_type.to_str() {
                        if !content_type_str.starts_with("application/json") &&
                           !content_type_str.starts_with("multipart/form-data") {
                            return Err(actix_web::error::ErrorBadRequest(
                                "Invalid content type. Expected application/json or multipart/form-data"
                            ));
                        }
                    }
                } else {
                    return Err(actix_web::error::ErrorBadRequest(
                        "Content-Type header is required for POST/PUT requests"
                    ));
                }

                // Validate content length
                if let Some(content_length) = req.headers().get(CONTENT_LENGTH) {
                    if let Ok(length_str) = content_length.to_str() {
                        if let Ok(length) = length_str.parse::<u64>() {
                            // 10MB limit
                            if length > 10_485_760 {
                                return Err(actix_web::error::ErrorBadRequest(
                                    "Request body too large. Maximum size is 10MB"
                                ));
                            }
                        }
                    }
                }
            }

            service.call(req).await
        })
    }
}
