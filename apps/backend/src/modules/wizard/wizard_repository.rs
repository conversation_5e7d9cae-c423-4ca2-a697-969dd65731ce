use diesel::prelude::*;
use diesel_async::RunQueryDsl;
use uuid::Uuid;
use crate::common::{CommonModule, errors::AppError, database::connection::DbPool, utils::pagination::PaginationParams};
use crate::schema::wizard_submissions::dsl::*;
use super::entities::{WizardSubmission, WizardSubmissionStatus};
use super::dto::CreateWizardSubmissionDto;

pub struct WizardRepository {
    pool: DbPool,
}

impl WizardRepository {
    pub fn new(common_module: &CommonModule) -> Self {
        Self {
            pool: common_module.database.pool.clone(),
        }
    }

    pub async fn create(&self, dto: CreateWizardSubmissionDto) -> Result<WizardSubmission, AppError> {
        let new_submission = WizardSubmission::create_new(
            dto.full_name,
            dto.email,
            dto.phone,
            dto.project_type,
            dto.description,
        );

        let mut conn = self.pool.get().await?;

        let result = diesel::insert_into(wizard_submissions)
            .values(&new_submission)
            .get_result::<WizardSubmission>(&mut conn)
            .await?;

        Ok(result)
    }

    pub async fn find_paginated(&self, params: &PaginationParams) -> Result<(Vec<WizardSubmission>, i64), AppError> {
        let mut conn = self.pool.get().await?;

        let total: i64 = wizard_submissions.count().get_result(&mut conn).await?;

        let results = wizard_submissions
            .order(created_at.desc())
            .limit(params.get_limit())
            .offset(params.get_offset())
            .load::<WizardSubmission>(&mut conn)
            .await?;

        Ok((results, total))
    }

    pub async fn find_by_id(&self, submission_id_str: &str) -> Result<Option<WizardSubmission>, AppError> {
        let submission_id = Uuid::parse_str(submission_id_str)
            .map_err(|_| AppError::BadRequest("Invalid submission ID format".to_string()))?;

        let mut conn = self.pool.get().await?;

        let result = wizard_submissions
            .filter(id.eq(submission_id))
            .first::<WizardSubmission>(&mut conn)
            .await
            .optional()?;

        Ok(result)
    }

    pub async fn update_status(&self, submission_id_str: &str, new_status: WizardSubmissionStatus) -> Result<WizardSubmission, AppError> {
        let submission_id = Uuid::parse_str(submission_id_str)
            .map_err(|_| AppError::BadRequest("Invalid submission ID format".to_string()))?;

        let mut conn = self.pool.get().await?;

        use chrono::Utc;
        let now = Utc::now();

        let result = diesel::update(wizard_submissions.filter(id.eq(submission_id)))
            .set((
                status.eq(new_status.to_string()),
                updated_at.eq(now),
            ))
            .get_result::<WizardSubmission>(&mut conn)
            .await
            .optional()?
            .ok_or_else(|| AppError::NotFound("Wizard submission not found".to_string()))?;

        Ok(result)
    }
}


