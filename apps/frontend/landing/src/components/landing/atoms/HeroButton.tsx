import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON><PERSON>, Rocket } from 'lucide-react';

interface Props {
  handleStartWizard: () => void;
}
export default function HeroButton({ handleStartWizard }: Props) {
  return (
    <Button
      onClick={handleStartWizard}
      className=" transition-all duration-300 hover:scale-105 group neo-brutal text-green-200! bg-gradient-to-r from-primary to-accent hover:from-primary/80 hover:to-accent/80 shadow-sm font-mono text-[clamp(0.4rem,3vw,0.8rem)]!"
    >
      <div className="flex items-center justify-center gap-2">
        <Rocket className="w-4 h-4 sm:w-5 sm:h-5 group-hover:animate-pulse" />
        <span>شروع کنید</span>
        <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5 group-hover:-translate-x-1 transition-transform" />
      </div>
    </Button>
  );
}
