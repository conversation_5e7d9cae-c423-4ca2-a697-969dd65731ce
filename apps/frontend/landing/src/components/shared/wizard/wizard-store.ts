import { atom } from 'jotai';
import { atomWithStorage } from 'jotai/utils';
import { WizardState, WizardStepData } from './wizard-types';

const createInitialWizardState = (): WizardState => ({
  currentStep: 0,
  stepData: {},
  isComplete: false,
  validationErrors: {},
  completedSteps: new Set(),
  touchedSteps: new Set(),
  isLoading: false,
  hasUnsavedChanges: false,
});

export const createWizardStore = (wizardId: string) => {
  const wizardAtom = atomWithStorage<WizardState>(
    `wizard-${wizardId}`,
    createInitialWizardState(),
    {
      getItem: (key, initialValue) => {
        try {
          const stored = localStorage.getItem(key);
          if (!stored) return initialValue;
          const parsed = JSON.parse(stored);
          return {
            ...parsed,
            completedSteps: new Set(parsed.completedSteps || []),
            touchedSteps: new Set(parsed.touchedSteps || []),
          };
        } catch {
          return initialValue;
        }
      },
      setItem: (key, value) => {
        const serialized = {
          ...value,
          completedSteps: Array.from(value.completedSteps || []),
          touchedSteps: Array.from(value.touchedSteps || []),
        };
        localStorage.setItem(key, JSON.stringify(serialized));
      },
      removeItem: (key) => localStorage.removeItem(key),
      subscribe: () => () => {},
    },
    { getOnInit: true },
  );

  const setCurrentStepAtom = atom(null, (get, set, step: number) => {
    const current = get(wizardAtom);
    const newTouchedSteps = new Set(current.touchedSteps);
    newTouchedSteps.add(step);

    set(wizardAtom, {
      ...current,
      currentStep: step,
      touchedSteps: newTouchedSteps,
      hasUnsavedChanges: true,
    });
  });

  const setStepDataAtom = atom(
    null,
    (
      get,
      set,
      stepIndex: number,
      data: WizardStepData | ((prev: WizardStepData) => WizardStepData),
    ) => {
      const current = get(wizardAtom);
      const currentStepData = current.stepData[stepIndex] || {};
      const newData =
        typeof data === 'function'
          ? data(currentStepData)
          : { ...currentStepData, ...data };

      const updatedValidationErrors = { ...current.validationErrors };
      delete updatedValidationErrors[stepIndex];

      set(wizardAtom, {
        ...current,
        stepData: {
          ...current.stepData,
          [stepIndex]: newData,
        },
        validationErrors: updatedValidationErrors,
        hasUnsavedChanges: true,
        lastSaveTime: Date.now(),
      });
    },
  );

  const setValidationErrorsAtom = atom(
    null,
    (get, set, stepIndex: number, errors: string[]) => {
      const current = get(wizardAtom);
      set(wizardAtom, {
        ...current,
        validationErrors: {
          ...current.validationErrors,
          [stepIndex]: errors,
        },
      });
    },
  );

  const markStepCompleteAtom = atom(null, (get, set, stepIndex: number) => {
    const current = get(wizardAtom);
    const newCompletedSteps = new Set(current.completedSteps);
    newCompletedSteps.add(stepIndex);

    set(wizardAtom, {
      ...current,
      completedSteps: newCompletedSteps,
    });
  });

  const setLoadingAtom = atom(null, (get, set, isLoading: boolean) => {
    const current = get(wizardAtom);
    set(wizardAtom, {
      ...current,
      isLoading,
    });
  });

  const setWizardCompleteAtom = atom(null, (get, set, isComplete: boolean) => {
    const current = get(wizardAtom);
    set(wizardAtom, {
      ...current,
      isComplete,
      hasUnsavedChanges: !isComplete,
    });
  });

  const resetWizardAtom = atom(null, (get, set) => {
    set(wizardAtom, createInitialWizardState());
  });

  const saveProgressAtom = atom(null, (get, set) => {
    const current = get(wizardAtom);
    set(wizardAtom, {
      ...current,
      hasUnsavedChanges: false,
      lastSaveTime: Date.now(),
    });
  });

  const clearWizardDataAtom = atom(null, (get, set) => {
    const wizardKey = `wizard-${wizardId}`;
    try {
      localStorage.removeItem(wizardKey);
    } catch {
      // Ignore localStorage errors
    }
    set(wizardAtom, createInitialWizardState());
  });

  return {
    wizardAtom,
    setCurrentStepAtom,
    setStepDataAtom,
    setValidationErrorsAtom,
    markStepCompleteAtom,
    setLoadingAtom,
    setWizardCompleteAtom,
    resetWizardAtom,
    saveProgressAtom,
    clearWizardDataAtom,
  };
};
