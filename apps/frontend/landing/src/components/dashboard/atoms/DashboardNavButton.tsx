'use client';

import { Button } from '@/components/ui/button';
import { getCurrentUserRole } from '@/lib/dashboard/auth';
import { Code2, User } from 'lucide-react';
import { useRouter } from 'next/navigation';

export function DashboardNavButton() {
  const router = useRouter();
  const userRole = getCurrentUserRole();

  if (!userRole) return null;

  const handleNavigateToDashboard = () => {
    const dashboardPath = userRole === 'buyer' ? '/buyers' : '/coders';
    router.push(dashboardPath);
  };

  return (
    <Button
      variant="outline"
      onClick={handleNavigateToDashboard}
      className="gap-2"
    >
      {userRole === 'buyer' ? (
        <User className="h-4 w-4" />
      ) : (
        <Code2 className="h-4 w-4" />
      )}
      <span>داشبورد من</span>
    </Button>
  );
}
