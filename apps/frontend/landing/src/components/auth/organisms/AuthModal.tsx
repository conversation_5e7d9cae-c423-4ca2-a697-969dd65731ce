'use client';

import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { useAuth } from '@/hooks/auth/use-auth';
import { useAuthModal } from '@/hooks/auth/use-auth-modal';
import { TokenManager } from '@/lib/auth/token-manager';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import LoginPage from './LoginPage';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function AuthModal({ isOpen, onClose }: AuthModalProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { authStatus, authError } = useAuthModal();
  const { refreshUser } = useAuth();
  const [localAuthStatus, setLocalAuthStatus] = useState<
    'idle' | 'processing' | 'success' | 'error'
  >('idle');
  const [localAuthError, setLocalAuthError] = useState<string | null>(null);

  // Check for auth tokens or errors in URL params
  const accessToken = searchParams.get('access_token');
  const refreshToken = searchParams.get('refresh_token');
  const urlError = searchParams.get('error');

  // Reset local state when modal opens (for fresh login attempts)
  useEffect(() => {
    if (isOpen) {
      setLocalAuthStatus('idle');
      setLocalAuthError(null);
    }
  }, [isOpen]);

  // Process tokens immediately when detected
  useEffect(() => {
    if (accessToken && refreshToken && localAuthStatus === 'idle') {
      setLocalAuthStatus('processing');

      // Clear URL params immediately to prevent loops
      const url = new URL(window.location.href);
      url.searchParams.delete('access_token');
      url.searchParams.delete('refresh_token');
      url.searchParams.delete('error');
      window.history.replaceState({}, '', url.toString());

      // Store tokens and refresh user
      try {
        TokenManager.setTokens(accessToken, refreshToken);
        refreshUser();
        setLocalAuthStatus('success');

        // Auto-close after 2 seconds
        setTimeout(() => {
          onClose();
          const redirectPath =
            localStorage.getItem('redirect_after_login') || '/';
          localStorage.removeItem('redirect_after_login');
          router.push(redirectPath);
        }, 2000);
      } catch (error) {
        setLocalAuthStatus('error');
        setLocalAuthError('خطا در ذخیره اطلاعات احراز هویت');
      }
    }
  }, [
    accessToken,
    refreshToken,
    localAuthStatus,
    refreshUser,
    onClose,
    router,
  ]);

  // Handle URL errors
  useEffect(() => {
    if (urlError && localAuthStatus === 'idle') {
      setLocalAuthStatus('error');
      setLocalAuthError(decodeURIComponent(urlError));

      // Clear URL params
      const url = new URL(window.location.href);
      url.searchParams.delete('error');
      window.history.replaceState({}, '', url.toString());
    }
  }, [urlError, localAuthStatus]);

  const handleSuccess = () => {
    const redirectPath = localStorage.getItem('redirect_after_login') || '/';
    localStorage.removeItem('redirect_after_login');
    onClose();
    router.push(redirectPath);
  };

  const renderContent = () => {
    // Handle local auth states (from URL processing)
    if (localAuthStatus === 'processing' || localAuthStatus === 'success') {
      return (
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="text-green-600 text-6xl mb-4">✓</div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              احراز هویت موفق بود!
            </h2>
            <p className="text-gray-600 mb-4">
              {localAuthStatus === 'processing'
                ? 'در حال پردازش...'
                : 'ورود موفقیت‌آمیز بود'}
            </p>
          </div>
        </div>
      );
    }

    if (localAuthStatus === 'error') {
      return (
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center max-w-md mx-auto px-4">
            <div className="text-red-600 text-6xl mb-4">✗</div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              احراز هویت ناموفق بود
            </h2>
            <p className="text-gray-600 mb-4">{localAuthError}</p>
            <button
              onClick={onClose}
              className="text-primary hover:text-primary/80 text-sm underline"
            >
              بستن
            </button>
          </div>
        </div>
      );
    }

    // Handle URL-based auth states (legacy - should not be reached)
    if (accessToken && refreshToken) {
      return (
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="text-green-600 text-6xl mb-4">✓</div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              احراز هویت موفق بود!
            </h2>
            <p className="text-gray-600 mb-4">در حال تکمیل ورود...</p>
            <button
              onClick={handleSuccess}
              className="bg-primary text-white px-4 py-2 rounded hover:bg-primary/80"
            >
              ادامه
            </button>
          </div>
        </div>
      );
    }

    if (urlError) {
      return (
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center max-w-md mx-auto px-4">
            <div className="text-red-600 text-6xl mb-4">✗</div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              احراز هویت ناموفق بود
            </h2>
            <p className="text-gray-600 mb-4">{decodeURIComponent(urlError)}</p>
            <button
              onClick={onClose}
              className="text-primary hover:text-primary/80 text-sm underline"
            >
              بستن
            </button>
          </div>
        </div>
      );
    }

    // Handle context-based auth states (from useAuthModal)
    if (authStatus === 'loading') {
      return (
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-gray-600">در حال تکمیل احراز هویت...</p>
          </div>
        </div>
      );
    }

    if (authStatus === 'success') {
      return (
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="text-green-600 text-6xl mb-4">✓</div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              احراز هویت موفق بود!
            </h2>
            <p className="text-gray-600">در حال انتقال...</p>
          </div>
        </div>
      );
    }

    if (authStatus === 'error') {
      return (
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center max-w-md mx-auto px-4">
            <div className="text-red-600 text-6xl mb-4">✗</div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              احراز هویت ناموفق بود
            </h2>
            <p className="text-gray-600 mb-4">
              {authError || 'خطا در احراز هویت'}
            </p>
            <button
              onClick={() => window.location.reload()}
              className="text-primary hover:text-primary/80 text-sm underline"
            >
              تلاش مجدد
            </button>
          </div>
        </div>
      );
    }

    return <LoginPage onSuccess={handleSuccess} />;
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-4xl w-[90vw] h-[80vh] overflow-y-auto p-0">
        <DialogTitle className="sr-only">احراز هویت</DialogTitle>
        <div className="p-6">{renderContent()}</div>
      </DialogContent>
    </Dialog>
  );
}
