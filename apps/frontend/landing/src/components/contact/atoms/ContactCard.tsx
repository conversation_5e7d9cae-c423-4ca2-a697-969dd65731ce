import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/core/utils';
import React from 'react';

interface ContactCardProps {
  title: string;
  children: React.ReactNode;
  className?: string;
}

const ContactCard: React.FC<ContactCardProps> = ({
  title,
  children,
  className,
}) => {
  return (
    <Card
      className={cn(
        'border-primary/20 hover:border-primary/40 transition-all duration-300',
        className,
      )}
    >
      <CardHeader className="mb-4">
        <CardTitle className="text-xl font-bold text-foreground">
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent className="flex-1">{children}</CardContent>
    </Card>
  );
};

export default ContactCard;
