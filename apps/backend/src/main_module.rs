use actix_web::{web};
use actix_cors::Cors;
use utoipa_swagger_ui::SwaggerUi;

use crate::common::CommonModule;
use crate::common::config::app_config::AppConfig;
use crate::common::swagger::SwaggerConfig;
use crate::common::middleware::AuthMiddleware;
use crate::modules::{auth::AuthModule, users::UsersModule, health::HealthModule, wizard::WizardModule};

#[derive(Clone)]
pub struct MainModule {
    pub common_module: CommonModule,
}

impl MainModule {
    pub async fn new() -> Self {
        let common_module = CommonModule::new().await;

        Self {
            common_module,
        }
    }

    pub fn configure(&self, cfg: &mut web::ServiceConfig) {
        let openapi = SwaggerConfig::create_docs();

        cfg
            // Swagger documentation
            .service(
                SwaggerUi::new("/swagger-ui/{_:.*}")
                    .url("/api-docs/openapi.json", openapi.clone())
            )

            // Public routes (no auth required)
            .configure(HealthModule::configure)
            .configure(|cfg| AuthModule::configure(cfg, &self.common_module))

            // Protected routes (auth required)
            .service(
                web::scope("/api")
                    .wrap(AuthMiddleware)
                    .configure(|cfg| UsersModule::configure(cfg, &self.common_module))
                    .configure(|cfg| WizardModule::configure(cfg, &self.common_module))
            );
    }    pub fn create_cors() -> Cors {
        let app_config = AppConfig::new();

        let mut cors = Cors::default()
            .allow_any_method()
            .allow_any_header()
            .max_age(3600)
            .supports_credentials();

        // Add allowed origins from config
        for origin in &app_config.cors_allowed_origins {
            cors = cors.allowed_origin(origin);
        }

        // Fallback for development
        if app_config.cors_allowed_origins.is_empty() {
            cors = cors.allowed_origin("http://localhost:3000")
                      .allowed_origin("http://localhost:3001")
                      .allowed_origin("http://localhost:3002");
        }

        cors
    }
}
