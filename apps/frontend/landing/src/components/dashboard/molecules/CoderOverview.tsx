'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, Code, DollarSign, Star } from 'lucide-react';
import { DashboardStatsCard } from '../atoms/DashboardStatsCard';

export function CoderOverview() {
  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <DashboardStatsCard
          title="پروژه‌های فعال"
          value="5"
          icon={<Code className="h-4 w-4" />}
          trend={{ value: 12, isPositive: true }}
        />
        <DashboardStatsCard
          title="تکمیل شده این ماه"
          value="8"
          icon={<CheckCircle className="h-4 w-4" />}
          trend={{ value: 25, isPositive: true }}
        />
        <DashboardStatsCard
          title="امتیاز"
          value="4.8"
          description="از ۵"
          icon={<Star className="h-4 w-4" />}
        />
        <DashboardStatsCard
          title="درآمد این ماه"
          value="۱۸۵,۰۰۰"
          description="تومان"
          icon={<DollarSign className="h-4 w-4" />}
          trend={{ value: 18, isPositive: true }}
        />
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="text-right">پروژه‌های فعال</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[
                {
                  title: 'طراحی سایت فروشگاهی',
                  deadline: '۵ روز',
                  progress: 75,
                  amount: '۴۵,۰۰۰',
                },
                {
                  title: 'اپلیکیشن موبایل',
                  deadline: '۱۲ روز',
                  progress: 40,
                  amount: '۸۰,۰۰۰',
                },
                {
                  title: 'سیستم مدیریت',
                  deadline: '۸ روز',
                  progress: 90,
                  amount: '۶۰,۰۰۰',
                },
              ].map((project, i) => (
                <div key={i} className="p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <div className="text-right">
                      <p className="font-medium">{project.title}</p>
                      <p className="text-sm text-muted-foreground">
                        {project.deadline} باقی‌مانده
                      </p>
                    </div>
                    <div className="text-left">
                      <p className="font-medium">{project.amount} تومان</p>
                      <p className="text-sm text-muted-foreground">
                        {project.progress}% تکمیل
                      </p>
                    </div>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div
                      className="bg-primary h-2 rounded-full transition-all duration-300"
                      style={{ width: `${project.progress}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="text-right">درخواست‌های جدید</CardTitle>
            <Button variant="outline" size="sm">
              مشاهده همه
            </Button>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[
                {
                  title: 'توسعه API',
                  budget: '۳۵,۰۰۰',
                  duration: '۱۵ روز',
                  skills: ['Node.js', 'Express'],
                },
                {
                  title: 'رفع باگ وبسایت',
                  budget: '۲۰,۰۰۰',
                  duration: '۷ روز',
                  skills: ['React', 'JavaScript'],
                },
                {
                  title: 'طراحی پنل ادمین',
                  budget: '۵۵,۰۰۰',
                  duration: '۲۰ روز',
                  skills: ['Vue.js', 'CSS'],
                },
              ].map((request, i) => (
                <div key={i} className="p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <div className="text-right">
                      <p className="font-medium">{request.title}</p>
                      <div className="flex gap-1 mt-1">
                        {request.skills.map((skill, j) => (
                          <span
                            key={j}
                            className="text-xs bg-primary/10 text-primary px-2 py-1 rounded"
                          >
                            {skill}
                          </span>
                        ))}
                      </div>
                    </div>
                    <div className="text-left">
                      <p className="font-medium">{request.budget} تومان</p>
                      <p className="text-sm text-muted-foreground">
                        {request.duration}
                      </p>
                    </div>
                  </div>
                  <Button variant="outline" size="sm" className="w-full mt-2">
                    مشاهده جزئیات
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
