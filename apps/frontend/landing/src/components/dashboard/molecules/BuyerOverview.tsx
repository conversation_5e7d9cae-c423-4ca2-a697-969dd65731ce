'use client';

import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { CheckCircle, Clock, DollarSign, FolderOpen } from 'lucide-react';
import { DashboardStatsCard } from '../atoms/DashboardStatsCard';

export function BuyerOverview() {
  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <DashboardStatsCard
          title="پروژه‌های فعال"
          value="12"
          icon={<FolderOpen className="h-4 w-4" />}
          trend={{ value: 15, isPositive: true }}
        />
        <DashboardStatsCard
          title="در انتظار تایید"
          value="3"
          icon={<Clock className="h-4 w-4" />}
          description="پروژه در انتظار"
        />
        <DashboardStatsCard
          title="تکمیل شده"
          value="28"
          icon={<CheckCircle className="h-4 w-4" />}
          trend={{ value: 8, isPositive: true }}
        />
        <DashboardStatsCard
          title="بودجه کل"
          value="۲۴۵,۰۰۰"
          description="تومان"
          icon={<DollarSign className="h-4 w-4" />}
        />
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="text-right">پروژه‌های اخیر</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[1, 2, 3].map((i) => (
                <div
                  key={i}
                  className="flex items-center justify-between p-3 bg-muted/50 rounded-lg"
                >
                  <div className="text-right">
                    <p className="font-medium">توسعه اپلیکیشن موبایل</p>
                    <p className="text-sm text-muted-foreground">
                      در حال انجام
                    </p>
                  </div>
                  <div className="text-left">
                    <p className="font-medium">۵۰,۰۰۰ تومان</p>
                    <p className="text-sm text-muted-foreground">
                      ۳ روز باقی‌مانده
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-right">توسعه‌دهندگان فعال</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[1, 2, 3].map((i) => (
                <div
                  key={i}
                  className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg"
                >
                  <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                    <span className="text-sm font-medium">م{i}</span>
                  </div>
                  <div className="flex-1 text-right">
                    <p className="font-medium">محمد علی زاده</p>
                    <p className="text-sm text-muted-foreground">
                      توسعه‌دهنده React
                    </p>
                  </div>
                  <div className="text-left">
                    <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
