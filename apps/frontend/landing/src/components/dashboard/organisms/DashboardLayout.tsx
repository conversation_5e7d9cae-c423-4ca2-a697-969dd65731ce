'use client';

import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/core/utils';
import { DashboardUser } from '@/lib/dashboard/auth';
import {
  currentSectionAtom,
  sidebarCollapsedAtom,
} from '@/lib/store/dashboard';
import { useAtom } from 'jotai';
import { useState } from 'react';
import { EmptyDashboardSection } from '../atoms/EmptyDashboardSection';
import { BuyerOverview } from '../molecules/BuyerOverview';
import { CoderOverview } from '../molecules/CoderOverview';
import { DashboardHeader } from '../molecules/DashboardHeader';
import { DashboardSidebar } from '../molecules/DashboardSidebar';

interface DashboardLayoutProps {
  user: DashboardUser;
}

export function DashboardLayout({ user }: DashboardLayoutProps) {
  const [currentSection] = useAtom(currentSectionAtom);
  const [sidebarCollapsed, setSidebarCollapsed] = useAtom(sidebarCollapsedAtom);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const renderContent = () => {
    if (currentSection === 'overview') {
      return user.role === 'buyer' ? <BuyerOverview /> : <CoderOverview />;
    }

    return <EmptyDashboardSection />;
  };

  return (
    <div className="h-screen flex overflow-hidden bg-background">
      <DashboardSidebar
        user={user}
        isCollapsed={sidebarCollapsed}
        className="hidden md:flex"
      />

      <div className="flex-1 flex flex-col overflow-hidden">
        <DashboardHeader
          user={user}
          onMenuToggle={() => setMobileMenuOpen(!mobileMenuOpen)}
        />

        <ScrollArea className="flex-1">
          <main className="p-6">{renderContent()}</main>
        </ScrollArea>
      </div>

      {mobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 md:hidden"
          onClick={() => setMobileMenuOpen(false)}
        />
      )}

      <div
        className={cn(
          'fixed left-0 top-0 h-full z-50 transform transition-transform duration-300 ease-in-out md:hidden',
          mobileMenuOpen ? 'translate-x-0' : '-translate-x-full',
        )}
      >
        <DashboardSidebar user={user} className="z-50 bg-white" />
      </div>
    </div>
  );
}
