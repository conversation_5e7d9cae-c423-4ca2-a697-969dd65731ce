use std::env;

#[derive(<PERSON>lone, Debug)]
#[allow(dead_code)]
pub struct GoogleOAuthConfig {
    pub client_id: String,
    pub client_secret: String,
    pub redirect_uri: String,
    pub auth_url: String,
    pub token_url: String,
    pub userinfo_url: String,
}

impl GoogleOAuthConfig {
    pub fn from_env() -> Self {
        Self {
            client_id: env::var("GOOGLE_CLIENT_ID")
                .expect("GOOGLE_CLIENT_ID must be set in environment variables"),
            client_secret: env::var("GOOGLE_CLIENT_SECRET")
                .expect("GOOGLE_CLIENT_SECRET must be set in environment variables"),
            redirect_uri: env::var("GOOGLE_REDIRECT_URI")
                .expect("GOOGLE_REDIRECT_URI must be set in environment variables"),
            auth_url: "https://accounts.google.com/o/oauth2/v2/auth".to_string(),
            token_url: "https://oauth2.googleapis.com/token".to_string(),
            userinfo_url: "https://www.googleapis.com/oauth2/v2/userinfo".to_string(),
        }
    }

    #[allow(dead_code)]
    pub fn get_auth_url(&self, state: &str) -> String {
        format!(
            "{}?client_id={}&response_type=code&scope={}&redirect_uri={}&state={}",
            self.auth_url,
            self.client_id,
            urlencoding::encode("openid profile email"),
            urlencoding::encode(&self.redirect_uri),
            state
        )
    }
}
