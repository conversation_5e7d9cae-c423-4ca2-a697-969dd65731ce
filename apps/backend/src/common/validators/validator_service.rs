use validator::{Validate, ValidationErrors};
use crate::common::errors::AppError;

pub struct ValidatorService;

impl ValidatorService {
    pub fn validate<T: Validate>(data: &T) -> Result<(), AppError> {
        match data.validate() {
            Ok(_) => Ok(()),
            Err(errors) => {
                let error_message = Self::format_validation_errors(errors);
                Err(AppError::Validation(error_message))
            }
        }
    }

    fn format_validation_errors(errors: ValidationErrors) -> String {
        let mut messages = Vec::new();

        for (field, field_errors) in errors.field_errors() {
            for error in field_errors {
                let message = error.message.as_ref()
                    .map(|m| m.to_string())
                    .unwrap_or_else(|| format!("Invalid value for field '{field}'"));
                messages.push(message);
            }
        }

        messages.join(", ")
    }
}
