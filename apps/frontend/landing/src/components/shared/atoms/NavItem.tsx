import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
} from '@/components/ui/navigation-menu';
import React from 'react';

interface NavItemProps {
  href: string;
  className?: string;
  listClassName?: string;
  itemClassName?: string;
  linkClassName?: string;
  children?: React.ReactNode;
}

const NavItem: React.FC<NavItemProps> = ({ href, children }) => {
  return (
    <NavigationMenu viewport={false}>
      <NavigationMenuList>
        <NavigationMenuItem>
          <NavigationMenuLink
            href={href}
            className="flex-shrink-0 text-sm font-medium hover:text-primary transition-colors duration-200 flex flex-row items-center gap-2 rounded-md hover:bg-primary/5 w-full justify-center md:justify-start whitespace-nowrap"
          >
            {children}
          </NavigationMenuLink>
        </NavigationMenuItem>
      </NavigationMenuList>
    </NavigationMenu>
  );
};

export default NavItem;
