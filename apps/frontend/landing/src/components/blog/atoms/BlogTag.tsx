import React from 'react';

interface BlogTagProps {
  tag: string;
  isActive?: boolean;
  onClick?: () => void;
  className?: string;
}

const BlogTag: React.FC<BlogTagProps> = ({
  tag,
  isActive = false,
  onClick,
  className,
}) => {
  return (
    <button
      onClick={onClick}
      className={`px-3 py-2 text-xs sm:text-sm font-mono rounded-lg border transition-all duration-300 hover:scale-105 ${
        isActive
          ? 'bg-primary text-primary-foreground border-primary shadow-lg neon-glow'
          : 'bg-card/50 text-muted-foreground border-primary/20 hover:bg-primary/10 hover:text-primary hover:border-primary/40'
      } ${className || ''}`}
    >
      <span className="text-accent">#</span>
      {tag}
    </button>
  );
};

export default BlogTag;
