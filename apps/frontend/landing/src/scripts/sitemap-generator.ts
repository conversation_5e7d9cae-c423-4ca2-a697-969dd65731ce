import fs from "fs";
import { glob } from "glob";
import path from "path";
import { promisify } from "util";
import { create } from "xmlbuilder2";
import zlib from "zlib";
import config from "../../sitemap.config.mjs"; // Adjust path if needed
import logger from "../lib/core/logger";

const gzip = promisify(zlib.gzip);

type ChangeFreq = "always" | "hourly" | "daily" | "weekly" | "monthly" | "yearly" | "never";

export interface SitemapEntry {
  loc: string; // Should be the full URL
  lastmod?: string;
  changefreq?: ChangeFreq;
  priority?: number;
  // For multi-language pages without distinct files
  alternates?: { hreflang: string; href: string }[];
}

// Adjusted Config interface to make languages optional
export interface Config {
  baseUrl: string;
  supportedLanguages?: string[]; // Optional
  defaultLanguage?: string; // Optional, defaults handled if supportedLanguages present
  pagesDir?: string;
  appDir?: string;
  outputDir: string;
  excludePaths?: string[];
  priority?: Record<string, number>;
  changefreq?: Record<string, ChangeFreq>;
  transform?: (
    urlPath: string,
    lang: string | undefined,
    file: string,
  ) => Promise<string | null | SitemapEntry | SitemapEntry[]>;
  logLevel?: "silent" | "info" | "debug";
  gzip?: boolean;
  sitemapIndex?: boolean; // Recommended if using multiple languages
  trailingSlash?: boolean;
  // Optional: Explicitly define if i18n routing ([locale] folders) is used.
  // If 'auto', script will try to detect; if false, assumes no locale folders.
  useI18nRouting?: boolean | "auto";
}

const log = (level: "info" | "debug" | "error", message: string, ...args: any[]) => {
  const configLevel = config.logLevel ?? "info";
  if (configLevel === "silent") return;
  if (configLevel === "info" && level === "debug") return;
  const prefix = `[SitemapGenerator:${level.toUpperCase()}]`;
  if (level === "error") {
    logger.error(prefix, message, ...args);
  } else {
    logger.log(prefix, message, ...args);
  }
};

const formatDate = (date: Date): string => date.toISOString().split("T")[0];

const normalizePath = (p: string): string => p.replace(/\\/g, "/");

const matchesPattern = (path: string, patterns: string[]): boolean =>
  patterns.some((pattern) => {
    try {
      // Escape special regex characters in the pattern before creating RegExp
      const escapedPattern = pattern
        .replace(/[.*+?^${}()|[\]\\]/g, "\\$&") // Escape standard regex chars
        .replace(/\\\*\\\*/g, ".*") // Convert ** to .*
        .replace(/\\\*/g, "[^/]*"); // Convert * to [^/]* (match within segment)
      return new RegExp(`^${escapedPattern}$`).test(path);
    } catch (e) {
      log("error", `Invalid regex pattern generated from exclusion: ${pattern}`);
      return false;
    }
  });

const getEntryProperty = <T>(
  urlPath: string,
  configMap: Record<string, T> | undefined,
  defaultValue: T,
): T => {
  if (!configMap) return defaultValue;
  const exactMatch = configMap[urlPath];
  if (exactMatch !== undefined) return exactMatch;
  const wildcardKey = Object.keys(configMap)
    .filter((key) => key.includes("*")) // Check for patterns containing '*'
    .sort((a, b) => b.length - a.length) // Longer patterns first for specificity
    .find((key) => {
      // Convert wildcard to regex for matching
      const regexPattern = `^${key.replace(/\*\*/g, ".*").replace(/\*/g, "[^/]*")}$`;
      try {
        return new RegExp(regexPattern).test(urlPath);
      } catch {
        log("error", `Invalid regex pattern generated from config key: ${key}`);
        return false;
      }
    });

  return wildcardKey ? configMap[wildcardKey] : defaultValue;
};

const isExcluded = (route: string, file: string, excludePatterns: string[]): boolean => {
  const normalizedRoute = normalizePath(route);
  const normalizedFile = normalizePath(file);

  // Basic Next.js internal/dynamic file exclusion (can be overridden by specific includes via transform)
  if (/(?:^|\/)(?:_app|_document|_error|404|api\/|\(.*\)|\[.*\])/.test(normalizedFile)) {
    log("debug", `Auto-excluding special/dynamic file structure: ${normalizedFile}`);
    return true;
  }

  // Check user-defined exclusions against route and file path
  return (
    matchesPattern(normalizedRoute, excludePatterns) ||
    matchesPattern(normalizedFile, excludePatterns)
  );
};

const generateSitemap = async () => {
  log("info", "Starting sitemap generation...");
  const start = Date.now();

  const {
    baseUrl,
    supportedLanguages = [], // Default to empty array
    defaultLanguage, // Can be undefined
    pagesDir = "pages",
    appDir = "app",
    outputDir = "public",
    excludePaths = [],
    priority = {},
    changefreq = {},
    transform,
    gzip: enableGzip = true,
    sitemapIndex = true, // Defaulting to true is safer if i18n might exist
    trailingSlash = false,
    useI18nRouting = "auto", // Default to auto-detect
  } = config;

  // --- i18n Configuration Logic ---
  const hasLanguagesConfig = supportedLanguages && supportedLanguages.length > 0;
  const effectiveDefaultLang =
    defaultLanguage ?? (hasLanguagesConfig ? supportedLanguages[0] : undefined);
  let i18nActive = useI18nRouting === true;

  if (useI18nRouting === "auto" && hasLanguagesConfig) {
    // Attempt to detect i18n structure ([locale] folders)
    const localePattern = `/{${supportedLanguages.join(",")}}/**`;
    const appLocalePath = normalizePath(
      path.join(path.resolve(process.cwd(), appDir), `/[${effectiveDefaultLang}]`),
    ); // Check for one lang folder
    const pagesLocalePath = normalizePath(
      path.join(path.resolve(process.cwd(), pagesDir), `/[${effectiveDefaultLang}]`),
    );
    // Simple check: Do directories like /app/[en] or /pages/[en] exist?
    // A more robust check could use glob, but this is faster for startup.
    i18nActive =
      fs.existsSync(appLocalePath.replace(/\[.*?\]/, effectiveDefaultLang!)) ||
      fs.existsSync(pagesLocalePath.replace(/\[.*?\]/, effectiveDefaultLang!));

    if (i18nActive) {
      log("info", "Auto-detected i18n routing structure.");
    } else {
      log("info", "No i18n routing structure detected or useI18nRouting set to false.");
    }
  } else if (useI18nRouting === false) {
    i18nActive = false;
    log("info", "i18n routing explicitly disabled in config.");
  }
  // --- End i18n Configuration ---

  const pagesPath = path.resolve(process.cwd(), pagesDir);
  const appPath = path.resolve(process.cwd(), appDir);
  const outputPath = path.resolve(process.cwd(), outputDir);

  if (!fs.existsSync(outputPath)) {
    fs.mkdirSync(outputPath, { recursive: true });
    log("info", `Created output directory: ${outputPath}`);
  }

  // Use a single map to store entries; language is a property within SitemapEntry if needed
  const sitemapEntriesMap: Map<string, SitemapEntry> = new Map(); // Key: canonical URL path (e.g., /about), Value: SitemapEntry

  const filePatterns: string[] = [];
  if (fs.existsSync(pagesPath)) {
    filePatterns.push(normalizePath(path.join(pagesPath, "/**/!(_*).{js,jsx,ts,tsx}")));
    filePatterns.push(normalizePath(`!${path.join(pagesPath, "/api/**")}`));
    if (i18nActive) {
      filePatterns.push(normalizePath(`!${path.join(pagesPath, "/\\[locale\\]/**")}`)); // Exclude files under explicit locale folder if i18n active
    }
  }
  if (fs.existsSync(appPath)) {
    filePatterns.push(normalizePath(path.join(appPath, "/**/{page,route}.{js,jsx,ts,tsx}")));
    filePatterns.push(normalizePath(`!${path.join(appPath, "/api/**")}`));
    // Exclude layout, template, loading, error, not-found by default unless explicitly handled
    filePatterns.push(
      normalizePath(
        `!${path.join(appPath, "/**/{layout,template,loading,error,not-found}.{js,jsx,ts,tsx}")}`,
      ),
    );
    if (i18nActive) {
      filePatterns.push(normalizePath(`!${path.join(appPath, "/\\[locale\\]/**")}`)); // Exclude files under explicit locale folder if i18n active
    }
  }

  if (filePatterns.length === 0) {
    log(
      "error",
      `No pagesDir ('${pagesDir}') or appDir ('${appDir}') found containing files matching patterns.`,
    );
    // Don't exit, maybe transform adds all URLs
  }

  log("debug", "Using file patterns:", filePatterns);
  const files =
    filePatterns.length > 0
      ? await glob(filePatterns, { nodir: true, dot: true, ignore: ["**/node_modules/**"] })
      : [];
  log("info", `Found ${files.length} potential static route files.`);

  for (const file of files) {
    const normalizedFile = normalizePath(file);
    let routeLang: string | undefined = undefined; // Assume no specific lang unless detected
    let baseDir = "";
    let relativePath = "";

    if (normalizedFile.startsWith(normalizePath(appPath))) {
      baseDir = normalizePath(appPath);
      relativePath = normalizedFile.substring(baseDir.length);
      // Check for [locale] structure ONLY if i18nActive
      if (i18nActive && hasLanguagesConfig) {
        const langSegmentMatch = relativePath.match(/^\/\[locale\]\/(.*)/); // Match files inside [locale] folder
        // We don't extract the language here; we assume this file applies to ALL supported languages
        if (langSegmentMatch) {
          relativePath = langSegmentMatch[1]; // Path relative to [locale]
        } else {
          // If not in [locale] folder, assume it's default lang or lang-agnostic
          if (effectiveDefaultLang) routeLang = effectiveDefaultLang;
        }
      } else {
        // Not using i18n routing, treat as language agnostic or default
        if (effectiveDefaultLang && supportedLanguages.length <= 1)
          routeLang = effectiveDefaultLang;
      }

      relativePath = relativePath.replace(/(\/route|\/page)\.(js|jsx|ts|tsx)$/, ""); // Remove /route.ext or /page.ext
      relativePath = relativePath.replace(/\/$/, "") || "/"; // Handle root
    } else if (normalizedFile.startsWith(normalizePath(pagesPath))) {
      baseDir = normalizePath(pagesPath);
      relativePath = normalizedFile.substring(baseDir.length);
      // Check for [locale] structure ONLY if i18nActive
      if (i18nActive && hasLanguagesConfig) {
        const langSegmentMatch = relativePath.match(/^\/\[locale\]\/(.*)/);
        if (langSegmentMatch) {
          relativePath = langSegmentMatch[1];
        } else {
          if (effectiveDefaultLang) routeLang = effectiveDefaultLang;
        }
      } else {
        if (effectiveDefaultLang && supportedLanguages.length <= 1)
          routeLang = effectiveDefaultLang;
      }

      relativePath = relativePath.replace(/\.(js|jsx|ts|tsx)$/, "");
      if (path.basename(relativePath) === "index") {
        relativePath = path.dirname(relativePath);
      }
      relativePath = relativePath.replace(/\/$/, "") || "/"; // Handle root index
    } else {
      continue;
    }

    // Route Group cleaning - /(marketing)/home -> /home
    relativePath = relativePath.replace(/\/\([^)]+\)/g, "");

    if (isExcluded(relativePath, normalizedFile, excludePaths)) {
      log(
        "debug",
        `Excluding route '${relativePath}' from file '${normalizedFile}' based on config.`,
      );
      continue;
    }

    let canonicalPath = relativePath; // Path without language prefix
    if (trailingSlash && canonicalPath !== "/") {
      canonicalPath += "/";
    } else if (!trailingSlash && canonicalPath.endsWith("/") && canonicalPath !== "/") {
      canonicalPath = canonicalPath.slice(0, -1);
    }

    const stat = fs.statSync(file);
    const lastmod = formatDate(stat.mtime);
    const resolvedPriority = getEntryProperty(canonicalPath, priority, 0.7);
    const resolvedChangefreq = getEntryProperty(canonicalPath, changefreq, "weekly");

    let transformedEntries: (SitemapEntry | string | null)[] = [];

    if (transform) {
      try {
        // Pass undefined lang if not specifically detected
        const result = await transform(canonicalPath, routeLang, normalizedFile);
        if (result === null) {
          log("debug", `Transform excluded path '${canonicalPath}'.`);
          transformedEntries = [];
        } else if (Array.isArray(result)) {
          transformedEntries = result;
        } else if (result) {
          transformedEntries = [result];
        } else {
          // If transform returns undefined/falsy, fallback to default entry
          transformedEntries.push({ loc: canonicalPath });
        }
      } catch (error: any) {
        log("error", `Error during transform for file ${normalizedFile}: ${error.message}`);
        transformedEntries = []; // Skip on error
      }
    } else {
      // Default entry if no transform
      transformedEntries.push({ loc: canonicalPath });
    }

    // Process transformed entries or the default one
    for (const entry of transformedEntries) {
      if (!entry) continue;

      const finalEntry: Partial<SitemapEntry> & { loc: string } =
        typeof entry === "string" ? { loc: entry } : entry;

      // Use canonical path from entry if provided, otherwise use derived path
      let entryCanonicalPath = finalEntry.loc.startsWith("/")
        ? finalEntry.loc
        : `/${finalEntry.loc}`;

      // Apply trailing slash based on config to the canonical path
      if (trailingSlash && entryCanonicalPath !== "/" && !entryCanonicalPath.endsWith("/")) {
        entryCanonicalPath += "/";
      } else if (!trailingSlash && entryCanonicalPath.endsWith("/") && entryCanonicalPath !== "/") {
        entryCanonicalPath = entryCanonicalPath.slice(0, -1);
      }

      const langsToGenerate =
        i18nActive && hasLanguagesConfig && !routeLang
          ? supportedLanguages
          : [routeLang ?? effectiveDefaultLang ?? "x-default"]; // Use 'x-default' if no lang context

      const alternates: { hreflang: string; href: string }[] = [];

      for (const lang of langsToGenerate) {
        if (!lang) continue; // Skip if no language context could be determined

        const langPrefix =
          i18nActive && hasLanguagesConfig && lang !== effectiveDefaultLang ? `/${lang}` : "";
        const fullUrl = `${baseUrl}${langPrefix}${entryCanonicalPath}`;

        try {
          new URL(fullUrl); // Validate URL structure
        } catch (e) {
          log(
            "error",
            `Invalid URL generated: ${fullUrl} from path ${entryCanonicalPath} for lang ${lang}. Skipping.`,
          );
          continue;
        }

        // Add to alternates if multiple languages for this canonical path
        if (i18nActive && hasLanguagesConfig && langsToGenerate.length > 1) {
          alternates.push({ hreflang: lang, href: fullUrl });
        }

        // Create or update the entry in the map. Keyed by *full URL* to handle language variants.
        // If not using i18n, alternates will be empty.
        if (!sitemapEntriesMap.has(fullUrl)) {
          log("debug", `Adding URL: ${fullUrl} (lang: ${lang})`);
          sitemapEntriesMap.set(fullUrl, {
            loc: fullUrl,
            lastmod: finalEntry.lastmod ?? lastmod,
            changefreq: finalEntry.changefreq ?? resolvedChangefreq,
            priority: finalEntry.priority ?? resolvedPriority,
            alternates: [], // Placeholder, will be populated later if needed
          });
        }
      }

      // Add alternate links if generated
      if (alternates.length > 0) {
        alternates.forEach((alt) => {
          const entryToUpdate = sitemapEntriesMap.get(alt.href);
          if (entryToUpdate) {
            // Filter out self-reference for alternates
            entryToUpdate.alternates = alternates.filter((a) => a.href !== alt.href);
          }
        });
      }
    } // End processing transformed entries
  } // End file loop

  // --- Allow transform function to add purely dynamic URLs ---
  if (transform) {
    log("info", "Running transform for dynamic URLs (file = null)...");
    try {
      // Call transform once without a specific file to allow adding arbitrary URLs
      const dynamicResult = await transform("/", undefined, ""); // Use root path and no file as signal
      const dynamicEntries =
        dynamicResult === null
          ? []
          : Array.isArray(dynamicResult)
            ? dynamicResult
            : dynamicResult
              ? [dynamicResult]
              : [];

      for (const entry of dynamicEntries) {
        if (!entry) continue;
        const finalEntry: Partial<SitemapEntry> & { loc: string } =
          typeof entry === "string" ? { loc: entry } : entry;

        // Assume loc is the full URL if added dynamically
        const fullUrl = finalEntry.loc.startsWith(baseUrl)
          ? finalEntry.loc
          : `${baseUrl}${finalEntry.loc.startsWith("/") ? "" : "/"}${finalEntry.loc}`;

        try {
          new URL(fullUrl); // Validate
          if (!sitemapEntriesMap.has(fullUrl)) {
            log("debug", `Adding dynamic URL from transform: ${fullUrl}`);
            sitemapEntriesMap.set(fullUrl, {
              loc: fullUrl,
              lastmod: finalEntry.lastmod ?? formatDate(new Date()), // Default to now
              changefreq: finalEntry.changefreq ?? "weekly",
              priority: finalEntry.priority ?? 0.5, // Default priority for dynamic
              alternates: finalEntry.alternates ?? [],
            });
          } else {
            log("debug", `Dynamic URL ${fullUrl} already exists, skipping.`);
          }
        } catch (e) {
          log("error", `Invalid dynamic URL from transform: ${fullUrl}. Skipping.`);
        }
      }
    } catch (error: any) {
      log("error", `Error during dynamic transform call: ${error.message}`);
    }
  }
  // --- End dynamic URL addition ---

  const allEntries = Array.from(sitemapEntriesMap.values());

  if (allEntries.length === 0) {
    log(
      "error",
      "No sitemap entries generated. Check patterns, exclusions, and transform function.",
    );
    return;
  }

  const sitemapsToWrite: { filename: string; content: string; url: string }[] = [];
  const needsIndex =
    sitemapIndex && hasLanguagesConfig && supportedLanguages.length > 1 && i18nActive; // Generate index only if i18n is active and configured

  if (needsIndex) {
    // Split entries by language for separate sitemaps
    const entriesByLang: Record<string, SitemapEntry[]> = {};
    supportedLanguages.forEach((lang: any) => {
      entriesByLang[lang] = [];
    });

    allEntries.forEach((entry) => {
      // Try to determine language from URL prefix
      let entryLang = effectiveDefaultLang ?? "x-default"; // Fallback
      if (i18nActive && hasLanguagesConfig) {
        const urlPath = entry.loc.substring(baseUrl.length);
        const langMatch = urlPath.match(/^\/([a-zA-Z]{2}(?:-[a-zA-Z]{2})?)\//);
        if (langMatch && supportedLanguages.includes(langMatch[1])) {
          entryLang = langMatch[1];
        }
      }
      if (entriesByLang[entryLang]) {
        entriesByLang[entryLang].push(entry);
      } else {
        log(
          "error",
          `Could not determine language for URL ${entry.loc}, assigning to default/first language.`,
        );
        entriesByLang[supportedLanguages[0]].push(entry); // Assign to first language as fallback
      }
    });

    supportedLanguages.forEach((lang: any) => {
      if (entriesByLang[lang].length > 0) {
        const root = create({ version: "1.0", encoding: "UTF-8" }).ele("urlset", {
          xmlns: "http://www.sitemaps.org/schemas/sitemap/0.9",
          "xmlns:xhtml": "http://www.w3.org/1999/xhtml", // Add xhtml namespace for alternates
        });

        entriesByLang[lang].forEach((sitemapEntry) => {
          const urlElement = root.ele("url");
          urlElement.ele("loc").txt(sitemapEntry.loc);
          if (sitemapEntry.lastmod) urlElement.ele("lastmod").txt(sitemapEntry.lastmod);
          if (sitemapEntry.changefreq) urlElement.ele("changefreq").txt(sitemapEntry.changefreq);
          if (sitemapEntry.priority !== undefined)
            urlElement.ele("priority").txt(sitemapEntry.priority.toFixed(1));

          // Add alternate links
          if (sitemapEntry.alternates && sitemapEntry.alternates.length > 0) {
            sitemapEntry.alternates.forEach((alt) => {
              urlElement.ele("xhtml:link", {
                rel: "alternate",
                hreflang: alt.hreflang,
                href: alt.href,
              });
            });
          }
        });
        const xml = root.end({ prettyPrint: true });
        const sitemapFilename = `sitemap-${lang}.xml`;
        sitemapsToWrite.push({
          filename: sitemapFilename,
          content: xml,
          url: `${baseUrl}/${sitemapFilename}`,
        });
      }
    });
  } else {
    // Generate a single sitemap.xml
    const root = create({ version: "1.0", encoding: "UTF-8" }).ele("urlset", {
      xmlns: "http://www.sitemaps.org/schemas/sitemap/0.9",
      "xmlns:xhtml": "http://www.w3.org/1999/xhtml",
    });

    allEntries.forEach((sitemapEntry) => {
      const urlElement = root.ele("url");
      urlElement.ele("loc").txt(sitemapEntry.loc);
      if (sitemapEntry.lastmod) urlElement.ele("lastmod").txt(sitemapEntry.lastmod);
      if (sitemapEntry.changefreq) urlElement.ele("changefreq").txt(sitemapEntry.changefreq);
      if (sitemapEntry.priority !== undefined)
        urlElement.ele("priority").txt(sitemapEntry.priority.toFixed(1));
      // Add alternate links if they exist (could be added by transform even without i18n routing)
      if (sitemapEntry.alternates && sitemapEntry.alternates.length > 0) {
        sitemapEntry.alternates.forEach((alt) => {
          urlElement.ele("xhtml:link", {
            rel: "alternate",
            hreflang: alt.hreflang,
            href: alt.href,
          });
        });
      }
    });
    const xml = root.end({ prettyPrint: true });
    const sitemapFilename = "sitemap.xml";
    sitemapsToWrite.push({
      filename: sitemapFilename,
      content: xml,
      url: `${baseUrl}/${sitemapFilename}`,
    });
  }

  // Write sitemap files (and potentially compress)
  const generatedSitemapUrls: string[] = [];
  const writePromises = sitemapsToWrite.map(async (sitemap) => {
    const filePath = path.join(outputPath, sitemap.filename);
    let writePath = filePath;
    let finalUrl = sitemap.url;

    if (enableGzip) {
      try {
        const compressed = await gzip(sitemap.content);
        writePath = `${filePath}.gz`;
        finalUrl = `${sitemap.url}.gz`;
        fs.writeFileSync(writePath, compressed);
        log(
          "info",
          `Generated ${path.basename(writePath)} (${sitemapEntriesMap.size} total entries mapped to relevant sitemaps)`,
        );
        generatedSitemapUrls.push(finalUrl);
      } catch (err: any) {
        log("error", `Failed to gzip ${sitemap.filename}: ${err.message}. Writing uncompressed.`);
        fs.writeFileSync(filePath, sitemap.content); // Fallback to uncompressed
        generatedSitemapUrls.push(sitemap.url);
      }
    } else {
      fs.writeFileSync(filePath, sitemap.content);
      log(
        "info",
        `Generated ${path.basename(writePath)} (${sitemapEntriesMap.size} total entries mapped)`,
      );
      generatedSitemapUrls.push(finalUrl);
    }
  });

  await Promise.all(writePromises);

  // Write sitemap index file if needed
  if (needsIndex && generatedSitemapUrls.length > 0) {
    const indexRoot = create({ version: "1.0", encoding: "UTF-8" }).ele("sitemapindex", {
      xmlns: "http://www.sitemaps.org/schemas/sitemap/0.9",
    });

    const generationDate = formatDate(new Date());

    generatedSitemapUrls.forEach((sitemapUrl) => {
      const sitemapElement = indexRoot.ele("sitemap");
      sitemapElement.ele("loc").txt(sitemapUrl);
      sitemapElement.ele("lastmod").txt(generationDate);
    });

    const indexXml = indexRoot.end({ prettyPrint: true });
    const indexPath = path.join(outputPath, "sitemap.xml");
    fs.writeFileSync(indexPath, indexXml);
    log("info", `Generated sitemap index: ${indexPath}`);
  } else if (!needsIndex && generatedSitemapUrls.length > 0) {
    // If not using index (single sitemap generated), ensure it's named sitemap.xml
    const generatedFile = path.basename(generatedSitemapUrls[0].replace(`${baseUrl}/`, ""));
    if (generatedFile !== "sitemap.xml" && generatedFile !== "sitemap.xml.gz") {
      const currentPath = path.join(outputPath, generatedFile);
      const targetPath = path.join(
        outputPath,
        generatedFile.endsWith(".gz") ? "sitemap.xml.gz" : "sitemap.xml",
      );
      try {
        fs.renameSync(currentPath, targetPath);
        log("info", `Renamed single sitemap to ${path.basename(targetPath)}`);
      } catch (err: any) {
        log(
          "error",
          `Could not rename single sitemap ${generatedFile} to ${path.basename(targetPath)}: ${err.message}`,
        );
      }
    }
  }

  const duration = Date.now() - start;
  log("info", `Sitemap generation finished in ${duration}ms.`);
};

generateSitemap().catch((error) => {
  log("error", "Unhandled error during sitemap generation:", error);
  process.exit(1);
});
