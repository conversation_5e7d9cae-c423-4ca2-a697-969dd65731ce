import { GlobalAuthModal } from '@/components/auth/providers/GlobalAuthModal';
import { Components } from '@/components/registry';
import { Toaster } from '@/components/ui/sonner';
import { AuthProvider } from '@/hooks/auth/use-auth';
import { ThemeProvider } from './theme-provider';

interface Props {
  children?: React.ReactNode;
}
const AppProvider = ({ children }: Props) => {
  return (
    <Components.AuthErrorBoundary>
      <ThemeProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
        disableTransitionOnChange
      >
        <AuthProvider>
          <GlobalAuthModal>{children}</GlobalAuthModal>
          <Toaster />
        </AuthProvider>
      </ThemeProvider>
    </Components.AuthErrorBoundary>
  );
};
export default AppProvider;
