use actix_web::web;
use super::health_controller::HealthController;

pub struct HealthModule;

impl HealthModule {
    pub fn configure(cfg: &mut web::ServiceConfig) {
        cfg.service(
            web::scope("/api/health")
                .route("", web::get().to(HealthController::check))
                .route("/ready", web::get().to(HealthController::ready))
                .route("/live", web::get().to(HealthController::live))
        );
    }
}
