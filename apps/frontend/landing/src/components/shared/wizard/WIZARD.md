# Wizard Component System

A comprehensive, type-safe, multi-step form wizard system built with React, TypeScript, and Jotai state management. This wizard system provides a flexible foundation for creating complex, multi-step user interfaces with validation, navigation, progress tracking, and auto-save capabilities.

## Architecture Overview

The wizard system consists of multiple interconnected modules:

- **Types**: Core type definitions and interfaces
- **Store**: State management using Jotai atoms
- **Hooks**: Custom React hooks for wizard functionality
- **Components**: UI components for rendering wizard elements
- **Utils**: Utility functions for validation and step management

## Core Components

### MultiStepWizard

The main wizard container component that orchestrates the entire wizard flow.

```tsx
import { MultiStepWizard } from './multi-step-wizard';

<MultiStepWizard
  wizardId="my-wizard"
  steps={[
    {
      id: 'step1',
      title: 'First Step',
      component: MyStepComponent,
      validate: (data) => (data.name ? true : 'Name is required'),
    },
  ]}
  onComplete={(data) => console.log('Wizard completed:', data)}
  theme={{
    progressVariant: 'steps',
    navigationPosition: 'bottom',
  }}
/>;
```

#### Props

- `wizardId`: Unique identifier for the wizard instance
- `steps`: Array of step configurations
- `onComplete`: Callback fired when wizard is completed
- `theme`: Visual theme configuration
- `persistProgress`: Enable progress persistence in localStorage
- `autoSave`: Enable automatic data saving
- `showStepProgress`: Show/hide progress indicator
- `showNavigation`: Show/hide navigation controls
- `allowStepClick`: Allow clicking on step indicators to navigate

### WizardProgress

Visual progress indicator showing current step position and completion status.

```tsx
<WizardProgress
  currentStep={2}
  totalSteps={5}
  steps={stepDefinitions}
  variant="steps"
  clickable={true}
  onStepClick={(stepIndex) => navigateToStep(stepIndex)}
/>
```

### WizardNavigation

Navigation controls for moving between wizard steps.

```tsx
<WizardNavigation
  canGoNext={isCurrentStepValid}
  canGoPrevious={!isFirstStep}
  onNext={handleNext}
  onPrevious={handlePrevious}
  position="bottom"
  showSkip={currentStep.allowSkip}
  showSave={autoSaveEnabled}
/>
```

## Step Configuration

### Basic Step Structure

```tsx
interface WizardStep<T = Record<string, any>> {
  id: string;
  title: string;
  description?: string;
  component: React.ComponentType<WizardStepProps<T>>;
  validate?: (data: T, context: WizardStepContext<T>) => WizardValidationResult;
  isOptional?: boolean;
  isVisible?: (context: WizardStepContext) => boolean;
  dependencies?: WizardStepDependency[];
  icon?: React.ComponentType<{ className?: string }>;
  estimatedTime?: number;
  allowSkip?: boolean;
  onEnter?: (context: WizardStepContext<T>) => void;
  onExit?: (context: WizardStepContext<T>) => void;
  onMount?: (context: WizardStepContext<T>) => void;
  onUnmount?: (context: WizardStepContext<T>) => void;
}
```

### Step Component Interface

Every step component receives standardized props:

```tsx
interface WizardStepProps<T = Record<string, any>> {
  data: T;
  onDataChange: (data: Partial<T> | ((prev: T) => T)) => void;
  onNext: () => Promise<void> | void;
  onPrevious: () => void;
  context: WizardStepContext<T>;
  canGoNext: boolean;
  canGoPrevious: boolean;
}
```

## Validation System

### Validation Types

```tsx
type WizardValidationResult = true | string | string[];
```

### Step Validation

```tsx
const validateUserInfo = (
  data: UserData,
  context: WizardStepContext<UserData>,
) => {
  const errors: string[] = [];

  if (!data.firstName) errors.push('First name is required');
  if (!data.email) errors.push('Email is required');
  if (data.email && !isValidEmail(data.email))
    errors.push('Invalid email format');

  return errors.length > 0 ? errors : true;
};
```

### Real-time Validation

Validation runs automatically with debouncing when step data changes.

## State Management

### Wizard State Structure

```tsx
interface WizardState {
  currentStep: number;
  stepData: Record<number, WizardStepData>;
  isComplete: boolean;
  validationErrors: Record<number, string[]>;
  completedSteps: Set<number>;
  touchedSteps: Set<number>;
  isLoading: boolean;
  hasUnsavedChanges: boolean;
  lastSaveTime?: number;
}
```

### Persistence

The wizard automatically persists state to localStorage when `persistProgress` is enabled. State includes:

- Current step position
- All step data
- Completed steps
- Touched steps

## Hooks

### useWizardNavigation

Provides navigation logic and methods:

```tsx
const navigation = useWizardNavigation(config, store);

// Navigate to specific step
await navigation.goToStep(3);

// Move to next step
await navigation.next();

// Move to previous step
navigation.previous();

// Check navigation availability
const canGoNext = navigation.canGoNext;
const canGoPrevious = navigation.canGoPrevious;
```

### useWizardStep

Hook for individual step management:

```tsx
const {
  stepData,
  updateData,
  validationErrors,
  context,
  isActive,
  isCompleted,
} = useWizardStep(stepIndex, config, store);
```

### useWizardValidation

Validation utilities:

```tsx
const validation = useWizardValidation(config, store);

// Validate current step
const isValid = await validation.validateCurrentStep();

// Validate all steps
const allValid = await validation.validateAllSteps();
```

### useWizardAutoSave

Auto-save functionality:

```tsx
const autoSave = useWizardAutoSave(config, store);

// Manual save
autoSave.manualSave();

// Check save status
const hasUnsavedChanges = autoSave.hasUnsavedChanges;
const lastSaveTime = autoSave.lastSaveTime;
```

## Advanced Features

### Step Dependencies

Steps can depend on data from other steps:

```tsx
{
  id: 'payment',
  title: 'Payment',
  component: PaymentStep,
  dependencies: [
    {
      stepId: 'plan-selection',
      condition: (stepData) => stepData.selectedPlan === 'premium'
    }
  ]
}
```

### Conditional Visibility

Steps can be conditionally shown/hidden:

```tsx
{
  id: 'business-info',
  title: 'Business Information',
  component: BusinessInfoStep,
  isVisible: (context) => {
    const userType = context.allStepData[0]?.userType;
    return userType === 'business';
  }
}
```

### Lifecycle Hooks

```tsx
{
  id: 'data-import',
  title: 'Import Data',
  component: DataImportStep,
  onEnter: async (context) => {
    // Initialize data import process
    await initializeImport();
  },
  onExit: async (context) => {
    // Cleanup resources
    await cleanupImport();
  }
}
```

### Auto-save Configuration

```tsx
<MultiStepWizard
  autoSave={true}
  autoSaveInterval={30000} // 30 seconds
  confirmOnExit={true}
  // ... other props
/>
```

## Theme System

### Available Themes

```tsx
interface WizardTheme {
  progressVariant?: 'line' | 'dots' | 'steps';
  navigationPosition?: 'bottom' | 'floating' | 'sidebar';
  animationType?: 'slide' | 'fade' | 'none';
  colorScheme?: 'auto' | 'light' | 'dark';
}
```

### Navigation Positions

- **bottom**: Standard bottom navigation
- **floating**: Floating navigation overlay
- **sidebar**: Sidebar navigation layout

### Progress Variants

- **line**: Simple progress line
- **dots**: Dot indicators
- **steps**: Detailed step indicators with labels

## Utility Functions

### Form Utilities

```tsx
const { createFieldUpdater, createMultiFieldUpdater, createFieldValidator } =
  createWizardFormUtils<MyFormData>();

// Field-specific updater
const updateName = createFieldUpdater(onDataChange)('name');

// Multi-field updater
const updateFields = createMultiFieldUpdater(onDataChange);

// Field validator
const validateFields = createFieldValidator({
  name: (value) => (value ? null : 'Name is required'),
  email: (value) => (isValidEmail(value) ? null : 'Invalid email'),
});
```

### Step Management

```tsx
// Check if navigation is allowed
const canNavigate = canNavigateToStep(
  targetStep,
  currentStep,
  completedSteps,
  allowSkipping,
  steps,
);

// Get visible steps based on conditions
const visibleSteps = getVisibleSteps(steps, allStepData, wizardId);

// Calculate progress
const progress = getStepProgress(currentStep, totalSteps, completedSteps);
```

## Error Handling

The wizard system includes comprehensive error handling:

- Form validation errors are displayed inline
- Step validation prevents navigation when invalid
- Runtime errors are caught and displayed with retry options
- Network errors during async operations are handled gracefully

## Best Practices

### Step Design

1. Keep steps focused on single concepts
2. Use clear, descriptive titles
3. Implement proper validation
4. Consider step dependencies carefully
5. Use lifecycle hooks for resource management

### Performance

1. Use lazy loading for step components when possible
2. Implement proper debouncing for validation
3. Consider virtualization for wizards with many steps
4. Minimize re-renders by optimizing data flow

### User Experience

1. Provide clear progress indication
2. Allow users to go back and edit previous steps
3. Use auto-save to prevent data loss
4. Show validation errors clearly
5. Consider accessibility requirements

### State Management

1. Use meaningful wizard IDs for persistence
2. Clean up wizard state when appropriate
3. Handle concurrent wizard instances properly
4. Consider memory usage with large datasets

## Examples

### Simple Survey Wizard

```tsx
const surveySteps = [
  {
    id: 'personal-info',
    title: 'Personal Information',
    component: PersonalInfoStep,
    validate: (data) =>
      data.name && data.email ? true : ['Name and email are required'],
  },
  {
    id: 'preferences',
    title: 'Preferences',
    component: PreferencesStep,
    isOptional: true,
  },
  {
    id: 'review',
    title: 'Review',
    component: ReviewStep,
  },
];

<MultiStepWizard
  wizardId="survey-2024"
  steps={surveySteps}
  onComplete={handleSurveyComplete}
  persistProgress={true}
  theme={{ progressVariant: 'steps' }}
/>;
```

### Complex Multi-branch Wizard

```tsx
const onboardingSteps = [
  {
    id: 'account-type',
    title: 'Account Type',
    component: AccountTypeStep,
  },
  {
    id: 'business-details',
    title: 'Business Details',
    component: BusinessDetailsStep,
    isVisible: (context) => context.allStepData[0]?.accountType === 'business',
    dependencies: [
      {
        stepId: 'account-type',
        condition: (data) => data.accountType === 'business',
      },
    ],
  },
  {
    id: 'personal-details',
    title: 'Personal Details',
    component: PersonalDetailsStep,
    isVisible: (context) => context.allStepData[0]?.accountType === 'personal',
  },
  {
    id: 'verification',
    title: 'Verification',
    component: VerificationStep,
    onEnter: async () => await sendVerificationCode(),
    validate: async (data) => await validateVerificationCode(data.code),
  },
  {
    id: 'completion',
    title: 'Welcome!',
    component: CompletionStep,
  },
];
```

## Troubleshooting

### Common Issues

1. **State not persisting**: Check wizardId uniqueness and localStorage availability
2. **Validation not working**: Ensure validation functions return proper types
3. **Steps not showing**: Check isVisible conditions and dependencies
4. **Navigation disabled**: Verify step validation and canNavigate conditions
5. **Performance issues**: Check for unnecessary re-renders and optimize data flow

### Debug Mode

Enable debug logging by setting `localStorage.setItem('wizard-debug', 'true')` in browser console.

---

This wizard system provides a robust foundation for building complex, multi-step user interfaces while maintaining flexibility and performance. The modular architecture allows for easy customization and extension based on specific requirements.
