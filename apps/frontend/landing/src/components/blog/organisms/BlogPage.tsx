'use client';

import blogData from '@/data/blog.json';
import React, { useMemo, useState } from 'react';
import BlogFilters from '../molecules/BlogFilters';
import BlogGrid from '../molecules/BlogGrid';
import BlogHeader from '../molecules/BlogHeader';

interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  author: string;
  readTime: string;
  tags: string[];
  slug: string;
  date: string;
}

interface BlogPageProps {
  className?: string;
}

const BlogPage: React.FC<BlogPageProps> = ({ className }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);

  const allPosts: BlogPost[] = blogData.map((post) => ({
    id: post.id,
    title: post.title,
    excerpt: post.excerpt,
    author: post.author,
    readTime: post.readTime,
    tags: post.tags,
    slug: post.slug,
    date: post.date,
  }));

  const availableTags = useMemo(() => {
    const tags = allPosts.flatMap((post) => post.tags);
    return Array.from(new Set(tags));
  }, []);

  const filteredPosts = useMemo(() => {
    return allPosts.filter((post) => {
      const matchesSearch =
        post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.excerpt.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesTags =
        selectedTags.length === 0 ||
        selectedTags.some((tag) => post.tags.includes(tag));

      return matchesSearch && matchesTags;
    });
  }, [searchTerm, selectedTags]);

  const handleTagToggle = (tag: string) => {
    setSelectedTags((prev) =>
      prev.includes(tag) ? prev.filter((t) => t !== tag) : [...prev, tag],
    );
  };

  return (
    <div
      className={`min-h-screen bg-gradient-to-br from-background via-card to-secondary ${className || ''}`}
    >
      <BlogHeader />

      <section className="py-8 sm:py-12 lg:py-16 relative">
        {/* Additional geeky background elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none opacity-30">
          <div className="absolute top-20 right-10 text-primary/10 font-mono text-xs rotate-12 float">
            {'console.log("reading blog...");'}
          </div>
          <div
            className="absolute top-1/2 left-10 text-accent/10 font-mono text-xs -rotate-12 float"
            style={{ animationDelay: '3s' }}
          >
            {'if (learning) { success++; }'}
          </div>
          <div
            className="absolute bottom-20 right-1/3 text-primary/10 font-mono text-xs rotate-6 float"
            style={{ animationDelay: '5s' }}
          >
            {'// Always be coding'}
          </div>
        </div>

        <div className="container relative z-10 mx-auto px-4 space-y-8 sm:space-y-12">
          <BlogFilters
            availableTags={availableTags}
            selectedTags={selectedTags}
            searchTerm={searchTerm}
            onTagToggle={handleTagToggle}
            onSearchChange={setSearchTerm}
          />

          {filteredPosts.length > 0 ? (
            <BlogGrid posts={filteredPosts} />
          ) : (
            <div className="text-center py-12 sm:py-16">
              <div className="bg-card/50 border border-primary/10 rounded-xl p-6 sm:p-8 max-w-md mx-auto">
                <div className="text-4xl sm:text-5xl mb-4 font-mono text-primary/50">
                  {'404'}
                </div>
                <div className="text-lg sm:text-xl font-mono text-foreground mb-2">
                  هیچ مقاله‌ای یافت نشد!
                </div>
                <div className="text-sm sm:text-base text-muted-foreground font-mono">
                  {'// لطفاً کلمات کلیدی یا فیلترهای دیگری امتحان کنید'}
                </div>
              </div>
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

export default BlogPage;
