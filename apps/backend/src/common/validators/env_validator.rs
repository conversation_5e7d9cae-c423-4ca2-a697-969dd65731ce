use std::env;

#[derive(Debug)]
pub struct EnvValidationError {
    pub missing_vars: Vec<String>,
}

impl std::fmt::Display for EnvValidationError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "Missing required environment variables: {}",
            self.missing_vars.join(", ")
        )
    }
}

impl std::error::Error for EnvValidationError {}

pub struct EnvValidator;

impl EnvValidator {
    pub fn validate_required_vars() -> Result<(), EnvValidationError> {
        let required_vars = vec![
            "DATABASE_URL",
            "REDIS_URL",
            "JWT_SECRET",
            "JWT_EXPIRES_IN",
            "KAVENEGAR_API_KEY",
            "SMS_DEFAULT_SENDER",
            "SMS_OTP_TEMPLATE",
            "SERVER_PORT",
            "SERVER_HOST",
            "MINIO_ENDPOINT",
            "MINIO_ACCESS_KEY",
            "MINIO_SECRET_KEY",
            "MINIO_BUCKET_NAME",
            "GOOGLE_CLIENT_ID",
            "GOOGLE_CLIENT_SECRET",
            "GOOGLE_REDIRECT_URI",
        ];

        let mut missing_vars = Vec::new();

        for var in required_vars {
            if env::var(var).is_err() {
                missing_vars.push(var.to_string());
            }
        }

        if !missing_vars.is_empty() {
            return Err(EnvValidationError { missing_vars });
        }

        Ok(())
    }

    #[allow(dead_code)]
    pub fn validate_sms_config() -> Result<(), EnvValidationError> {
        let sms_vars = vec![
            "KAVENEGAR_API_KEY",
            "SMS_DEFAULT_SENDER",
            "SMS_OTP_TEMPLATE",
        ];

        let mut missing_vars = Vec::new();

        for var in sms_vars {
            if env::var(var).is_err() {
                missing_vars.push(var.to_string());
            }
        }

        if !missing_vars.is_empty() {
            return Err(EnvValidationError { missing_vars });
        }

        Ok(())
    }

    #[allow(dead_code)]
    pub fn validate_database_config() -> Result<(), EnvValidationError> {
        let db_vars = vec!["DATABASE_URL"];

        let mut missing_vars = Vec::new();

        for var in db_vars {
            if env::var(var).is_err() {
                missing_vars.push(var.to_string());
            }
        }

        if !missing_vars.is_empty() {
            return Err(EnvValidationError { missing_vars });
        }

        Ok(())
    }

    #[allow(dead_code)]
    pub fn validate_google_oauth_config() -> Result<(), EnvValidationError> {
        let google_vars = vec![
            "GOOGLE_CLIENT_ID",
            "GOOGLE_CLIENT_SECRET",
            "GOOGLE_REDIRECT_URI",
        ];

        let mut missing_vars = Vec::new();

        for var in google_vars {
            if env::var(var).is_err() {
                missing_vars.push(var.to_string());
            }
        }

        if !missing_vars.is_empty() {
            return Err(EnvValidationError { missing_vars });
        }

        Ok(())
    }
}
