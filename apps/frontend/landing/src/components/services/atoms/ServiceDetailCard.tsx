import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { useAuthModal } from '@/hooks/auth/use-auth-modal';
import { ArrowRight, LucideIcon, Rocket } from 'lucide-react';
import React from 'react';

interface ServiceDetailCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
  features: string[];
  technologies?: string[];
  className?: string;
}

const ServiceDetailCard: React.FC<ServiceDetailCardProps> = ({
  icon: Icon,
  title,
  description,
  features,
  technologies = [],
  className,
}) => {
  const { openModal } = useAuthModal();

  return (
    <Card
      className={`group transition-all duration-500 hover:shadow-md hover:-translate-y-1 border border-primary/30 hover:border-primary/60 bg-card/90 backdrop-blur-sm overflow-hidden relative ${className || ''}`}
    >
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-accent/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
      <div className="absolute -top-20 -right-20 w-40 h-40 rounded-full bg-primary/10 group-hover:scale-150 transition-transform duration-700 ease-out"></div>

      <CardHeader className="relative z-10 pb-4">
        <div className="flex items-start gap-4 mb-4">
          <div className="p-3 bg-gradient-to-br from-primary to-accent rounded-xl text-background shadow-sm group-hover:scale-110 transition-transform duration-300">
            <Icon className="w-6 h-6" />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-bold text-primary font-mono mb-2">
              {'{'} {title} {'}'}
            </h3>
            <p className="text-sm text-muted-foreground leading-relaxed">
              {description}
            </p>
          </div>
        </div>

        {technologies.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-4">
            {technologies.map((tech, index) => (
              <span
                key={index}
                className="text-xs font-mono bg-accent/10 text-accent border border-accent/20 hover:bg-accent/20 transition-colors px-2 py-1 rounded-md"
              >
                {tech}
              </span>
            ))}
          </div>
        )}
      </CardHeader>

      <CardContent className="relative z-10 pt-0">
        <div className="space-y-3 mb-6">
          {features.map((feature, index) => (
            <div key={index} className="flex items-center gap-3">
              <div className="w-2 h-2 rounded-full bg-primary group-hover:scale-125 transition-transform duration-300"></div>
              <span className="text-sm text-foreground">{feature}</span>
            </div>
          ))}
        </div>

        <Button
          variant="outline"
          className="w-full font-mono text-sm bg-gradient-to-r from-primary/20 to-accent/20 hover:from-primary/30 hover:to-accent/30 border-primary/30 text-primary hover:border-primary/50 shadow-lg hover:scale-105 transition-all duration-300 group neo-brutal flex items-center justify-center gap-3"
          onClick={openModal}
        >
          <ArrowRight className="w-5 h-5 group-hover:-translate-x-1 transition-transform order-2" />
          <span className="order-1">درخواست مشاوره</span>
          <Rocket className="w-5 h-5 group-hover:animate-pulse order-0" />
        </Button>
      </CardContent>
    </Card>
  );
};

export default ServiceDetailCard;
