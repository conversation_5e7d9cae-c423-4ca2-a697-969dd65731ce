import { cn } from '@/lib/core/utils';
import React from 'react';

interface ContactInfoItemProps {
  icon: React.ReactNode;
  title: string;
  content: string;
  href?: string;
  className?: string;
}

const ContactInfoItem: React.FC<ContactInfoItemProps> = ({
  icon,
  title,
  content,
  href,
  className,
}) => {
  const ContentWrapper = href ? 'a' : 'div';
  const contentProps = href
    ? { href, target: '_blank', rel: 'noopener noreferrer' }
    : {};

  return (
    <div
      className={cn(
        'flex items-center gap-4 p-4 rounded-lg border border-primary/20 bg-card hover:border-primary/40 transition-all duration-300',
        className,
      )}
    >
      <div className="flex-shrink-0 w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center text-primary">
        {icon}
      </div>
      <div className="flex-1">
        <h3 className="font-semibold text-foreground text-sm mb-1">{title}</h3>
        <ContentWrapper
          {...contentProps}
          className={cn(
            'text-muted-foreground text-sm',
            href &&
              'hover:text-primary transition-colors duration-200 cursor-pointer',
          )}
        >
          {content}
        </ContentWrapper>
      </div>
    </div>
  );
};

export default ContactInfoItem;
