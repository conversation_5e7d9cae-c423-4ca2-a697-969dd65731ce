import axios, { AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { toast } from 'sonner';
import { TokenManager } from '../auth/token-manager';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './error-handler';

const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000,
  withCredentials: true,
});

api.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = TokenManager.getAccessToken();

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  },
);

api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = TokenManager.getRefreshToken();

        if (refreshToken) {
          const response = await axios.post(
            `${API_BASE_URL}/auth/refresh`,
            { refresh_token: '' },
            {
              withCredentials: true,
              timeout: 5000,
            },
          );

          if (response.data.access_token) {
            TokenManager.setTokens(
              response.data.access_token,
              response.data.refresh_token,
            );
            TokenManager.setUser(response.data.user);

            if (originalRequest.headers) {
              originalRequest.headers.Authorization = `Bearer ${response.data.access_token}`;
            }

            return api(originalRequest);
          }
        }
      } catch (refreshError) {
        console.warn('Token refresh failed:', refreshError);
        TokenManager.clearAll();
        if (typeof window !== 'undefined') {
          window.location.href = '/login';
        }
      }
    }

    const errorMessage = ErrorHandler.handleApiError(error);

    if (typeof window !== 'undefined') {
      toast.error(errorMessage, {
        className: 'font-bold! text-red-600! font-yekanBakhFaNum',
      });
    }

    return Promise.reject(error);
  },
);

export default api;
