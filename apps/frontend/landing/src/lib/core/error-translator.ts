interface ErrorTranslation {
  [key: string]: string;
}

interface ErrorResponse {
  error?: string;
  message?: string;
}

export class ErrorTranslator {
  private static readonly translations: ErrorTranslation = {
    validation_error: 'خطای اعتبارسنجی',
    authentication_error: 'خطای احراز هویت',
    authorization_error: 'عدم دسترسی',
    not_found: 'یافت نشد',
    conflict: 'تداخل',
    bad_request: 'درخواست نامعتبر',
    internal_server_error: 'خطای داخلی سرور',

    'User not found': 'کاربر یافت نشد',
    'Invalid credentials': 'اطلاعات ورود نامعتبر است',
    'Email already exists': 'این ایمیل قبلاً ثبت شده است',
    'Username already exists': 'این نام کاربری قبلاً گرفته شده است',
    'Passwords do not match': 'رمزهای عبور یکسان نیستند',
    'Invalid or expired token': 'توکن نامعتبر یا منقضی شده است',
    'Insufficient permissions': 'دسترسی کافی ندارید',
    'Invalid phone number format': 'فرمت شماره تلفن نامعتبر است',
    'Invalid OTP code': 'کد تأیید نامعتبر است',
    'OTP code has expired': 'کد تأیید منقضی شده است',
    'OTP code has already been used': 'این کد تأیید قبلاً استفاده شده است',
    'Too many OTP verification attempts':
      'تعداد تلاش‌های تأیید کد بیش از حد مجاز است',
    'Phone number not verified': 'شماره تلفن تأیید نشده است',
    'Phone number already exists': 'این شماره تلفن قبلاً ثبت شده است',
    'Failed to hash password': 'خطا در پردازش رمز عبور',
    'An unexpected error occurred': 'خطای غیرمنتظره‌ای رخ داد',

    'Database error': 'خطا در پایگاه داده',
    'Connection pool error': 'خطا در اتصال به پایگاه داده',
    'Redis error': 'خطا در سرویس کش',
    'JWT error': 'خطا در احراز هویت',

    'Network Error': 'خطا در اتصال به شبکه',
    'Request timeout': 'زمان درخواست به پایان رسید',
    'Connection refused': 'اتصال رد شد',
    'Server unavailable': 'سرور در دسترس نیست',

    'Required field': 'این فیلد الزامی است',
    'Invalid email format': 'فرمت ایمیل نامعتبر است',
    'Password too short': 'رمز عبور باید حداقل ۸ کاراکتر باشد',
    'Password must contain uppercase letter':
      'رمز عبور باید شامل حروف بزرگ باشد',
    'Password must contain lowercase letter':
      'رمز عبور باید شامل حروف کوچک باشد',
    'Password must contain number': 'رمز عبور باید شامل عدد باشد',
    'Password must contain special character':
      'رمز عبور باید شامل کاراکتر ویژه باشد',
  };

  private static readonly defaultMessages: ErrorTranslation = {
    '400': 'درخواست نامعتبر',
    '401': 'احراز هویت نشده‌اید',
    '403': 'دسترسی مجاز نیست',
    '404': 'صفحه یافت نشد',
    '409': 'تداخل در داده‌ها',
    '422': 'داده‌های ارسالی نامعتبر است',
    '429': 'تعداد درخواست‌ها بیش از حد مجاز است',
    '500': 'خطای داخلی سرور',
    '502': 'خطا در گیت‌وی',
    '503': 'سرویس موقتاً در دسترس نیست',
    '504': 'زمان انتظار گیت‌وی به پایان رسید',
  };

  public static translate(
    errorResponse: ErrorResponse,
    statusCode?: number,
  ): string {
    if (!errorResponse) {
      return 'خطای نامشخص';
    }

    const { error, message } = errorResponse;

    if (message && this.translations[message]) {
      return this.translations[message];
    }

    if (error && this.translations[error]) {
      return this.translations[error];
    }

    if (message && this.isEnglishText(message)) {
      const translatedMessage = this.findPartialMatch(message);
      if (translatedMessage) {
        return translatedMessage;
      }
    }

    if (statusCode && this.defaultMessages[statusCode.toString()]) {
      return this.defaultMessages[statusCode.toString()];
    }

    if (message) {
      return message;
    }

    if (error) {
      return error;
    }

    return 'خطای نامشخص';
  }

  public static translateNetworkError(error: any): string {
    if (!error) {
      return 'خطای نامشخص';
    }

    if (error.code === 'ECONNREFUSED') {
      return this.translations['Connection refused'];
    }

    if (error.code === 'ENOTFOUND') {
      return 'سرور یافت نشد';
    }

    if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
      return this.translations['Request timeout'];
    }

    if (error.message === 'Network Error') {
      return this.translations['Network Error'];
    }

    if (error.message && this.isEnglishText(error.message)) {
      const translatedMessage = this.findPartialMatch(error.message);
      if (translatedMessage) {
        return translatedMessage;
      }
    }

    return error.message || 'خطای شبکه';
  }

  private static isEnglishText(text: string): boolean {
    const englishRegex = /^[a-zA-Z0-9\s\.,;:!?\-_'"()]+$/;
    return englishRegex.test(text);
  }

  private static findPartialMatch(message: string): string | null {
    const lowerMessage = message.toLowerCase();

    for (const [key, value] of Object.entries(this.translations)) {
      if (lowerMessage.includes(key.toLowerCase())) {
        return value;
      }
    }

    if (lowerMessage.includes('password')) {
      return 'خطا در رمز عبور';
    }

    if (lowerMessage.includes('email')) {
      return 'خطا در ایمیل';
    }

    if (lowerMessage.includes('phone')) {
      return 'خطا در شماره تلفن';
    }

    if (lowerMessage.includes('username')) {
      return 'خطا در نام کاربری';
    }

    if (
      lowerMessage.includes('validation') ||
      lowerMessage.includes('invalid')
    ) {
      return 'اطلاعات وارد شده نامعتبر است';
    }

    if (lowerMessage.includes('required') || lowerMessage.includes('missing')) {
      return 'اطلاعات الزامی وارد نشده است';
    }

    if (lowerMessage.includes('expired')) {
      return 'منقضی شده است';
    }

    if (
      lowerMessage.includes('already exists') ||
      lowerMessage.includes('duplicate')
    ) {
      return 'این اطلاعات قبلاً ثبت شده است';
    }

    return null;
  }
}
