import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  ArrowRight,
  ClipboardList,
  Headphones,
  Rocket,
  RotateCcw,
  Settings,
  Target,
  Zap,
} from 'lucide-react';
import React from 'react';

interface FeaturesSectionProps {
  className?: string;
}

const FeaturesSection: React.FC<FeaturesSectionProps> = ({ className }) => {
  const features = [
    {
      icon: ClipboardList,
      title: 'دسترسی نامحدود',
      description:
        'دسترسی به طیف گسترده‌ای از منابع و تخصص با امتیاز ویژه ما، ارائه پشتیبانی نامحدود در دلفک.',
      action: 'بیشتر بدانید',
    },
    {
      icon: Zap,
      title: 'تحویل سریع',
      description:
        'زمان‌بندی پروژه‌های خود را با فرآیندهای ساده‌ما تسریع کنید و تحویل سریع و نتایج کارآمد را تضمین کنید.',
      action: 'بیشتر بدانید',
    },
    {
      icon: Target,
      title: 'منحصر به فرد و متعلق به شما',
      description:
        'راهکارهای انحصاری متناسب با نیازهای شما را باز کنید و سفارشی‌سازی و مالکیت بی‌نظیر پروژه‌هایتان را فراهم کنید.',
      action: 'بیشتر بدانید',
    },
    {
      icon: RotateCcw,
      title: 'انعطاف‌پذیر',
      description:
        'با راهکارهای همه‌کاره ما بدون زحمت با نیازهای در حال تغییر سازگار شوید و تنظیمات یکپارچه و پاسخ‌های چابک ارائه دهید.',
      action: 'بیشتر بدانید',
    },
    {
      icon: Headphones,
      title: 'پشتیبانی اختصاصی',
      description:
        'روی پشتیبانی اختصاصی برای راهکارهای سریع و شخصی‌سازی شده حساب کنید - تضمین سفری یکپارچه با ما.',
      action: 'بیشتر بدانید',
    },
    {
      icon: Settings,
      title: 'قابل سفارشی‌سازی',
      description:
        'راهکارها را متناسب با ترجیحات و نیازهای منحصر به فرد خود تنظیم کنید و نتایج شخصی‌سازی شده و تجربه کاربری بهبود یافته را تضمین کنید.',
      action: 'بیشتر بدانید',
    },
  ];

  return (
    <section
      id="features"
      className={`py-8 sm:py-12 lg:py-16 xl:py-20 bg-gradient-to-br from-background via-card to-secondary relative overflow-hidden ${className || ''}`}
    >
      {/* Enhanced geeky decorative elements */}
      <div className="absolute -top-40 -left-40 w-[clamp(12rem,25vw,20rem)] h-[clamp(12rem,25vw,20rem)] rounded-full bg-primary/8 mix-blend-screen filter blur-3xl cyber-glow"></div>
      <div className="absolute -bottom-20 right-20 w-[clamp(10rem,20vw,16rem)] h-[clamp(10rem,20vw,16rem)] rounded-full bg-accent/8 mix-blend-screen filter blur-3xl cyber-glow"></div>

      {/* Binary floating elements with data stream */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 text-primary/[0.03] font-mono text-sm rotate-45 float data-stream">
          01001000
        </div>
        <div
          className="absolute top-1/3 right-1/3 text-accent/[0.03] font-mono text-sm -rotate-45 float"
          style={{ animationDelay: '2s' }}
        >
          11100111
        </div>
        <div
          className="absolute bottom-1/4 left-1/2 text-primary/[0.03] font-mono text-sm rotate-12 float"
          style={{ animationDelay: '4s' }}
        >
          10101010
        </div>
      </div>

      <div className="container relative z-10 mx-auto px-4">
        <div className="text-center mb-8 sm:mb-12 lg:mb-16">
          <Badge
            variant="outline"
            className="mb-3 sm:mb-4 px-3 sm:px-4 py-2 bg-card/80 text-primary rounded-lg text-xs sm:text-sm font-mono border border-primary/20 cyber-glow"
          >
            <span className="text-accent">function</span>{' '}
            {'custom_projects() { return solutions; }'}
          </Badge>
          <h2 className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold mb-3 sm:mb-4 font-mono">
            <span className="text-foreground holographic-text">
              پروژه‌های سفارشی متناسب با نیازهای شما
            </span>
          </h2>
          <div className="max-w-2xl mx-auto mb-3 sm:mb-4 text-sm sm:text-base leading-relaxed font-mono code-block-light">
            <div className="text-accent mb-2">
              // توسعه نرم‌افزار آسان شده - ساده‌سازی راهکارها برای موفقیت شما
            </div>
            <div>
              تیم ما از توسعه‌دهندگان با استعداد و علاقه‌مند اینجا هستند تا
              انتظارات شما را فراتر ببرند و خدمات فوق‌العاده ارائه دهند.
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
          {features.map((feature, index) => (
            <Card
              key={index}
              className="bg-card/80 backdrop-blur-sm border border-primary/20 shadow-sm transition-all duration-300 hover:border-primary/40 hover:scale-105 hover:shadow-md terminal-window group"
            >
              <CardHeader className="text-center pb-3">
                <CardTitle
                  className="text-sm sm:text-base lg:text-lg font-bold mb-2 font-mono text-primary glitch"
                  data-text={feature.title}
                >
                  {feature.title}
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center pt-0">
                <CardDescription className="text-xs sm:text-sm lg:text-base text-foreground mb-3 sm:mb-4 leading-relaxed">
                  {feature.description}
                </CardDescription>
                <Separator className="my-4 bg-primary/20" />
                <Button
                  variant="outline"
                  size="sm"
                  className="font-mono text-xs sm:text-sm bg-gradient-to-r from-primary/20 to-accent/20 hover:from-primary/30 hover:to-accent/30 border-primary/30 text-primary hover:border-primary/50 shadow-lg hover:scale-105 transition-all duration-300 group flex items-center justify-center gap-2 sm:gap-3 px-3 sm:px-4 py-2 sm:py-3 neo-brutal"
                >
                  <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 group-hover:-translate-x-1 transition-transform order-2" />
                  <span className="order-1">{feature.action}</span>
                  <Rocket className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 group-hover:animate-pulse order-0" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};
export default FeaturesSection;
