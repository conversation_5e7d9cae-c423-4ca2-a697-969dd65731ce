'use client';
import { cn } from '@/lib/core/utils';
import { AlertCircle, CheckCircle, Clock } from 'lucide-react';
import React from 'react';
import { WizardTheme } from './wizard-types';

interface WizardProgressStep {
  id: string;
  title: string;
  isOptional?: boolean;
  icon?: React.ComponentType<{ className?: string }>;
  estimatedTime?: number;
}

interface WizardProgressProps {
  currentStep: number;
  totalSteps: number;
  steps: WizardProgressStep[];
  completedSteps?: Set<number>;
  hasErrors?: Record<number, boolean>;
  variant?: WizardTheme['progressVariant'];
  showLabels?: boolean;
  showProgress?: boolean;
  clickable?: boolean;
  onStepClick?: (stepIndex: number) => void;
}

export const WizardProgress: React.FC<WizardProgressProps> = ({
  currentStep,
  totalSteps,
  steps,
  completedSteps = new Set(),
  hasErrors = {},
  variant = 'line',
  showLabels = true,
  showProgress = true,
  clickable = false,
  onStepClick,
}) => {
  const getStepStatus = (index: number) => {
    if (hasErrors[index]) return 'error';
    if (completedSteps.has(index)) return 'completed';
    if (index === currentStep) return 'current';
    if (index < currentStep) return 'completed';
    return 'pending';
  };

  const getStepIcon = (
    step: WizardProgressStep,
    index: number,
    status: string,
  ) => {
    const iconClass = 'w-4 h-4';
    const calcIndex = index + 1;
    if (status === 'error') return <AlertCircle className={iconClass} />;
    if (status === 'completed') return <CheckCircle className={iconClass} />;
    if (step.icon) return <step.icon className={iconClass} />;
    return (
      <span className="text-xs font-medium">
        {calcIndex.toLocaleString('fa-IR')}
      </span>
    );
  };

  const getStepStyles = (status: string) => {
    const baseStyles = 'transition-all duration-300 ease-in-out';

    switch (status) {
      case 'completed':
        return cn(
          baseStyles,
          'bg-primary text-primary-foreground border-primary',
        );
      case 'current':
        return cn(
          baseStyles,
          'bg-primary text-primary-foreground border-primary ring-2 ring-primary/20',
        );
      case 'error':
        return cn(
          baseStyles,
          'bg-destructive text-destructive-foreground border-destructive',
        );
      default:
        return cn(
          baseStyles,
          'bg-muted text-muted-foreground border-border hover:bg-muted/80',
        );
    }
  };

  const renderDotsVariant = () => (
    <div className="flex items-center justify-center space-x-2 rtl:space-x-reverse mb-4">
      {steps.map((_, index) => {
        const status = getStepStatus(index);
        return (
          <button
            key={index}
            type="button"
            disabled={!clickable}
            onClick={() => clickable && onStepClick?.(index)}
            className={cn(
              'w-3 h-3 rounded-full border-2',
              getStepStyles(status),
              clickable && 'cursor-pointer hover:scale-110',
              !clickable && 'cursor-default',
            )}
          />
        );
      })}
    </div>
  );

  const renderStepsVariant = () => (
    <div className="flex items-center justify-between mb-4">
      {steps.map((step, index) => {
        const status = getStepStatus(index);
        const isClickable =
          clickable && (completedSteps.has(index) || index <= currentStep);

        return (
          <div
            key={step.id}
            className={cn(
              'flex items-center',
              index < totalSteps - 1 ? 'flex-1' : '',
            )}
          >
            <div className="flex flex-col items-center">
              <button
                type="button"
                disabled={!isClickable}
                onClick={() => isClickable && onStepClick?.(index)}
                className={cn(
                  'w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium border-2',
                  getStepStyles(status),
                  isClickable && 'cursor-pointer hover:scale-105',
                  !isClickable && 'cursor-default',
                )}
              >
                {getStepIcon(step, index, status)}
              </button>

              {showLabels && (
                <div className="text-center mt-2 max-w-20">
                  <span
                    className={cn(
                      'text-xs block',
                      status === 'current'
                        ? 'text-foreground font-medium'
                        : 'text-muted-foreground',
                    )}
                  >
                    {step.title}
                  </span>

                  {step.isOptional && (
                    <span className="text-xs text-muted-foreground">
                      (اختیاری)
                    </span>
                  )}

                  {step.estimatedTime && (
                    <div className="flex items-center justify-center mt-1 text-xs text-muted-foreground">
                      <Clock className="w-3 h-3 ml-1" />
                      {step.estimatedTime}م
                    </div>
                  )}
                </div>
              )}
            </div>

            {index < totalSteps - 1 && (
              <div
                className={cn(
                  'flex-1 h-px mx-4 transition-colors duration-300',
                  completedSteps.has(index) ? 'bg-primary' : 'bg-muted',
                )}
              />
            )}
          </div>
        );
      })}
    </div>
  );

  const progressPercentage =
    totalSteps > 0 ? Math.round(((currentStep + 1) / totalSteps) * 100) : 0;

  return (
    <div className="w-full mb-8">
      {variant === 'dots' && renderDotsVariant()}
      {variant === 'steps' && renderStepsVariant()}
      {variant === 'line' && (
        <div className="w-full bg-muted rounded-full h-2 overflow-hidden mb-4">
          <div
            className="bg-primary h-2 rounded-full transition-all duration-500 ease-out"
            style={{
              width: `${progressPercentage}%`,
            }}
          />
        </div>
      )}

      {showProgress && (
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">
              مرحله{' '}
              {Math.min(currentStep + 1, totalSteps).toLocaleString('fa-IR')} از{' '}
              {totalSteps.toLocaleString('fa-IR')}
            </span>
            <span className="text-muted-foreground">
              {progressPercentage.toLocaleString('fa-IR')}%
            </span>
          </div>

          <div className="w-full bg-muted rounded-full h-2 overflow-hidden">
            <div
              className="bg-primary h-2 rounded-full transition-all duration-500 ease-out"
              style={{
                width: `${progressPercentage}%`,
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
};
