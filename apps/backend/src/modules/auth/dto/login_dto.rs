use serde::{Deserialize, Serialize};
use validator::<PERSON>ida<PERSON>;
use utoipa::ToSchema;

#[derive(Debug, Deserialize, Serialize, Validate, ToSchema)]
pub struct LoginDto {
    #[validate(email(message = "Invalid email format"))]
    #[schema(example = "<EMAIL>")]
    pub email: String,

    #[validate(length(min = 1, message = "Password is required"))]
    #[schema(example = "password123")]
    pub password: String,
}
