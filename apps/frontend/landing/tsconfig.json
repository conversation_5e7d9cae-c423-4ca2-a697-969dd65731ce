{"compilerOptions": {"target": "ESNext", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": false, "outDir": "./dist", "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"]}}, "include": ["**/*.ts", "**/*.tsx", "next-env.d.ts", "sitemap.config.mts", "src/scripts/sitemap-generator.ts", ".next/types/**/*.ts"], "exclude": ["node_modules", ".next", "dist", "build"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}