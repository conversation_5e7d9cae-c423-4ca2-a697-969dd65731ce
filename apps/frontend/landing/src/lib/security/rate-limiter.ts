interface RateLimitEntry {
  count: number;
  resetTime: number;
  blocked: boolean;
}

export class RateLimiter {
  private static readonly STORAGE_KEY = 'auth_rate_limit';
  private static readonly MAX_ATTEMPTS = 5;
  private static readonly WINDOW_MS = 15 * 60 * 1000; // 15 minutes
  private static readonly BLOCK_DURATION_MS = 30 * 60 * 1000; // 30 minutes

  private static getKey(identifier: string, action: string): string {
    return `${action}_${identifier}`;
  }

  private static getRateLimitData(): Record<string, RateLimitEntry> {
    if (typeof window === 'undefined') return {};

    try {
      const data = localStorage.getItem(this.STORAGE_KEY);
      return data ? JSON.parse(data) : {};
    } catch {
      return {};
    }
  }

  private static setRateLimitData(data: Record<string, RateLimitEntry>): void {
    if (typeof window === 'undefined') return;

    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(data));
    } catch {
      // Handle storage errors gracefully
    }
  }

  private static cleanupExpiredEntries(
    data: Record<string, RateLimitEntry>,
  ): Record<string, RateLimitEntry> {
    const now = Date.now();
    const cleaned: Record<string, RateLimitEntry> = {};

    Object.entries(data).forEach(([key, entry]) => {
      if (entry.blocked && now < entry.resetTime) {
        cleaned[key] = entry;
      } else if (!entry.blocked && now < entry.resetTime) {
        cleaned[key] = entry;
      }
      // Expired entries are not added to cleaned data
    });

    return cleaned;
  }

  static isBlocked(identifier: string, action: string): boolean {
    const key = this.getKey(identifier, action);
    const data = this.getRateLimitData();
    const entry = data[key];

    if (!entry) return false;

    const now = Date.now();

    if (entry.blocked && now < entry.resetTime) {
      return true;
    }

    if (entry.blocked && now >= entry.resetTime) {
      // Block period expired, remove entry
      delete data[key];
      this.setRateLimitData(data);
      return false;
    }

    return false;
  }

  static getTimeUntilReset(identifier: string, action: string): number {
    const key = this.getKey(identifier, action);
    const data = this.getRateLimitData();
    const entry = data[key];

    if (!entry || !entry.blocked) return 0;

    const now = Date.now();
    return Math.max(0, entry.resetTime - now);
  }

  static recordAttempt(
    identifier: string,
    action: string,
    failed: boolean,
  ): void {
    if (!failed) return; // Don't record successful attempts

    const key = this.getKey(identifier, action);
    let data = this.getRateLimitData();

    // Cleanup expired entries
    data = this.cleanupExpiredEntries(data);

    const now = Date.now();
    const entry = data[key];

    if (!entry) {
      // First failed attempt
      data[key] = {
        count: 1,
        resetTime: now + this.WINDOW_MS,
        blocked: false,
      };
    } else if (now >= entry.resetTime && !entry.blocked) {
      // Window expired, reset
      data[key] = {
        count: 1,
        resetTime: now + this.WINDOW_MS,
        blocked: false,
      };
    } else {
      // Increment count
      entry.count += 1;

      if (entry.count >= this.MAX_ATTEMPTS) {
        entry.blocked = true;
        entry.resetTime = now + this.BLOCK_DURATION_MS;
      }
    }

    this.setRateLimitData(data);
  }

  static getRemainingAttempts(identifier: string, action: string): number {
    const key = this.getKey(identifier, action);
    const data = this.getRateLimitData();
    const entry = data[key];

    if (!entry || entry.blocked) return 0;

    const now = Date.now();
    if (now >= entry.resetTime) return this.MAX_ATTEMPTS;

    return Math.max(0, this.MAX_ATTEMPTS - entry.count);
  }

  static formatTimeRemaining(ms: number): string {
    const minutes = Math.ceil(ms / (60 * 1000));
    if (minutes < 1) return 'کمتر از یک دقیقه';
    if (minutes === 1) return 'یک دقیقه';
    return `${minutes} دقیقه`;
  }

  static clearRateLimit(identifier: string, action: string): void {
    const key = this.getKey(identifier, action);
    const data = this.getRateLimitData();
    delete data[key];
    this.setRateLimitData(data);
  }

  static clearAllRateLimits(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(this.STORAGE_KEY);
    }
  }
}
