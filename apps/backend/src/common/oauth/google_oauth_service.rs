use reqwest::Client;
use serde::{Deserialize, Serialize};
use crate::common::{CommonModule, errors::AppError};
use uuid::Uuid;

#[derive(Debug, Serialize)]
pub struct GoogleTokenRequest {
    pub client_id: String,
    pub client_secret: String,
    pub code: String,
    pub grant_type: String,
    pub redirect_uri: String,
}

#[derive(Debug, Deserialize)]
#[allow(dead_code)]
pub struct GoogleTokenResponse {
    pub access_token: String,
    pub expires_in: i64,
    pub refresh_token: Option<String>,
    pub scope: String,
    pub token_type: String,
    pub id_token: Option<String>,
}

#[derive(Debug, Deserialize)]
#[allow(dead_code)]
pub struct GoogleUserInfo {
    pub id: String,
    pub email: String,
    pub verified_email: bool,
    pub name: String,
    pub given_name: Option<String>,
    pub family_name: Option<String>,
    pub picture: Option<String>,
    pub locale: Option<String>,
}

pub struct GoogleOAuthService {
    client: Client,
    common_module: CommonModule,
}

impl GoogleOAuthService {
    pub fn new(common_module: &CommonModule) -> Self {
        Self {
            client: Client::new(),
            common_module: common_module.clone(),
        }
    }

    #[allow(dead_code)]
    pub fn get_auth_url(&self, state: Option<&str>) -> String {
        let default_state = Uuid::new_v4().to_string();
        let state = state.unwrap_or(&default_state);
        self.common_module.config.google_oauth.get_auth_url(state)
    }

    pub async fn exchange_code_for_token(&self, code: &str) -> Result<GoogleTokenResponse, AppError> {
        let token_request = GoogleTokenRequest {
            client_id: self.common_module.config.google_oauth.client_id.clone(),
            client_secret: self.common_module.config.google_oauth.client_secret.clone(),
            code: code.to_string(),
            grant_type: "authorization_code".to_string(),
            redirect_uri: self.common_module.config.google_oauth.redirect_uri.clone(),
        };

        // Log the token request for debugging (without sensitive data)
        println!("Token exchange request - Client ID: {}, Redirect URI: {}, Grant Type: {}",
                 &token_request.client_id[..10], &token_request.redirect_uri, &token_request.grant_type);

        let response = self
            .client
            .post(&self.common_module.config.google_oauth.token_url)
            .header("Accept", "application/json")
            .header("Content-Type", "application/x-www-form-urlencoded")
            .form(&token_request)
            .send()
            .await
            .map_err(|e| AppError::InternalServer(format!("Failed to request Google token: {e}")))?;

        let status = response.status();
        let response_text = response.text().await.unwrap_or_else(|_| "Unable to read response".to_string());

        if !status.is_success() {
            // Log the full error response for debugging
            println!("Google token request failed with status {status}: {response_text}");
            return Err(AppError::InternalServer(format!("Google token request failed: {response_text}")));
        }

        let token_response: GoogleTokenResponse = serde_json::from_str(&response_text)
            .map_err(|e| AppError::InternalServer(format!("Failed to parse Google token response: {e} - Response: {response_text}")))?;

        Ok(token_response)
    }

    pub async fn get_user_info(&self, access_token: &str) -> Result<GoogleUserInfo, AppError> {
        let response = self
            .client
            .get(&self.common_module.config.google_oauth.userinfo_url)
            .bearer_auth(access_token)
            .send()
            .await
            .map_err(|e| AppError::InternalServer(format!("Failed to request Google user info: {e}")))?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
            return Err(AppError::InternalServer(format!("Google user info request failed: {error_text}")));
        }

        let user_info: GoogleUserInfo = response
            .json()
            .await
            .map_err(|e| AppError::InternalServer(format!("Failed to parse Google user info: {e}")))?;

        Ok(user_info)
    }

    pub async fn authenticate_user(&self, code: &str) -> Result<GoogleUserInfo, AppError> {
        let token_response = self.exchange_code_for_token(code).await?;
        let user_info = self.get_user_info(&token_response.access_token).await?;
        Ok(user_info)
    }
}
