use actix_web::{web, HttpResponse, Result, ResponseError, http::header::{HeaderValue, SET_COOKIE, LOCATION}};
use crate::common::CommonModule;
use crate::common::logger::LoggerService;
use crate::modules::shared::otp::{SendOtpDto, VerifyOtpDto, PhoneLoginDto};
use super::{auth_service::AuthService, google_oauth_service::AuthGoogleService, dto::{LoginDto, RegisterDto, AuthResponseDto, RefreshTokenDto, GoogleAuthCallbackDto, GoogleAuthUrlDto}};
use crate::common::validators::DtoValidator;
use uuid::Uuid;

pub struct AuthController {
    auth_service: AuthService,
    google_oauth_service: AuthGoogleService,
}

#[utoipa::path(
    post,
    path = "/api/auth/login",
    request_body = LoginDto,
    responses(
        (status = 200, description = "Login successful", body = AuthResponseDto),
        (status = 400, description = "Invalid credentials", body = crate::common::errors::ErrorResponse),
        (status = 422, description = "Validation error", body = crate::common::errors::ErrorResponse)
    ),
    tag = "auth"
)]
pub async fn login(
    auth_controller: web::Data<AuthController>,
    login_dto: web::Json<LoginDto>,
) -> Result<HttpResponse> {
    login_dto.validate_dto().map_err(actix_web::error::ErrorBadRequest)?;

    match auth_controller.auth_service.login(&login_dto).await {
        Ok(response) => {
            LoggerService::log_auth_event("login", Some(&response.user.email), true);

            let access_cookie = format!(
                "access_token={}; HttpOnly; Secure; SameSite=None; Max-Age={}; Path=/",
                response.access_token,
                response.expires_in
            );

            let refresh_cookie = format!(
                "refresh_token={}; HttpOnly; Secure; SameSite=None; Max-Age=604800; Path=/",
                response.refresh_token
            );

            Ok(HttpResponse::Ok()
                .insert_header((SET_COOKIE, HeaderValue::from_str(&access_cookie).unwrap()))
                .insert_header((SET_COOKIE, HeaderValue::from_str(&refresh_cookie).unwrap()))
                .json(response))
        },
        Err(e) => {
            LoggerService::log_auth_event("login", Some(&login_dto.email), false);
            let error_msg = e.to_string();
            LoggerService::log_error(&format!("Login failed for {}: {}", &login_dto.email, error_msg), Some("AuthController"));
            Ok(e.error_response())
        }
    }
}

#[utoipa::path(
    post,
    path = "/api/auth/register",
    request_body = RegisterDto,
    responses(
        (status = 201, description = "Registration successful", body = AuthResponseDto),
        (status = 400, description = "Registration failed", body = crate::common::errors::ErrorResponse),
        (status = 422, description = "Validation error", body = crate::common::errors::ErrorResponse)
    ),
    tag = "auth"
)]
pub async fn register(
    auth_controller: web::Data<AuthController>,
    register_dto: web::Json<RegisterDto>,
) -> Result<HttpResponse> {
    register_dto.validate_dto().map_err(actix_web::error::ErrorBadRequest)?;

    match auth_controller.auth_service.register(&register_dto).await {
        Ok(response) => {
            LoggerService::log_auth_event("register", Some(&response.user.email), true);

            let access_cookie = format!(
                "access_token={}; HttpOnly; Secure; SameSite=None; Max-Age={}; Path=/",
                response.access_token,
                response.expires_in
            );

            let refresh_cookie = format!(
                "refresh_token={}; HttpOnly; Secure; SameSite=None; Max-Age=604800; Path=/",
                response.refresh_token
            );

            Ok(HttpResponse::Created()
                .insert_header((SET_COOKIE, HeaderValue::from_str(&access_cookie).unwrap()))
                .insert_header((SET_COOKIE, HeaderValue::from_str(&refresh_cookie).unwrap()))
                .json(response))
        },
        Err(e) => {
            LoggerService::log_auth_event("register", Some(&register_dto.email), false);
            let error_msg = e.to_string();
            LoggerService::log_error(&format!("Registration failed for {}: {}", &register_dto.email, error_msg), Some("AuthController"));
            Ok(e.error_response())
        }
    }
}

#[utoipa::path(
    post,
    path = "/api/auth/refresh",
    request_body = RefreshTokenDto,
    responses(
        (status = 200, description = "Token refreshed successfully", body = AuthResponseDto),
        (status = 401, description = "Invalid or expired token", body = crate::common::errors::ErrorResponse),
        (status = 422, description = "Validation error", body = crate::common::errors::ErrorResponse)
    ),
    tag = "auth"
)]
pub async fn refresh_token(
    auth_controller: web::Data<AuthController>,
    refresh_dto: web::Json<RefreshTokenDto>,
    req: actix_web::HttpRequest,
) -> Result<HttpResponse> {
    refresh_dto.validate_dto().map_err(actix_web::error::ErrorBadRequest)?;

    let refresh_token = if !refresh_dto.refresh_token.is_empty() {
        refresh_dto.refresh_token.clone()
    } else {
        let jwt_strategy = super::jwt_strategy::JwtStrategy::new(auth_controller.auth_service.get_jwt_config());
        let auth_guard = super::auth_guard::AuthGuard::new(jwt_strategy);
        auth_guard.extract_refresh_token_from_cookies(&req)?
            .ok_or_else(|| actix_web::error::ErrorBadRequest("No refresh token provided"))?
    };

    match auth_controller.auth_service.refresh_token(&refresh_token).await {
        Ok(response) => {
            LoggerService::log_auth_event("refresh_token", Some(&response.user.email), true);

            let access_cookie = format!(
                "access_token={}; HttpOnly; Secure; SameSite=None; Max-Age={}; Path=/",
                response.access_token,
                response.expires_in
            );

            let refresh_cookie = format!(
                "refresh_token={}; HttpOnly; Secure; SameSite=None; Max-Age=604800; Path=/",
                response.refresh_token
            );

            Ok(HttpResponse::Ok()
                .insert_header((SET_COOKIE, HeaderValue::from_str(&access_cookie).unwrap()))
                .insert_header((SET_COOKIE, HeaderValue::from_str(&refresh_cookie).unwrap()))
                .json(response))
        },
        Err(e) => {
            LoggerService::log_auth_event("refresh_token", None, false);
            let error_msg = e.to_string();
            LoggerService::log_error(&format!("Token refresh failed: {error_msg}"), Some("AuthController"));
            Ok(e.error_response())
        }
    }
}

#[utoipa::path(
    post,
    path = "/api/auth/logout",
    responses(
        (status = 200, description = "Logout successful"),
        (status = 401, description = "Invalid or expired token")
    ),
    security(
        ("bearer_auth" = [])
    ),
    tag = "auth"
)]
pub async fn logout(
    auth_controller: web::Data<AuthController>,
    req: actix_web::HttpRequest,
) -> Result<HttpResponse> {
    let jwt_strategy = super::jwt_strategy::JwtStrategy::new(auth_controller.auth_service.get_jwt_config());
    let auth_guard = super::auth_guard::AuthGuard::new(jwt_strategy);

    let mut token_found = false;

    if let Ok(Some(token)) = auth_guard.extract_token_from_request(&req) {
        match auth_controller.auth_service.logout(&token).await {
            Ok(_) => {
                LoggerService::log_auth_event("logout", None, true);
                token_found = true;
            },
            Err(e) => {
                LoggerService::log_auth_event("logout", None, false);
                return Ok(e.error_response());
            }
        }
    }

    let clear_access_cookie = "access_token=; HttpOnly; Secure; SameSite=None; Max-Age=0; Path=/";
    let clear_refresh_cookie = "refresh_token=; HttpOnly; Secure; SameSite=None; Max-Age=0; Path=/";

    if token_found {
        Ok(HttpResponse::Ok()
            .insert_header((SET_COOKIE, HeaderValue::from_str(clear_access_cookie).unwrap()))
            .insert_header((SET_COOKIE, HeaderValue::from_str(clear_refresh_cookie).unwrap()))
            .json(serde_json::json!({
                "message": "Logged out successfully"
            })))
    } else {
        LoggerService::log_auth_event("logout", None, false);
        Ok(HttpResponse::Unauthorized()
            .insert_header((SET_COOKIE, HeaderValue::from_str(clear_access_cookie).unwrap()))
            .insert_header((SET_COOKIE, HeaderValue::from_str(clear_refresh_cookie).unwrap()))
            .json(serde_json::json!({
                "error": "Missing or invalid authorization header"
            })))
    }
}
#[utoipa::path(
    post,
    path = "/api/auth/send-otp",
    request_body = SendOtpDto,
    responses(
        (status = 200, description = "OTP sent successfully"),
        (status = 400, description = "Invalid phone number or SMS sending failed", body = crate::common::errors::ErrorResponse),
        (status = 422, description = "Validation error", body = crate::common::errors::ErrorResponse)
    ),
    tag = "auth"
)]
pub async fn send_otp(
    auth_controller: web::Data<AuthController>,
    send_otp_dto: web::Json<SendOtpDto>,
) -> Result<HttpResponse> {
    send_otp_dto.validate_dto().map_err(actix_web::error::ErrorBadRequest)?;

    match auth_controller.auth_service.send_otp(&send_otp_dto).await {
        Ok(_) => {
            LoggerService::info(&format!("OTP sent to phone: {}", &send_otp_dto.phone));
            Ok(HttpResponse::Ok().json(serde_json::json!({
                "message": "OTP sent successfully"
            })))
        },
        Err(e) => {
            let error_msg = e.to_string();
            LoggerService::log_error(&format!("OTP sending failed for {}: {}", &send_otp_dto.phone, error_msg), Some("AuthController"));
            Ok(e.error_response())
        }
    }
}

#[utoipa::path(
    post,
    path = "/api/auth/verify-otp",
    request_body = VerifyOtpDto,
    responses(
        (status = 200, description = "OTP verified successfully"),
        (status = 400, description = "Invalid OTP code", body = crate::common::errors::ErrorResponse),
        (status = 422, description = "Validation error", body = crate::common::errors::ErrorResponse)
    ),
    tag = "auth"
)]
pub async fn verify_otp(
    auth_controller: web::Data<AuthController>,
    verify_otp_dto: web::Json<VerifyOtpDto>,
) -> Result<HttpResponse> {
    verify_otp_dto.validate_dto().map_err(actix_web::error::ErrorBadRequest)?;

    match auth_controller.auth_service.verify_otp(&verify_otp_dto).await {
        Ok(_) => {
            LoggerService::info(&format!("OTP verified for phone: {}", &verify_otp_dto.phone));
            Ok(HttpResponse::Ok().json(serde_json::json!({
                "message": "OTP verified successfully"
            })))
        },
        Err(e) => {
            let error_msg = e.to_string();
            LoggerService::log_error(&format!("OTP verification failed for {}: {}", &verify_otp_dto.phone, error_msg), Some("AuthController"));
            Ok(e.error_response())
        }
    }
}

#[utoipa::path(
    post,
    path = "/api/auth/phone-login",
    request_body = PhoneLoginDto,
    responses(
        (status = 200, description = "Phone login successful", body = AuthResponseDto),
        (status = 400, description = "Invalid OTP or user not found", body = crate::common::errors::ErrorResponse),
        (status = 422, description = "Validation error", body = crate::common::errors::ErrorResponse)
    ),
    tag = "auth"
)]
pub async fn phone_login(
    auth_controller: web::Data<AuthController>,
    phone_login_dto: web::Json<PhoneLoginDto>,
) -> Result<HttpResponse> {
    phone_login_dto.validate_dto().map_err(actix_web::error::ErrorBadRequest)?;

    match auth_controller.auth_service.phone_otp_login(&phone_login_dto).await {
        Ok(response) => {
            LoggerService::log_auth_event("phone_login", Some(&phone_login_dto.phone), true);

            let access_cookie = format!(
                "access_token={}; HttpOnly; Secure; SameSite=None; Max-Age={}; Path=/",
                response.access_token,
                response.expires_in
            );

            let refresh_cookie = format!(
                "refresh_token={}; HttpOnly; Secure; SameSite=None; Max-Age=604800; Path=/",
                response.refresh_token
            );

            Ok(HttpResponse::Ok()
                .insert_header((SET_COOKIE, HeaderValue::from_str(&access_cookie).unwrap()))
                .insert_header((SET_COOKIE, HeaderValue::from_str(&refresh_cookie).unwrap()))
                .json(response))
        },
        Err(e) => {
            LoggerService::log_auth_event("phone_login", Some(&phone_login_dto.phone), false);
            let error_msg = e.to_string();
            LoggerService::log_error(&format!("Phone login failed for {}: {}", &phone_login_dto.phone, error_msg), Some("AuthController"));
            Ok(e.error_response())
        }
    }
}

#[utoipa::path(
    get,
    path = "/api/auth/google/url",
    responses(
        (status = 200, description = "Google OAuth URL generated", body = GoogleAuthUrlDto),
    ),
    tag = "auth"
)]
pub async fn google_auth_url(
    auth_controller: web::Data<AuthController>,
) -> Result<HttpResponse> {
    let state = Uuid::new_v4().to_string();
    let auth_url = auth_controller.google_oauth_service.get_auth_url(Some(&state));

    let response = GoogleAuthUrlDto {
        auth_url,
        state: state.clone(),
    };

    LoggerService::debug(&format!("Google OAuth URL generated with state: {state}"));
    Ok(HttpResponse::Ok().json(response))
}

#[utoipa::path(
    get,
    path = "/api/auth/google/callback",
    params(
        ("code" = String, Query, description = "Authorization code from Google"),
        ("state" = String, Query, description = "State parameter for CSRF protection")
    ),
    responses(
        (status = 302, description = "Redirect to frontend with authentication result"),
        (status = 400, description = "Invalid OAuth code or state")
    ),
    tag = "auth"
)]
pub async fn google_callback_redirect(
    auth_controller: web::Data<AuthController>,
    query: web::Query<GoogleAuthCallbackDto>,
) -> Result<HttpResponse> {
    // Extract role from state parameter instead of query parameters
    let role = if let Some(state) = &query.state {
        if state.contains("role=buyer") {
            Some(crate::modules::users::entities::UserRole::Buyer)
        } else if state.contains("role=coder") {
            Some(crate::modules::users::entities::UserRole::Coder)
        } else {
            None
        }
    } else {
        None
    };

    match auth_controller.google_oauth_service.handle_callback_with_role(&query.code, role).await {
        Ok(response) => {
            LoggerService::log_auth_event("google_oauth", Some(&response.user.email), true);

            let frontend_redirect_url = format!(
                "http://localhost:3000/?access_token={}&refresh_token={}&expires_in={}",
                urlencoding::encode(&response.access_token),
                urlencoding::encode(&response.refresh_token),
                response.expires_in
            );

            Ok(HttpResponse::Found()
                .insert_header((LOCATION, frontend_redirect_url))
                .finish())
        },
        Err(e) => {
            LoggerService::log_auth_event("google_oauth", None, false);
            let error_msg = e.to_string();
            LoggerService::log_error(&format!("Google OAuth failed: {error_msg}"), Some("AuthController"));

            let error_redirect_url = format!(
                "http://localhost:3000/?error={}",
                urlencoding::encode(&error_msg)
            );

            Ok(HttpResponse::Found()
                .insert_header((LOCATION, error_redirect_url))
                .finish())
        }
    }
}

#[utoipa::path(
    post,
    path = "/api/auth/google/callback",
    request_body = GoogleAuthCallbackDto,
    responses(
        (status = 200, description = "Google OAuth authentication successful", body = AuthResponseDto),
        (status = 400, description = "Invalid OAuth code", body = crate::common::errors::ErrorResponse),
        (status = 422, description = "Validation error", body = crate::common::errors::ErrorResponse)
    ),
    tag = "auth"
)]
pub async fn google_callback(
    auth_controller: web::Data<AuthController>,
    callback_dto: web::Json<GoogleAuthCallbackDto>,
) -> Result<HttpResponse> {
    callback_dto.validate_dto().map_err(actix_web::error::ErrorBadRequest)?;

    match auth_controller.google_oauth_service.handle_callback_with_role(&callback_dto.code, callback_dto.role.clone()).await {
        Ok(response) => {
            LoggerService::log_auth_event("google_oauth", Some(&response.user.email), true);

            let access_cookie = format!(
                "access_token={}; HttpOnly; Secure; SameSite=None; Max-Age={}; Path=/",
                response.access_token,
                response.expires_in
            );

            let refresh_cookie = format!(
                "refresh_token={}; HttpOnly; Secure; SameSite=None; Max-Age=604800; Path=/",
                response.refresh_token
            );

            Ok(HttpResponse::Ok()
                .insert_header((SET_COOKIE, HeaderValue::from_str(&access_cookie).unwrap()))
                .insert_header((SET_COOKIE, HeaderValue::from_str(&refresh_cookie).unwrap()))
                .json(response))
        },
        Err(e) => {
            LoggerService::log_auth_event("google_oauth", None, false);
            let error_msg = e.to_string();
            LoggerService::log_error(&format!("Google OAuth failed: {error_msg}"), Some("AuthController"));
            Ok(e.error_response())
        }
    }
}

impl AuthController {
    pub fn new(common_module: &CommonModule) -> Self {
        Self {
            auth_service: AuthService::new(common_module),
            google_oauth_service: AuthGoogleService::new(common_module),
        }
    }

    pub async fn login(
        auth_controller: web::Data<AuthController>,
        login_dto: web::Json<LoginDto>,
    ) -> Result<HttpResponse> {
        login(auth_controller, login_dto).await
    }

    pub async fn register(
        auth_controller: web::Data<AuthController>,
        register_dto: web::Json<RegisterDto>,
    ) -> Result<HttpResponse> {
        register(auth_controller, register_dto).await
    }

    pub async fn refresh_token(
        auth_controller: web::Data<AuthController>,
        refresh_dto: web::Json<RefreshTokenDto>,
        req: actix_web::HttpRequest,
    ) -> Result<HttpResponse> {
        refresh_token(auth_controller, refresh_dto, req).await
    }

    pub async fn logout(
        auth_controller: web::Data<AuthController>,
        req: actix_web::HttpRequest,
    ) -> Result<HttpResponse> {
        logout(auth_controller, req).await
    }

    pub async fn send_otp(
        auth_controller: web::Data<AuthController>,
        send_otp_dto: web::Json<SendOtpDto>,
    ) -> Result<HttpResponse> {
        send_otp(auth_controller, send_otp_dto).await
    }

    pub async fn verify_otp(
        auth_controller: web::Data<AuthController>,
        verify_otp_dto: web::Json<VerifyOtpDto>,
    ) -> Result<HttpResponse> {
        verify_otp(auth_controller, verify_otp_dto).await
    }

    pub async fn phone_login(
        auth_controller: web::Data<AuthController>,
        phone_login_dto: web::Json<PhoneLoginDto>,
    ) -> Result<HttpResponse> {
        phone_login(auth_controller, phone_login_dto).await
    }

    pub async fn google_auth_url(
        auth_controller: web::Data<AuthController>,
    ) -> Result<HttpResponse> {
        google_auth_url(auth_controller).await
    }

    pub async fn google_callback(
        auth_controller: web::Data<AuthController>,
        callback_dto: web::Json<GoogleAuthCallbackDto>,
    ) -> Result<HttpResponse> {
        google_callback(auth_controller, callback_dto).await
    }

    pub async fn google_callback_redirect(
        auth_controller: web::Data<AuthController>,
        query: web::Query<GoogleAuthCallbackDto>,
    ) -> Result<HttpResponse> {
        google_callback_redirect(auth_controller, query).await
    }

    #[allow(dead_code)]
    fn create_auth_response_with_cookies(response: AuthResponseDto) -> HttpResponse {
        let access_cookie = format!(
            "access_token={}; HttpOnly; Secure; SameSite=None; Max-Age={}; Path=/",
            response.access_token,
            response.expires_in
        );

        let refresh_cookie = format!(
            "refresh_token={}; HttpOnly; Secure; SameSite=None; Max-Age=604800; Path=/",
            response.refresh_token
        );

        HttpResponse::Ok()
            .insert_header((SET_COOKIE, HeaderValue::from_str(&access_cookie).unwrap()))
            .insert_header((SET_COOKIE, HeaderValue::from_str(&refresh_cookie).unwrap()))
            .json(response)
    }
}
