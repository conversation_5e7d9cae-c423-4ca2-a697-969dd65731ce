import { Project } from '@/lib/types/project';

export function formatCurrency(amount: number, currency: 'IRR' | 'USD' = 'IRR'): string {
  if (currency === 'IRR') {
    // Convert to <PERSON><PERSON> (divide by 10) and format
    const tomanAmount = amount / 10;
    return new Intl.NumberFormat('fa-IR').format(tomanAmount) + ' تومان';
  } else {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }
}

export function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('fa-IR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(date);
}

export function formatRelativeDate(dateString: string): string {
  const date = new Date(dateString);
  const now = new Date();
  const diffInMs = now.getTime() - date.getTime();
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
  const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));

  if (diffInDays > 0) {
    return `${diffInDays} روز پیش`;
  } else if (diffInHours > 0) {
    return `${diffInHours} ساعت پیش`;
  } else if (diffInMinutes > 0) {
    return `${diffInMinutes} دقیقه پیش`;
  } else {
    return 'همین الان';
  }
}

export function getStatusLabel(status: Project['status']): string {
  const statusLabels = {
    draft: 'پیش‌نویس',
    published: 'منتشر شده',
    in_progress: 'در حال انجام',
    completed: 'تکمیل شده',
    cancelled: 'لغو شده'
  };
  return statusLabels[status];
}

export function getStatusColor(status: Project['status']): 'default' | 'secondary' | 'destructive' | 'outline' {
  const statusColors = {
    draft: 'outline' as const,
    published: 'default' as const,
    in_progress: 'secondary' as const,
    completed: 'default' as const,
    cancelled: 'destructive' as const
  };
  return statusColors[status];
}

export function getComplexityLabel(complexity: Project['complexity']): string {
  const complexityLabels = {
    beginner: 'مبتدی',
    intermediate: 'متوسط',
    expert: 'پیشرفته'
  };
  return complexityLabels[complexity];
}

export function getComplexityColor(complexity: Project['complexity']): string {
  const complexityColors = {
    beginner: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    intermediate: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    expert: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
  };
  return complexityColors[complexity];
}

export function calculateProjectProgress(project: Project): number {
  if (!project.milestones || project.milestones.length === 0) {
    return project.status === 'completed' ? 100 : 0;
  }

  const completedMilestones = project.milestones.filter(m => m.status === 'completed').length;
  return Math.round((completedMilestones / project.milestones.length) * 100);
}

export function isProjectExpired(deadline: string): boolean {
  return new Date(deadline) < new Date();
}

export function getDaysUntilDeadline(deadline: string): number {
  const deadlineDate = new Date(deadline);
  const now = new Date();
  const diffInMs = deadlineDate.getTime() - now.getTime();
  return Math.ceil(diffInMs / (1000 * 60 * 60 * 24));
}

export function filterProjects(projects: Project[], filters: {
  category?: string;
  skills?: string[];
  budgetRange?: { min: number; max: number };
  complexity?: string;
  status?: string;
  search?: string;
}): Project[] {
  return projects.filter(project => {
    // Category filter
    if (filters.category && project.category !== filters.category) {
      return false;
    }

    // Skills filter
    if (filters.skills && filters.skills.length > 0) {
      const hasRequiredSkills = filters.skills.some(skill => 
        project.skills.includes(skill)
      );
      if (!hasRequiredSkills) {
        return false;
      }
    }

    // Budget filter
    if (filters.budgetRange) {
      const projectMaxBudget = project.budget.max;
      if (projectMaxBudget < filters.budgetRange.min || projectMaxBudget > filters.budgetRange.max) {
        return false;
      }
    }

    // Complexity filter
    if (filters.complexity && project.complexity !== filters.complexity) {
      return false;
    }

    // Status filter
    if (filters.status && project.status !== filters.status) {
      return false;
    }

    // Search filter
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      const searchableText = `${project.title} ${project.description} ${project.skills.join(' ')}`.toLowerCase();
      if (!searchableText.includes(searchTerm)) {
        return false;
      }
    }

    return true;
  });
}

export function sortProjects(projects: Project[], sortBy: 'newest' | 'oldest' | 'budget_high' | 'budget_low' | 'deadline'): Project[] {
  return [...projects].sort((a, b) => {
    switch (sortBy) {
      case 'newest':
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      case 'oldest':
        return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
      case 'budget_high':
        return b.budget.max - a.budget.max;
      case 'budget_low':
        return a.budget.max - b.budget.max;
      case 'deadline':
        return new Date(a.deadline).getTime() - new Date(b.deadline).getTime();
      default:
        return 0;
    }
  });
}
