// sitemap.config.ts
import type { Config } from "./src/scripts/sitemap-generator.js"; // Adjust path if needed

const config: Config = {
  baseUrl: "https://www.dolfak.com", // Replace with your actual domain
  supportedLanguages: ["en", "fa"],
  defaultLanguage: "en",
  pagesDir: "src/pages", // Use 'pages' if not using src/
  appDir: "src/app", // Use 'app' if not using src/
  outputDir: "public",
  excludePaths: [
    "/api/**", // Exclude all API routes
    "/_*", // Exclude Next.js internal files
    "/\\[.*\\]", // Exclude dynamic route files by default
    "/\\(.*\\)/*", // Exclude route groups
  ],
  priority: {
    "/": 1.0,
  },
  changefreq: {
    "/": "daily",
  },
  // Optional: Add custom transformations
  // transform: async (urlPath: string, lang: string, file: string): Promise<string | null | SitemapEntry> => {
  //   // Example: Fetch dynamic blog post slugs
  //   if (urlPath.startsWith('/blog/')) {
  //      // const posts = await fetchPostsForLang(lang);
  //      // return posts.map(post => ({ loc: `/blog/${post.slug}`, lastmod: post.updatedAt }));
  //      return null; // Return null if not handled here, or an array of SitemapEntry for multiple URLs from one file
  //   }
  //   return { loc: urlPath }; // Return basic object if no change
  // },
  logLevel: "info", // 'silent', 'info', 'debug'
  gzip: false,
  sitemapIndex: true, // Generate sitemap index if multiple language sitemaps are created
};

export default config;
