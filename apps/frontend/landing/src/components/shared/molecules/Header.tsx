'use client';

import { Components } from '@/components/registry';
import { useScrollPosition } from '@/hooks/ui/useScrollPosition';
import React, { useEffect, useState } from 'react';

interface HeaderProps {
  className?: string;
}

const Header: React.FC<HeaderProps> = ({ className }) => {
  const scrollY = useScrollPosition();
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    setIsScrolled(scrollY > 10);
  }, [scrollY]);

  return (
    <header
      className={`bg-background/80 backdrop-blur-[clamp(4px,1vw,8px)] sticky top-0 z-50 border-b border-primary/20 h-12 md:h-14 transition-all duration-300 will-change-transform ${className || ''}`}
      style={
        {
          '--tw-shadow': isScrolled
            ? '0 4px 6px -1px oklch(0.7 0.15 140 / 0.2), 0 2px 4px -2px oklch(0.7 0.15 140 / 0.1)'
            : '0 1px 2px 0 oklch(0.7 0.15 140 / 0.05)',
          '--tw-shadow-colored': isScrolled
            ? '0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color)'
            : '0 1px 2px 0 var(--tw-shadow-color)',
        } as React.CSSProperties
      }
    >
      <div className="container mx-auto px-4 h-full">
        <Components.HeaderNavigation />
      </div>
    </header>
  );
};

export default Header;
