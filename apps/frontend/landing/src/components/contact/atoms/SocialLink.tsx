import { cn } from '@/lib/core/utils';
import React from 'react';

interface SocialLinkProps {
  href: string;
  icon: React.ReactNode;
  name: string;
  className?: string;
}

const SocialLink: React.FC<SocialLinkProps> = ({
  href,
  icon,
  name,
  className,
}) => {
  return (
    <a
      href={href}
      target="_blank"
      rel="noopener noreferrer"
      className={cn(
        'flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 text-primary hover:bg-primary hover:text-primary-foreground transition-all duration-300 transform hover:scale-110',
        className,
      )}
      aria-label={name}
      title={name}
    >
      {icon}
    </a>
  );
};

export default SocialLink;
