import { WizardStepProps } from '@/components/shared/wizard/wizard-types';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { ProjectTypeData } from '@/lib/validations/wizard';
import { Building, Globe, Lightbulb } from 'lucide-react';

const projectTypes = [
  { id: 'webapp', label: 'وب اپلیکیشن', icon: Globe },
  { id: 'mobile', label: 'اپلیکیشن موبایل', icon: Building },
  { id: 'startup', label: 'استارتاپ', icon: Lightbulb },
];

export const ProjectTypeStep = ({
  data,
  onDataChange,
  context,
}: WizardStepProps<ProjectTypeData>) => {
  const handleProjectTypeChange = (value: string) => {
    onDataChange({ projectType: value });
  };

  const handleDescriptionChange = (value: string) => {
    onDataChange({ description: value });
  };

  const validationErrors = context.validationErrors;
  console.log('ProjectTypeStep validationErrors:', validationErrors);
  console.log('ProjectTypeStep context:', context);

  const getFieldError = (field: string) => {
    const error = validationErrors.find((error) => {
      if (field === 'projectType') {
        return error.includes('نوع پروژه') || error.includes('انتخاب');
      }
      if (field === 'description') {
        return error.includes('توضیحات') || error.includes('کاراکتر');
      }
      return false;
    });
    return error;
  };

  const projectTypeError = getFieldError('projectType');
  const descriptionError = getFieldError('description');

  return (
    <Card className="border-0 shadow-none bg-transparent">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Lightbulb className="w-4 h-4" />
          نوع پروژه شما
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label className="text-sm">چه نوع پروژه‌ای در ذهن دارید؟ *</Label>
          <RadioGroup
            value={data.projectType || ''}
            onValueChange={handleProjectTypeChange}
            className={`mt-2 ${projectTypeError ? 'border-destructive' : ''}`}
          >
            {projectTypes.map((type) => {
              const Icon = type.icon;
              return (
                <div
                  key={type.id}
                  className="flex items-center space-x-2 p-2 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <RadioGroupItem value={type.id} id={type.id} />
                  <Icon className="w-4 h-4 text-primary mx-2" />
                  <Label
                    htmlFor={type.id}
                    className="cursor-pointer flex-1 text-sm"
                  >
                    {type.label}
                  </Label>
                </div>
              );
            })}
          </RadioGroup>
          {projectTypeError && (
            <p className="text-xs text-destructive mt-1">{projectTypeError}</p>
          )}
        </div>

        <div>
          <Label htmlFor="description" className="text-sm">
            توضیحات پروژه *
          </Label>
          <Textarea
            id="description"
            value={data.description || ''}
            onChange={(e) => handleDescriptionChange(e.target.value)}
            placeholder="ایده یا نیازتان را به طور مختصر توضیح دهید... (حداقل ۲۰ کاراکتر)"
            rows={3}
            className={`mt-1 text-sm ${descriptionError ? 'border-destructive' : ''}`}
            aria-invalid={!!descriptionError}
            aria-describedby={
              descriptionError ? 'description-error' : undefined
            }
          />
          {descriptionError && (
            <p id="description-error" className="text-xs text-destructive mt-1">
              {descriptionError}
            </p>
          )}
          <div className="text-xs text-muted-foreground mt-1">
            {data.description?.length || 0} / 1000 کاراکتر
            {data.description && data.description.length < 20 && (
              <span className="text-destructive ml-2">
                (حداقل {20 - data.description.length} کاراکتر دیگر نیاز است)
              </span>
            )}
          </div>
        </div>

        {validationErrors.length > 0 &&
          !projectTypeError &&
          !descriptionError && (
            <div className="text-xs text-destructive space-y-1">
              {validationErrors
                .filter(
                  (error) =>
                    !error.includes('نوع پروژه') &&
                    !error.includes('انتخاب') &&
                    !error.includes('توضیحات') &&
                    !error.includes('کاراکتر'),
                )
                .map((error, index) => (
                  <p key={index}>{error}</p>
                ))}
            </div>
          )}
      </CardContent>
    </Card>
  );
};
