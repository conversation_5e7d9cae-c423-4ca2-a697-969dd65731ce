import AppProvider from '@/providers/app-provider';
import type { Metadata, Viewport } from 'next';
import { Geist_Mono } from 'next/font/google';
import { headers } from 'next/headers';
import Script from 'next/script';
import './global.css';

// YekanBakhFaNum font variable for CSS
const yekanBakhFaNum = {
  variable: '--font-yekanBakhFaNum',
};

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
  display: 'swap',
  preload: true,
  adjustFontFallback: true,
  weight: ['400', '500'],
});

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#f8f9fa' },
    { media: '(prefers-color-scheme: dark)', color: '#1a1a1a' },
  ],
  colorScheme: 'light dark',
};

export const metadata: Metadata = {
  title: {
    default: 'سوپر اپلیکیشن دلفک | برنامه نویسی | نرم افزار',
    template: '%s | سوپر اپلیکیشن دلفک | برنامه نویسی | نرم افزار',
  },
  description:
    'سوپر اپلیکیشن دلفک، راهکارهای هوشمند برنامه نویسی حرفه‌ای برای توسعه دهندگان و شرکت‌های نرم‌افزاری. تجربه توسعه نرم‌افزار را با ابزارهای کارآمد دلفک متحول کنید.',
  metadataBase: new URL('https://dolfak.com'),
  applicationName: 'دلفک',
  authors: [{ name: 'تیم توسعه دلفک' }],
  generator: 'Next.js',
  keywords: [
    'دلفک',
    'برنامه نویسی',
    'نرم افزار',
    'توسعه وب',
    'اپلیکیشن',
    'هوش مصنوعی',
    'ابزار برنامه نویسی',
  ],
  creator: 'دلفک',
  publisher: 'شرکت دلفک',
  category: 'تکنولوژی',
  formatDetection: {
    telephone: true,
    date: true,
    address: true,
    email: true,
    url: true,
  },
  openGraph: {
    title: 'سوپر اپلیکیشن دلفک | برنامه نویسی | نرم افزار',
    description:
      'سوپر اپلیکیشن دلفک، راهکارهای هوشمند برنامه نویسی حرفه‌ای برای توسعه دهندگان و شرکت‌های نرم‌افزاری. تجربه توسعه نرم‌افزار را با ابزارهای کارآمد دلفک متحول کنید.',
    url: 'https://dolfak.com',
    siteName: 'سوپر اپلیکیشن دلفک',
    images: [
      {
        url: '/dolfak.webp',
        width: 1200,
        height: 630,
        alt: 'لوگوی دلفک - سوپر اپلیکیشن برنامه نویسی و نرم افزار',
      },
    ],
    locale: 'fa_IR',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'سوپر اپلیکیشن دلفک | برنامه نویسی | نرم افزار',
    description:
      'سوپر اپلیکیشن دلفک، راهکارهای هوشمند برنامه نویسی حرفه‌ای برای توسعه دهندگان و شرکت‌های نرم‌افزاری',
    images: [
      {
        url: '/dolfak.webp',
        alt: 'لوگوی دلفک - سوپر اپلیکیشن برنامه نویسی و نرم افزار',
      },
    ],
    site: '@dolfak',
    creator: '@dolfak',
  },
  alternates: {
    canonical: 'https://dolfak.com',
    languages: {
      'fa-IR': 'https://dolfak.com',
    },
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'گوگل سرچ کنسول وریفیکیشن کد را اینجا قرار دهید',
  },
};

async function getCanonicalUrl(): Promise<string> {
  const headersList = await headers();
  const host = headersList.get('host') || 'dolfak.com';
  const proto = headersList.get('x-forwarded-proto') || 'https';
  const path = headersList.get('x-next-url') || '/';

  return `${proto}://${host}${path}`;
}

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const canonicalUrl = await getCanonicalUrl();

  return (
    <html lang="fa" dir="rtl" suppressHydrationWarning>
      <head>
        <meta name="enamad" content="64185362" />

        <link rel="canonical" href={canonicalUrl} />

        <Script
          id="schema-organization"
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'Organization',
              name: 'دلفک',
              url: 'https://dolfak.com',
              logo: 'https://dolfak.com/dolfak.webp',
              description:
                'سوپر اپلیکیشن دلفک، راهکارهای هوشمند برنامه نویسی حرفه‌ای برای توسعه دهندگان و شرکت‌های نرم‌افزاری.',
              sameAs: [
                'https://twitter.com/dolfak',
                'https://www.instagram.com/dolfak',
                'https://www.linkedin.com/company/dolfak',
              ],
            }),
          }}
        />

        <meta property="og:site_name" content="دلفک" />
        <meta property="og:image:width" content="1200" />
        <meta property="og:image:height" content="630" />
        <meta property="og:locale" content="fa_IR" />
      </head>
      <body
        className={`${yekanBakhFaNum.variable} ${geistMono.variable} font-yekanBakhFaNum antialiased`}
        suppressHydrationWarning
      >
        <AppProvider>
          <div className="min-h-screen">{children}</div>
        </AppProvider>
      </body>
    </html>
  );
}
