use actix_web::web::Bytes;
use anyhow::Result;
use std::path::Path;
use tokio::fs;

#[allow(dead_code)]
pub struct LocalStorage {
    upload_dir: String,
}

impl Default for LocalStorage {
    fn default() -> Self {
        Self::new()
    }
}

impl LocalStorage {
    pub fn new() -> Self {
        Self {
            upload_dir: "uploads".to_string(),
        }
    }

    #[allow(dead_code)]
    pub async fn upload(&self, filename: &str, file_data: Bytes) -> Result<String> {
        let upload_path = Path::new(&self.upload_dir);

        if !upload_path.exists() {
            fs::create_dir_all(&upload_path).await?;
        }

        let file_path = upload_path.join(filename);
        fs::write(&file_path, &file_data).await?;

        Ok(file_path.to_string_lossy().to_string())
    }

    #[allow(dead_code)]
    pub async fn delete(&self, filename: &str) -> Result<()> {
        let file_path = Path::new(&self.upload_dir).join(filename);

        if file_path.exists() {
            fs::remove_file(file_path).await?;
        }

        Ok(())
    }
}
