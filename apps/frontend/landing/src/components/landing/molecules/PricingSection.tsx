import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Building2, Check, Info, Rocket, TrendingUp } from 'lucide-react';
import React from 'react';
import { PricingButton } from './Pricing/PricingButton';

interface PricingSectionProps {
  className?: string;
}

const PricingSection: React.FC<PricingSectionProps> = ({ className }) => {
  const plans = [
    {
      name: 'استارتاپ‌ها و کسب‌وکارهای کوچک',
      price: '۲۵,۰۰۰,۰۰۰',
      period: '/ماهانه',
      features: [
        '۲۰ ساعت کار توسعه در ماه',
        'یک توسعه‌دهنده اختصاصی',
        'پشتیبانی پایه',
        'گزارش پیشرفت ماهانه',
      ],
      popular: false,
      icon: Rocket,
      color: 'from-primary/20 to-accent/20',
      tooltip: 'مناسب برای استارتاپ‌های تازه تأسیس',
    },
    {
      name: 'کسب‌وکارهای در حال رشد',
      price: '۵۰,۰۰۰,۰۰۰',
      period: '/ماهانه',
      features: [
        '۴۰ ساعت کار توسعه در ماه',
        'دو توسعه‌دهنده اختصاصی',
        'پشتیبانی اولویت‌دار',
        'جلسات استراتژی فصلی',
      ],
      popular: true,
      icon: TrendingUp,
      color: 'from-accent/20! to-primary/30!',
      tooltip: 'بهترین انتخاب برای کسب‌وکارهای رو به رشد',
    },
    {
      name: 'سازمان‌های بزرگ',
      price: '۱۰۰,۰۰۰,۰۰۰',
      period: '/ماهانه',
      features: [
        '۸۰ ساعت کار توسعه در ماه',
        'تیم کامل توسعه‌دهندگان',
        'پشتیبانی اولویت‌دار ۲۴/۷',
        'دسترسی به فناوری‌ها و قابلیت‌های جدید',
      ],
      popular: false,
      icon: Building2,
      color: 'from-primary/30 to-accent/20',
      tooltip: 'راهکار کامل برای سازمان‌های بزرگ',
    },
  ];

  return (
    <section
      id="pricing"
      className={`py-12 sm:py-16 lg:py-20 xl:py-24 bg-gradient-to-br from-background via-card to-secondary relative overflow-hidden ${className || ''}`}
    >
      <div className="absolute -top-40 -left-40 w-[clamp(12rem,25vw,20rem)] h-[clamp(12rem,25vw,20rem)] rounded-full bg-primary/10 mix-blend-screen filter blur-3xl opacity-70 cyber-glow"></div>
      <div className="absolute -bottom-20 right-20 w-[clamp(10rem,20vw,16rem)] h-[clamp(10rem,20vw,16rem)] rounded-full bg-accent/10 mix-blend-screen filter blur-3xl opacity-60 cyber-glow"></div>

      <div className="container relative z-10 mx-auto px-4">
        <div className="text-center mb-8 sm:mb-12 lg:mb-16">
          <Badge
            variant="outline"
            className="mb-4 sm:mb-6 px-4 sm:px-6 py-2 sm:py-3 bg-card/80 text-primary rounded-lg text-sm sm:text-base font-mono border border-primary/20 shadow-lg cyber-glow"
          >
            <span className="text-accent">const</span> pricing = await
            fetchPlans()
          </Badge>
          <h2 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold mb-4 sm:mb-6 font-mono">
            <span className="text-primary">{'<'}</span>
            <span className="text-foreground mx-2 holographic-text">
              قیمت‌گذاری دلفک
            </span>
            <span className="text-primary">{'/>'}</span>
          </h2>
          <div className="max-w-2xl mx-auto text-sm sm:text-base lg:text-lg font-mono code-block-light">
            <div className="text-accent mb-2">
              // ایده‌های خود را در چند دقیقه با دلفک به واقعیت تبدیل کنید
            </div>
          </div>
        </div>

        <TooltipProvider>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
            {plans.map((plan, index) => (
              <Tooltip key={index}>
                <TooltipTrigger asChild>
                  <Card
                    className={`relative bg-card/80 backdrop-blur-sm border-primary/20 shadow-lg transition-all duration-300 hover:scale-105 terminal-window group ${
                      plan.popular
                        ? 'border-primary/40 ring-2 ring-primary/20 cyber-glow'
                        : 'hover:border-primary/30'
                    }`}
                  >
                    {plan.popular && (
                      <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-primary to-accent text-primary-foreground px-3 py-1 rounded-full text-xs font-mono shadow-lg">
                        محبوب
                      </Badge>
                    )}

                    <CardHeader className="text-center">
                      <div className="flex justify-center items-center gap-2 mb-3">
                        <plan.icon className="w-12 h-12 sm:w-16 sm:h-16 text-primary group-hover:animate-pulse" />
                        <Info className="w-4 h-4 text-muted-foreground" />
                      </div>
                      <CardTitle
                        className="text-sm sm:text-base lg:text-lg font-mono text-primary leading-relaxed glitch"
                        data-text={plan.name}
                      >
                        {plan.name}
                      </CardTitle>
                      <div className="mt-4">
                        <span className="text-xl sm:text-2xl lg:text-3xl font-bold text-foreground holographic-text">
                          {plan.price}
                        </span>
                        <span className="text-sm sm:text-base text-muted-foreground">
                          {' '}
                          تومان{plan.period}
                        </span>
                      </div>
                    </CardHeader>

                    <CardContent>
                      <Separator className="my-4 bg-primary/20" />
                      <ul className="space-y-3 mb-6">
                        {plan.features.map((feature, featureIndex) => (
                          <li
                            key={featureIndex}
                            className="flex items-center gap-3 font-mono text-sm sm:text-base"
                          >
                            <div className="flex-shrink-0">
                              <Check className="w-5 h-5 sm:w-6 sm:h-6 text-primary bg-primary/10 rounded-full p-1 sm:p-1.5" />
                            </div>
                            <span className="text-foreground leading-relaxed">
                              {feature}
                            </span>
                          </li>
                        ))}
                      </ul>

                      <PricingButton popular={plan?.popular} />
                    </CardContent>
                  </Card>
                </TooltipTrigger>
                <TooltipContent
                  side="top"
                  className="font-mono bg-card border border-primary/20 text-black! dark:text-white!"
                >
                  <p>{plan.tooltip}</p>
                </TooltipContent>
              </Tooltip>
            ))}
          </div>
        </TooltipProvider>
      </div>
    </section>
  );
};
export default PricingSection;
