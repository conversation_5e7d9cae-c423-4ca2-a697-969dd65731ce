use serde::{Serialize, Deserialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
#[allow(dead_code)]
pub enum SmsTemplate {
    OtpVerification,
    Custom(String),
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SmsRequest {
    pub receptor: String,
    pub message: String,
    pub sender: Option<String>,
    pub template: Option<String>,
    pub token: Option<String>,
}

#[derive(Debu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct SmsResponse {
    pub return_status: SmsReturnStatus,
    pub entries: Vec<SmsEntry>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SmsReturnStatus {
    pub status: u32,
    pub message: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SmsEntry {
    pub messageid: u64,
    pub message: String,
    pub status: u32,
    pub statustext: String,
    pub sender: String,
    pub receptor: String,
    pub date: u64,
    pub cost: u32,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct OtpSmsRequest {
    pub phone: String,
    pub code: String,
}

#[derive(Debug, thiserror::Error)]
pub enum SmsError {
    #[error("HTTP request failed: {0}")]
    Http(String),
    #[error("API error: status {status}, message: {message}")]
    Api { status: u32, message: String },
    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),
    #[error("Config error: {0}")]
    #[allow(dead_code)]
    Config(String),
    #[error("Persian conversion error: {0}")]
    #[allow(dead_code)]
    PersianConversion(String),
}

pub type SmsResult<T> = Result<T, SmsError>;
