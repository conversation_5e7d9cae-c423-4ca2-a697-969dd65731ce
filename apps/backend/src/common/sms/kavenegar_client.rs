use reqwest::Client;
use serde_json::Value;
use std::collections::HashMap;
use crate::common::sms::types::*;
use crate::common::logger::LoggerService;

pub struct KavenegarClient {
    client: Client,
    api_key: String,
    base_url: String,
}

impl KavenegarClient {
    pub fn new(api_key: String) -> Self {
        Self {
            client: Client::new(),
            api_key,
            base_url: "https://api.kavenegar.com/v1".to_string(),
        }
    }

    #[allow(dead_code)]
    pub async fn send_simple_sms(&self, request: &SmsRequest) -> SmsResult<SmsResponse> {
        let url = format!("{}/{}/sms/send.json", self.base_url, self.api_key);

        let mut params = HashMap::new();
        params.insert("receptor", request.receptor.clone());
        params.insert("message", request.message.clone());

        if let Some(sender) = &request.sender {
            params.insert("sender", sender.clone());
        }

        LoggerService::info(&format!("Sending SMS to: {}", request.receptor));

        let response = self.client
            .post(&url)
            .form(&params)
            .send()
            .await
            .map_err(|e| SmsError::Http(e.to_string()))?;

        let status = response.status();
        let text = response.text().await
            .map_err(|e| SmsError::Http(e.to_string()))?;

        if !status.is_success() {
            LoggerService::log_error(&format!("SMS API error: status {status}, body: {text}"), Some("KavenegarClient"));
            return Err(SmsError::Http(format!("HTTP {status} - {text}")));
        }

        let json: Value = serde_json::from_str(&text)?;

        let return_status = SmsReturnStatus {
            status: json["return"]["status"].as_u64().unwrap_or(0) as u32,
            message: json["return"]["message"].as_str().unwrap_or("").to_string(),
        };

        if return_status.status != 200 {
            LoggerService::log_error(&format!("SMS API returned error: {}", return_status.message), Some("KavenegarClient"));
            return Err(SmsError::Api {
                status: return_status.status,
                message: return_status.message.clone(),
            });
        }

        let mut entries = Vec::new();
        if let Some(entries_array) = json["entries"].as_array() {
            for entry in entries_array {
                entries.push(SmsEntry {
                    messageid: entry["messageid"].as_u64().unwrap_or(0),
                    message: entry["message"].as_str().unwrap_or("").to_string(),
                    status: entry["status"].as_u64().unwrap_or(0) as u32,
                    statustext: entry["statustext"].as_str().unwrap_or("").to_string(),
                    sender: entry["sender"].as_str().unwrap_or("").to_string(),
                    receptor: entry["receptor"].as_str().unwrap_or("").to_string(),
                    date: entry["date"].as_u64().unwrap_or(0),
                    cost: entry["cost"].as_u64().unwrap_or(0) as u32,
                });
            }
        }

        LoggerService::info(&format!("SMS sent successfully to {}", request.receptor));

        Ok(SmsResponse {
            return_status,
            entries,
        })
    }

    pub async fn send_lookup_sms(&self, receptor: &str, template: &str, token: &str) -> SmsResult<SmsResponse> {
        let url = format!("{}/{}/verify/lookup.json", self.base_url, self.api_key);

        let mut params = HashMap::new();
        params.insert("receptor", receptor.to_string());
        params.insert("template", template.to_string());
        params.insert("token", token.to_string());

        LoggerService::info(&format!("Sending lookup SMS to: {receptor} with template: {template}"));

        let response = self.client
            .post(&url)
            .form(&params)
            .send()
            .await
            .map_err(|e| SmsError::Http(e.to_string()))?;

        let status = response.status();
        let text = response.text().await
            .map_err(|e| SmsError::Http(e.to_string()))?;

        if !status.is_success() {
            LoggerService::log_error(&format!("SMS Lookup API error: status {status}, body: {text}"), Some("KavenegarClient"));
            return Err(SmsError::Http(format!("HTTP {status} - {text}")));
        }

        let json: Value = serde_json::from_str(&text)?;

        let return_status = SmsReturnStatus {
            status: json["return"]["status"].as_u64().unwrap_or(0) as u32,
            message: json["return"]["message"].as_str().unwrap_or("").to_string(),
        };

        if return_status.status != 200 {
            LoggerService::log_error(&format!("SMS Lookup API returned error: {}", return_status.message), Some("KavenegarClient"));
            return Err(SmsError::Api {
                status: return_status.status,
                message: return_status.message.clone(),
            });
        }

        let mut entries = Vec::new();
        if let Some(entries_array) = json["entries"].as_array() {
            for entry in entries_array {
                entries.push(SmsEntry {
                    messageid: entry["messageid"].as_u64().unwrap_or(0),
                    message: entry["message"].as_str().unwrap_or("").to_string(),
                    status: entry["status"].as_u64().unwrap_or(0) as u32,
                    statustext: entry["statustext"].as_str().unwrap_or("").to_string(),
                    sender: entry["sender"].as_str().unwrap_or("").to_string(),
                    receptor: entry["receptor"].as_str().unwrap_or("").to_string(),
                    date: entry["date"].as_u64().unwrap_or(0),
                    cost: entry["cost"].as_u64().unwrap_or(0) as u32,
                });
            }
        }

        LoggerService::info(&format!("Lookup SMS sent successfully to {receptor}"));

        Ok(SmsResponse {
            return_status,
            entries,
        })
    }
}
