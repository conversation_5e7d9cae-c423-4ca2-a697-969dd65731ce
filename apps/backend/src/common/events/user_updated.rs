use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct UserUpdatedEvent {
    pub user_id: Uuid,
    pub email: Option<String>,
    pub username: Option<String>,
}

#[allow(dead_code)]
impl UserUpdatedEvent {
    pub fn new(user_id: Uuid, email: Option<String>, username: Option<String>) -> Self {
        Self {
            user_id,
            email,
            username,
        }
    }
}
