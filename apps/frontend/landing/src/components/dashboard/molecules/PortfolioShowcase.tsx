'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { PortfolioItem } from '@/lib/types/user';
import { formatRelativeDate } from '@/lib/utils/project';
import { ExternalLink, Github, Plus, Star } from 'lucide-react';
import Image from 'next/image';

interface PortfolioShowcaseProps {
  portfolioItems: PortfolioItem[];
  isOwner?: boolean;
  onAddItem?: () => void;
  onEditItem?: (itemId: string) => void;
  onViewItem?: (itemId: string) => void;
}

export function PortfolioShowcase({
  portfolioItems,
  isOwner = false,
  onAddItem,
  onEditItem,
  onViewItem
}: PortfolioShowcaseProps) {
  if (portfolioItems.length === 0 && !isOwner) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center p-12 text-center">
          <div className="text-muted-foreground mb-2">
            هنوز نمونه کاری اضافه نشده است
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-semibold">نمونه کارها</h3>
        {isOwner && (
          <Button onClick={onAddItem}>
            <Plus className="h-4 w-4 ml-2" />
            افزودن نمونه کار
          </Button>
        )}
      </div>

      {portfolioItems.length === 0 && isOwner ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-12 text-center">
            <div className="text-muted-foreground mb-4">
              هنوز نمونه کاری اضافه نکرده‌اید
            </div>
            <Button onClick={onAddItem}>
              <Plus className="h-4 w-4 ml-2" />
              اولین نمونه کار خود را اضافه کنید
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {portfolioItems.map((item) => (
            <PortfolioItemCard
              key={item.id}
              item={item}
              isOwner={isOwner}
              onEdit={() => onEditItem?.(item.id)}
              onView={() => onViewItem?.(item.id)}
            />
          ))}
        </div>
      )}
    </div>
  );
}

interface PortfolioItemCardProps {
  item: PortfolioItem;
  isOwner: boolean;
  onEdit?: () => void;
  onView?: () => void;
}

function PortfolioItemCard({ item, isOwner, onEdit, onView }: PortfolioItemCardProps) {
  return (
    <Card className="h-full flex flex-col hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        {item.images.length > 0 && (
          <div className="relative aspect-video mb-3 rounded-lg overflow-hidden bg-muted">
            <Image
              src={item.images[0]}
              alt={item.title}
              fill
              className="object-cover"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = 'https://via.placeholder.com/600x400?text=No+Image';
              }}
            />
          </div>
        )}
        
        <div className="space-y-2">
          <CardTitle className="text-lg leading-tight line-clamp-2">
            {item.title}
          </CardTitle>
          
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">
              {item.category}
            </Badge>
            <span className="text-xs text-muted-foreground">
              {formatRelativeDate(item.completedAt)}
            </span>
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex-1 pb-3">
        <div className="space-y-3">
          <p className="text-sm text-muted-foreground line-clamp-3">
            {item.description}
          </p>

          <div className="flex flex-wrap gap-1">
            {item.skills.slice(0, 4).map((skill, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {skill}
              </Badge>
            ))}
            {item.skills.length > 4 && (
              <Badge variant="outline" className="text-xs">
                +{item.skills.length - 4}
              </Badge>
            )}
          </div>

          {item.client && (
            <div className="text-sm">
              <span className="text-muted-foreground">کارفرما: </span>
              <span className="font-medium">{item.client}</span>
            </div>
          )}

          {item.testimonial && (
            <div className="bg-muted/50 p-3 rounded-lg">
              <div className="flex items-center gap-1 mb-1">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`h-3 w-3 ${
                      i < item.testimonial!.rating
                        ? 'text-yellow-500 fill-current'
                        : 'text-gray-300'
                    }`}
                  />
                ))}
                <span className="text-xs text-muted-foreground mr-1">
                  {item.testimonial.clientName}
                </span>
              </div>
              <p className="text-xs text-muted-foreground line-clamp-2">
                "{item.testimonial.text}"
              </p>
            </div>
          )}
        </div>
      </CardContent>

      <CardFooter className="pt-3">
        <div className="flex gap-2 w-full">
          <Button
            variant="outline"
            size="sm"
            onClick={onView}
            className="flex-1"
          >
            مشاهده جزئیات
          </Button>
          
          {item.liveUrl && (
            <Button variant="outline" size="sm" asChild>
              <a href={item.liveUrl} target="_blank" rel="noopener noreferrer">
                <ExternalLink className="h-4 w-4" />
              </a>
            </Button>
          )}
          
          {item.sourceUrl && (
            <Button variant="outline" size="sm" asChild>
              <a href={item.sourceUrl} target="_blank" rel="noopener noreferrer">
                <Github className="h-4 w-4" />
              </a>
            </Button>
          )}
          
          {isOwner && (
            <Button variant="secondary" size="sm" onClick={onEdit}>
              ویرایش
            </Button>
          )}
        </div>
      </CardFooter>
    </Card>
  );
}
