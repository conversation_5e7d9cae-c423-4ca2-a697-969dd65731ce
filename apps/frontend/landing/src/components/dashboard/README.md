# Dashboard System

سیستم داشبورد نقش‌محور برای خریداران و توسعه‌دهندگان دلفک

## ساختار

### Components

```
components/dashboard/
├── atoms/              # کامپوننت‌های ابتدایی
│   ├── DashboardStatsCard.tsx
│   ├── UserProfileCard.tsx
│   ├── SidebarMenuItem.tsx
│   ├── DashboardNavButton.tsx
│   ├── DashboardDropdown.tsx
│   └── EmptyDashboardSection.tsx
├── molecules/          # کامپوننت‌های ترکیبی
│   ├── DashboardHeader.tsx
│   ├── DashboardSidebar.tsx
│   ├── BuyerOverview.tsx
│   └── CoderOverview.tsx
├── organisms/          # کامپوننت‌های پیچیده
│   ├── DashboardLayout.tsx
│   └── RoleBasedDashboard.tsx
└── providers/
    └── DashboardProvider.tsx
```

### State Management

- استفاده از Jotai برای مدیریت حالت سراسری داشبورد
- فایل‌های store در `lib/store/dashboard.ts`

### Authentication & Authorization

- بررسی احراز هویت و مجوز دسترسی بر اساس نقش کاربر
- فایل‌های helper در `lib/dashboard/auth.ts`

### Routes

- `/buyers` - داشبورد خریداران
- `/coders` - داشبورد توسعه‌دهندگان
- `/dashboard` - redirect به داشبورد مناسب بر اساس نقش

## ویژگی‌های کلیدی

### ✅ پیاده‌سازی شده

1. **احراز هویت نقش‌محور**: بررسی نقش کاربر و هدایت به داشبورد مناسب
2. **رابط کاربری ریسپانسیو**: سازگار با موبایل و دسکتاپ
3. **مدیریت حالت**: استفاده از Jotai برای state management
4. **کامپوننت‌های قابل استفاده مجدد**: ساختار Atomic Design
5. **طراحی فارسی**: تمام متون و UI در زبان فارسی

### 🔄 در حال توسعه

1. **بخش‌های مختلف داشبورد**: پروژه‌ها، تنظیمات، کیف پول، و...
2. **API Integration**: اتصال به backend
3. **Notifications**: سیستم اعلان‌ها
4. **Charts & Analytics**: نمودارها و آمار

## استفاده

### اضافه کردن بخش جدید

```tsx
// 1. اضافه کردن به menu items در lib/dashboard/menu.ts
export const buyerMenuItems: MenuItem[] = [
  // ...existing items
  {
    label: 'بخش جدید',
    icon: 'IconName',
    section: 'new-section',
    roles: ['buyer'],
  },
];

// 2. اضافه کردن render logic در DashboardLayout.tsx
const renderContent = () => {
  switch (currentSection) {
    case 'overview':
      return user.role === 'buyer' ? <BuyerOverview /> : <CoderOverview />;
    case 'new-section':
      return <NewSectionComponent />;
    default:
      return <EmptyDashboardSection />;
  }
};
```

### کامپوننت‌های در دسترس

```tsx
import {
  DashboardStatsCard,
  UserProfileCard,
  DashboardNavButton,
  DashboardDropdown,
  EmptyDashboardSection,
} from '@/components/dashboard';
```

## پیکربندی

### نقش‌های کاربری

- `buyer`: خریدار پروژه
- `coder`: توسعه‌دهنده

### ذخیره‌سازی اطلاعات

- اطلاعات کاربر در cookies
- حالت dashboard در Jotai store

## نکات مهم

1. **امنیت**: همیشه بررسی کنید که کاربر مجوز دسترسی دارد
2. **Performance**: از lazy loading برای بخش‌های بزرگ استفاده کنید
3. **UX**: پیام‌های مناسب برای حالت‌های loading و error
4. **Accessibility**: رعایت اصول دسترسی‌پذیری
