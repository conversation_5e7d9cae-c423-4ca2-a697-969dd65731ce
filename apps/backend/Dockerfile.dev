FROM rust:1.88.0-slim

RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

RUN cargo install cargo-watch

WORKDIR /app

RUN mkdir -p src migrations && \
    echo "fn main() { println!(\"Waiting for sync...\"); std::thread::sleep(std::time::Duration::from_secs(1000)); }" > src/main.rs && \
    echo '[package]\nname = "temp"\nversion = "0.1.0"\nedition = "2021"' > Cargo.toml

EXPOSE 8080

CMD ["cargo", "watch", "-x", "run"]
