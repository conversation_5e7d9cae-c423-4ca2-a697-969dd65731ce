use crate::common::{CommonModule, oauth::GoogleOAuthService, errors::AppError};
use crate::modules::auth::dto::{GoogleUserDto, AuthResponseDto};
use crate::modules::users::{users_service::UsersService, entities::user_entity::UserEntity};
use crate::modules::auth::jwt_strategy::JwtStrategy;

pub struct AuthGoogleService {
    google_oauth_service: GoogleOAuthService,
    users_service: UsersService,
    jwt_strategy: JwtStrategy,
    common_module: CommonModule,
}

impl AuthGoogleService {
    pub fn new(common_module: &CommonModule) -> Self {
        Self {
            google_oauth_service: GoogleOAuthService::new(common_module),
            users_service: UsersService::new(common_module),
            jwt_strategy: JwtStrategy::new(&common_module.config.jwt),
            common_module: common_module.clone(),
        }
    }

    #[allow(dead_code)]
    pub fn get_auth_url(&self, state: Option<&str>) -> String {
        self.google_oauth_service.get_auth_url(state)
    }

    pub async fn handle_callback_with_role(&self, code: &str, role: Option<crate::modules::users::entities::UserRole>) -> Result<AuthResponseDto, AppError> {
        let google_user_info = self.google_oauth_service.authenticate_user(code).await?;

        let existing_user = self.users_service.find_by_email(&google_user_info.email).await?;

        let user = match existing_user {
            Some(user) => user,
            None => {
                let username = format!("google_oauth_{}", google_user_info.id);

                let existing_username_user = self.users_service.find_by_username(&username).await?;
                if let Some(user) = existing_username_user {
                    return Ok(AuthResponseDto {
                        access_token: self.jwt_strategy.generate_access_token(&user.id.to_string())?,
                        refresh_token: self.jwt_strategy.generate_refresh_token(&user.id.to_string())?,
                        token_type: "Bearer".to_string(),
                        expires_in: self.common_module.config.jwt.expires_in,
                        user: user.into(),
                    });
                }

                let selected_role = role.unwrap_or(crate::modules::users::entities::UserRole::Buyer);
                let new_user = UserEntity::new_with_role(
                    google_user_info.email.clone(),
                    username,
                    google_user_info.name.clone(),
                    selected_role,
                );

                self.users_service.create(&new_user).await?
            }
        };

        let access_token = self.jwt_strategy.generate_access_token(&user.id.to_string())?;
        let refresh_token = self.jwt_strategy.generate_refresh_token(&user.id.to_string())?;

        Ok(AuthResponseDto {
            access_token,
            refresh_token,
            token_type: "Bearer".to_string(),
            expires_in: self.common_module.config.jwt.expires_in,
            user: user.into(),
        })
    }

    #[allow(dead_code)]
    pub async fn get_user_info(&self, code: &str) -> Result<GoogleUserDto, AppError> {
        let google_user_info = self.google_oauth_service.authenticate_user(code).await?;

        Ok(GoogleUserDto {
            google_id: google_user_info.id,
            email: google_user_info.email,
            name: google_user_info.name,
            picture: google_user_info.picture,
            verified_email: google_user_info.verified_email,
        })
    }
}
