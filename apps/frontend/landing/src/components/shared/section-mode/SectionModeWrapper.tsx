'use client';
import { SectionMode, useSectionMode } from '@/hooks/use-section-mode';
import React from 'react';

interface SectionModeWrapperProps {
  children: React.ReactNode;
  defaultMode?: SectionMode;
  className?: string;
}

interface SectionModeContextValue {
  mode: SectionMode;
  setMode: (mode: SectionMode) => void;
  isMode: (mode: SectionMode) => boolean;
  resetMode: () => void;
  toggleMode: (mode: SectionMode) => void;
}

const SectionModeContext = React.createContext<SectionModeContextValue | null>(
  null,
);

export const SectionModeWrapper: React.FC<SectionModeWrapperProps> = ({
  children,
  defaultMode = 'default',
  className,
}) => {
  const sectionModeState = useSectionMode(defaultMode);

  return (
    <SectionModeContext.Provider value={sectionModeState}>
      <div className={className}>{children}</div>
    </SectionModeContext.Provider>
  );
};

export const useSectionModeContext = (): SectionModeContextValue => {
  const context = React.useContext(SectionModeContext);
  if (!context) {
    throw new Error(
      'useSectionModeContext must be used within a SectionModeWrapper',
    );
  }
  return context;
};
