use chrono::{DateTime, Utc};
use uuid::Uuid;
use serde::{Deserialize, Serialize};
use utoipa::ToSchema;
use super::super::entities::{UserEntity, UserRole};

#[derive(Debug, Deserialize, Serialize, ToSchema)]
pub struct UserResponseDto {
    #[schema(example = "550e8400-e29b-41d4-a716-************")]
    pub id: Uuid,
    #[schema(example = "<EMAIL>")]
    pub email: String,
    #[schema(example = "johndoe")]
    pub username: String,
    #[schema(example = "2023-01-01T12:00:00Z")]
    pub created_at: DateTime<Utc>,
    #[schema(example = "2023-01-01T12:00:00Z")]
    pub updated_at: DateTime<Utc>,
    #[schema(example = "buyer")]
    pub role: UserRole,
}

impl From<UserEntity> for UserResponseDto {
    fn from(user: UserEntity) -> Self {
        Self {
            id: user.id,
            email: user.email,
            username: user.username,
            created_at: user.created_at,
            updated_at: user.updated_at,
            role: user.role,
        }
    }
}
