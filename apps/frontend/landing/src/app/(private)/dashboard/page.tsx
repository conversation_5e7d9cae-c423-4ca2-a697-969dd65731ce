'use client';

import { LoadingPlaceholder } from '@/components/shared/atoms/LoadingPlaceholder';
import { getCurrentUserRole } from '@/lib/dashboard/auth';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function DashboardPage() {
  const router = useRouter();
  const [isRedirecting, setIsRedirecting] = useState(true);

  useEffect(() => {
    try {
      const userRole = getCurrentUserRole();

      if (!userRole) {
        router.push('/');
        return;
      }

      const dashboardPath = userRole === 'buyer' ? '/buyers' : '/coders';
      router.push(dashboardPath);
    } catch (error) {
      console.error('Error redirecting from dashboard:', error);
      router.push('/');
    } finally {
      setIsRedirecting(false);
    }
  }, [router]);

  if (isRedirecting) {
    return <LoadingPlaceholder />;
  }

  return null;
}
