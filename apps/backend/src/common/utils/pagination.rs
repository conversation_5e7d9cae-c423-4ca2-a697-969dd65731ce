use serde::{Deserialize, Serialize};
use utoipa::ToSchema;

#[derive(Debug, Deserialize, ToSchema)]
pub struct PaginationParams {
    #[schema(example = 1, minimum = 1)]
    pub page: Option<i64>,

    #[schema(example = 10, minimum = 1, maximum = 100)]
    pub limit: Option<i64>,
}

impl PaginationParams {
    pub fn get_page(&self) -> i64 {
        self.page.unwrap_or(1).max(1)
    }

    pub fn get_limit(&self) -> i64 {
        self.limit.unwrap_or(10).clamp(1, 100)
    }

    pub fn get_offset(&self) -> i64 {
        (self.get_page() - 1) * self.get_limit()
    }
}

#[derive(Debug, Serialize, ToSchema)]
pub struct PaginationMeta {
    #[schema(example = 1)]
    pub current_page: i64,

    #[schema(example = 10)]
    pub per_page: i64,

    #[schema(example = 100)]
    pub total: i64,

    #[schema(example = 10)]
    pub total_pages: i64,

    #[schema(example = true)]
    pub has_next_page: bool,

    #[schema(example = false)]
    pub has_prev_page: bool,
}

impl PaginationMeta {
    pub fn new(current_page: i64, per_page: i64, total: i64) -> Self {
        let total_pages = (total + per_page - 1) / per_page;

        Self {
            current_page,
            per_page,
            total,
            total_pages,
            has_next_page: current_page < total_pages,
            has_prev_page: current_page > 1,
        }
    }
}

#[derive(Debug, Serialize, ToSchema)]
pub struct PaginatedResponse<T> {
    pub data: Vec<T>,
    pub meta: PaginationMeta,
}

impl<T> PaginatedResponse<T> {
    pub fn new(data: Vec<T>, params: &PaginationParams, total: i64) -> Self {
        let meta = PaginationMeta::new(params.get_page(), params.get_limit(), total);

        Self {
            data,
            meta,
        }
    }
}
