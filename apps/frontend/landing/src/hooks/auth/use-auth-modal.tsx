'use client';

import { TokenManager } from '@/lib/auth/token-manager';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from 'react';
import { useAuth } from './use-auth';

interface AuthModalContextType {
  isOpen: boolean;
  openModal: () => void;
  closeModal: () => void;
  resetModal: () => void;
  authStatus: 'idle' | 'loading' | 'success' | 'error';
  authError: string | null;
}

const AuthModalContext = createContext<AuthModalContextType | undefined>(
  undefined,
);

export function AuthModalProvider({ children }: { children: ReactNode }) {
  const [isOpen, setIsOpen] = useState(false);
  const [authStatus, setAuthStatus] = useState<
    'idle' | 'loading' | 'success' | 'error'
  >('idle');
  const [authError, setAuthError] = useState<string | null>(null);
  const { refreshUser } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();

  const openModal = () => {
    setIsOpen(true);
    setAuthStatus('idle');
    setAuthError(null);
  };

  const closeModal = () => {
    setIsOpen(false);
    setAuthStatus('idle');
    setAuthError(null);
  };

  const resetModal = () => {
    setAuthStatus('idle');
    setAuthError(null);
  };

  // Handle OAuth callbacks and auth success/error from URL params
  useEffect(() => {
    const handleAuthFromUrl = async () => {
      const accessToken = searchParams.get('access_token');
      const refreshToken = searchParams.get('refresh_token');
      const error = searchParams.get('error');

      if (error) {
        setAuthError(decodeURIComponent(error));
        setAuthStatus('error');
        openModal();
        // Clean URL
        const url = new URL(window.location.href);
        url.searchParams.delete('error');
        router.replace(url.pathname + url.search);
        return;
      }

      if (accessToken && refreshToken) {
        setAuthStatus('loading');
        try {
          TokenManager.setTokens(accessToken, refreshToken);
          await refreshUser();
          setAuthStatus('success');

          const redirectPath =
            localStorage.getItem('redirect_after_login') || '/';
          localStorage.removeItem('redirect_after_login');

          // Clean URL
          const url = new URL(window.location.href);
          url.searchParams.delete('access_token');
          url.searchParams.delete('refresh_token');
          router.replace(url.pathname + url.search);

          setTimeout(() => {
            closeModal();
            if (redirectPath !== window.location.pathname) {
              router.push(redirectPath);
            }
          }, 2000);
        } catch (err) {
          setAuthError('خطا در تکمیل احراز هویت');
          setAuthStatus('error');
          openModal();
        }
      }
    };

    handleAuthFromUrl();
  }, [searchParams, refreshUser, router]);

  return (
    <AuthModalContext.Provider
      value={{
        isOpen,
        openModal,
        closeModal,
        resetModal,
        authStatus,
        authError,
      }}
    >
      {children}
    </AuthModalContext.Provider>
  );
}

export function useAuthModal() {
  const context = useContext(AuthModalContext);
  if (context === undefined) {
    throw new Error('useAuthModal must be used within an AuthModalProvider');
  }
  return context;
}
