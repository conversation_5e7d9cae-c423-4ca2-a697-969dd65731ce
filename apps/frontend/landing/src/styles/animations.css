/* Geeky matrix rain effect */
@keyframes matrix-rain {
  0% {
    transform: translateY(-100vh) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotate(360deg);
    opacity: 0;
  }
}

.matrix-rain {
  animation: matrix-rain 8s linear infinite;
}

/* Terminal cursor blink */
@keyframes cursor-blink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

.cursor-blink {
  animation: cursor-blink 1s infinite;
}

/* Code typing effect */
@keyframes typing {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

.typing-effect {
  overflow: hidden;
  border-right: 2px solid var(--primary);
  white-space: nowrap;
  animation:
    typing 3.5s steps(40, end),
    cursor-blink 1s step-end infinite;
}

/* Glitch effect */
@keyframes glitch {
  0% {
    transform: translate(0);
  }
  20% {
    transform: translate(-2px, 2px);
  }
  40% {
    transform: translate(-2px, -2px);
  }
  60% {
    transform: translate(2px, 2px);
  }
  80% {
    transform: translate(2px, -2px);
  }
  100% {
    transform: translate(0);
  }
}

.glitch:hover {
  animation: glitch 0.3s;
}

/* Floating particles */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-10px) rotate(90deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
  75% {
    transform: translateY(-10px) rotate(270deg);
  }
}

.float {
  animation: float 6s ease-in-out infinite;
}

/* Scan line effect */
@keyframes scan-line {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(100vh);
  }
}

.scan-line {
  position: absolute;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--primary), transparent);
  animation: scan-line 3s linear infinite;
}

/* Animation styles for blob effects */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Shake animation for error messages */
@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-5px);
  }
  20%,
  40%,
  60%,
  80% {
    transform: translateX(5px);
  }
}

.animate-shake {
  animation: shake 0.6s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}

/* Fade in animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-in-out forwards;
}

/* Holographic text effect */
.holographic-text {
  background: linear-gradient(
    45deg,
    var(--primary),
    var(--accent),
    var(--secondary),
    var(--primary)
  );
  background-size: 400% 400%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: holographic 3s ease-in-out infinite;
}

@keyframes holographic {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Data stream effect */
@keyframes data-stream {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateX(100vw);
    opacity: 0;
  }
}

.data-stream {
  animation: data-stream 4s linear infinite;
}

/* Code block enhancement */
.code-block::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    transparent 0%,
    var(--primary) 50%,
    transparent 100%
  );
  opacity: 0.03;
  animation: code-highlight 4s ease-in-out infinite alternate;
}

@keyframes code-highlight {
  0% {
    opacity: 0.01;
  }
  100% {
    opacity: 0.05;
  }
}

/* Lighter code block variant */
.code-block-light::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    transparent 0%,
    var(--primary) 50%,
    transparent 100%
  );
  opacity: 0.008;
  animation: code-highlight-light 6s ease-in-out infinite alternate;
}

@keyframes code-highlight-light {
  0% {
    opacity: 0.003;
  }
  100% {
    opacity: 0.015;
  }
}

/* Particle system */
.particles {
  position: absolute;
  width: 2px;
  height: 2px;
  background: var(--primary);
  border-radius: 50%;
  animation: particles 8s linear infinite;
}

@keyframes particles {
  0% {
    transform: translateY(100vh) translateX(0) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) translateX(200px) rotate(360deg);
    opacity: 0;
  }
}

/* Glitch text variations */
.glitch-intense::before {
  color: var(--destructive);
  animation: glitch-1 0.5s infinite linear alternate-reverse;
}

.glitch-intense::after {
  color: var(--accent);
  animation: glitch-2 0.5s infinite linear alternate-reverse;
}

@keyframes glitch-1 {
  0% {
    transform: translateX(0);
  }
  20% {
    transform: translateX(-2px);
  }
  40% {
    transform: translateX(2px);
  }
  60% {
    transform: translateX(-1px);
  }
  80% {
    transform: translateX(1px);
  }
  100% {
    transform: translateX(0);
  }
}

@keyframes glitch-2 {
  0% {
    transform: translateX(0);
  }
  20% {
    transform: translateX(2px);
  }
  40% {
    transform: translateX(-2px);
  }
  60% {
    transform: translateX(1px);
  }
  80% {
    transform: translateX(-1px);
  }
  100% {
    transform: translateX(0);
  }
}

/* Loading spinner with cyber style */
.cyber-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--muted);
  border-top: 2px solid var(--primary);
  border-radius: 50%;
  animation: cyber-spin 1s linear infinite;
}

@keyframes cyber-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Typewriter effect for text */
.typewriter {
  overflow: hidden;
  border-right: 2px solid var(--primary);
  white-space: nowrap;
  animation:
    typewriter 4s steps(40, end),
    blink-caret 0.75s step-end infinite;
}

@keyframes typewriter {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes blink-caret {
  from,
  to {
    border-color: transparent;
  }
  50% {
    border-color: var(--primary);
  }
}
