use serde::{Deserialize, Serialize};
use utoipa::ToSchema;
use crate::common::{validators::DtoValidator, errors::AppError};

#[derive(Debug, Deserialize, Serialize, ToSchema)]
pub struct RefreshTokenDto {
    #[schema(example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")]
    #[serde(default)]
    pub refresh_token: String,
}

impl DtoValidator for RefreshTokenDto {
    fn validate_dto(&self) -> Result<(), AppError> {
        Ok(())
    }
}
