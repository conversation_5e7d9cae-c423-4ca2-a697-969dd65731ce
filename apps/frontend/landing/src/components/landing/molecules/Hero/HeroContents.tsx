'use client';

import { useSectionModeContext } from '@/components/shared/section-mode/SectionModeWrapper';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Code2, Terminal } from 'lucide-react';
import { useEffect, useState } from 'react';
import HeroButton from '../../atoms/HeroButton';
import { HeroWizard } from './HeroWizard';
interface Props {
  className?: string;
}
export const HeroContents = ({ className }: Props) => {
  const { mode, setMode } = useSectionModeContext();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleStartWizard = () => {
    setMode('wizard');
  };

  if (mode === 'wizard') {
    return <HeroWizard />;
  }
  return (
    <section
      className={`relative overflow-hidden py-3 sm:py-4 lg:py-6 ${className || ''}`}
    >
      {/* Simplified background */}
      <div className="absolute inset-0 bg-gradient-to-br from-background via-card to-secondary" />

      {/* Reduced matrix rain effect */}
      <div className="absolute inset-0 overflow-hidden opacity-30">
        {isClient &&
          Array.from({ length: 8 }).map((_, i) => (
            <div
              key={i}
              className="absolute text-primary/15 font-mono text-xs matrix-rain"
              style={{
                left: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 8}s`,
                animationDuration: `${8 + Math.random() * 4}s`,
              }}
            >
              {Math.random().toString(36).substring(2, 8)}
            </div>
          ))}
      </div>

      {/* Minimal background shapes */}
      <div className="absolute top-20 left-10 w-32 h-32 rounded-full bg-primary/5 mix-blend-screen filter blur-2xl animate-blob" />
      <div className="absolute top-40 right-10 w-40 h-40 rounded-full bg-accent/5 mix-blend-screen filter blur-2xl animate-blob animation-delay-2000" />

      <div className="container relative z-10 mx-auto px-4 text-center">
        {/* Compact terminal-like badge */}
        <Badge
          variant="outline"
          className="mb-2 sm:mb-3 terminal-window px-2 sm:px-3 pt-6 text-xs sm:text-sm backdrop-blur-md font-mono shadow-lg"
        >
          <Terminal className="w-3 h-3 mr-2 text-primary" />
          <span className="text-primary font-medium">~/dolfak $</span>
          <span className="text-foreground ml-2">init</span>
          <span className="cursor-blink text-primary ml-1">|</span>
        </Badge>

        {/* Streamlined heading */}
        <h1 className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold mb-2 sm:mb-3 leading-tight font-mono text-center">
          <span className="block text-primary">رشد منحصر به فرد</span>
          <span className="block text-accent mt-1">دلفک</span>
          <span className="block text-foreground mt-1 text-sm sm:text-base md:text-lg">
            ما ایده‌های شما را به واقعیت تبدیل می‌کنیم
          </span>
        </h1>

        {/* Compact terminal-style description */}
        <Card className="w-full max-w-xs sm:max-w-sm md:max-w-md mx-auto mb-3 sm:mb-4 bg-card/90 border border-primary/20 rounded-lg backdrop-blur-sm shadow-lg">
          <CardContent className="p-2 sm:p-3 font-mono text-left">
            <div dir="ltr" className="flex items-center mb-2">
              <div className="flex gap-1">
                <div className="w-2 h-2 rounded-full bg-destructive"></div>
                <div className="w-2 h-2 rounded-full bg-accent"></div>
                <div className="w-2 h-2 rounded-full bg-primary"></div>
              </div>
              <span className="text-muted-foreground ml-2 text-xs truncate">
                platform.dolfak.com
              </span>
            </div>
            <div className="space-y-1 text-xs sm:text-sm">
              <div>
                <span className="text-primary">user@dolfak:~$</span>{' '}
                <span className="text-foreground">cat solution.txt</span>
              </div>
              <div className="text-accent leading-relaxed">
                ایده ی استارتاپی داری؟ همین اول زیاد خرج نکن
              </div>
              <div className="text-foreground leading-relaxed">
                یه اپلیکیشن آماده بخر یا یه تیم از ما استخدام کن
              </div>
              <div className="mt-2">
                <Progress value={75} className="h-1" />
                <div className="text-xs text-muted-foreground mt-1">
                  Loading solutions... 75%
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <HeroButton handleStartWizard={handleStartWizard} />

        {/* Minimal floating code elements */}
        <div className="absolute top-1/4 left-1/4 text-primary/10 font-mono text-xs opacity-50">
          <Code2 className="w-3 h-3 inline mr-1" />
          {'{ dev }'}
        </div>
        <div
          className="absolute bottom-1/4 right-1/3 text-accent/10 font-mono text-xs opacity-50"
          style={{ animationDelay: '2s' }}
        >
          {'</>'}
        </div>
      </div>
    </section>
  );
};
