# Error Translation System

This system provides comprehensive English to Persian error message translation for all backend API errors in the Dolfak application.

## Features

- **Automatic Translation**: All axios responses are automatically translated via the API interceptor
- **Manual Error Handling**: Utilities for manual error handling in components
- **Network Error Support**: Handles network connectivity issues
- **Comprehensive Coverage**: Supports all backend error types and HTTP status codes
- **Persian Fallbacks**: Smart partial matching and fallback messages in Persian

## Components

### ErrorTranslator

Core translation engine that maps English error messages to Persian equivalents.

**Supported Error Types:**

- Backend API errors (`validation_error`, `authentication_error`, etc.)
- Custom error messages from Rust backend
- HTTP status codes (400, 401, 403, 404, 409, etc.)
- Network errors (`ECONNREFUSED`, `ENOTFOUND`, timeouts)
- Common validation messages

### ErrorHandler

Higher-level utility for handling and displaying errors.

**Methods:**

- `handleApiError(error)`: Translates axios errors
- `showErrorToast(error)`: Shows translated error as toast notification
- `showErrorMessage(message, statusCode?)`: Shows custom error messages
- `getErrorMessage(error)`: Returns translated error string

## Usage

### Automatic (Recommended)

The system works automatically with all API calls made through the configured axios instance:

```typescript
import api from '@/lib/core/api';

// Errors are automatically translated and shown as toasts
const response = await api.post('/auth/login', credentials);
```

### Manual Error Handling

For custom error handling:

```typescript
import { ErrorHandler, ErrorTranslator } from '@/lib/core';

try {
  const response = await api.post('/users', userData);
} catch (error) {
  // Show error toast
  ErrorHandler.showErrorToast(error);

  // Or get error message without showing toast
  const message = ErrorHandler.getErrorMessage(error);
  console.log(message); // Persian error message
}
```

### Direct Translation

For translating specific error objects:

```typescript
import { ErrorTranslator } from '@/lib/core';

// Translate backend error response
const translated = ErrorTranslator.translate(
  {
    error: 'authentication_error',
    message: 'Invalid credentials',
  },
  401,
);
console.log(translated); // "اطلاعات ورود نامعتبر است"

// Translate network error
const networkError = ErrorTranslator.translateNetworkError({
  code: 'ECONNREFUSED',
});
console.log(networkError); // "اتصال رد شد"
```

## Error Mappings

### Backend Error Codes

- `validation_error` → "خطای اعتبارسنجی"
- `authentication_error` → "خطای احراز هویت"
- `authorization_error` → "عدم دسترسی"
- `not_found` → "یافت نشد"
- `conflict` → "تداخل"
- `bad_request` → "درخواست نامعتبر"
- `internal_server_error` → "خطای داخلی سرور"

### Custom Messages

- "User not found" → "کاربر یافت نشد"
- "Invalid credentials" → "اطلاعات ورود نامعتبر است"
- "Email already exists" → "این ایمیل قبلاً ثبت شده است"
- "Username already exists" → "این نام کاربری قبلاً گرفته شده است"
- "Invalid OTP code" → "کد تأیید نامعتبر است"
- And many more...

### HTTP Status Codes

- 400 → "درخواست نامعتبر"
- 401 → "احراز هویت نشده‌اید"
- 403 → "دسترسی مجاز نیست"
- 404 → "صفحه یافت نشد"
- 409 → "تداخل در داده‌ها"
- 500 → "خطای داخلی سرور"

### Network Errors

- `ECONNREFUSED` → "اتصال رد شد"
- `ENOTFOUND` → "سرور یافت نشد"
- Timeout errors → "زمان درخواست به پایان رسید"
- Network Error → "خطا در اتصال به شبکه"

## Smart Translation Features

### Partial Matching

The system can detect and translate partial matches in error messages:

- Messages containing "password" → "خطا در رمز عبور"
- Messages containing "email" → "خطا در ایمیل"
- Messages containing "validation" → "اطلاعات وارد شده نامعتبر است"

### English Detection

Only English error messages are translated. Persian messages are passed through unchanged.

### Fallback System

If no specific translation is found:

1. Try partial matching
2. Use HTTP status code message
3. Return original message
4. Default to "خطای نامشخص"

## Integration

The error translation system is integrated into:

- **Axios Interceptor**: Automatic translation of all API errors
- **Toast Notifications**: All error toasts use Persian messages
- **Auth System**: Login/registration error handling
- **Form Validation**: Field validation error messages

## Extending

To add new error translations, update the `translations` object in `error-translator.ts`:

```typescript
private static readonly translations: ErrorTranslation = {
  // Add new mappings
  'New error message': 'پیام خطای جدید',
  // ...existing mappings
};
```
