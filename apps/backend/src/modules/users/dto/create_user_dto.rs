use serde::{Deserialize, Serialize};
use validator::Valida<PERSON>;
use utoipa::ToSchema;
use crate::common::validators::custom_validators::{validate_password, validate_username};
use crate::modules::users::entities::UserRole;

#[derive(Debug, Deserialize, Serialize, Validate, ToSchema)]
pub struct CreateUserDto {
    #[validate(email(message = "Invalid email format"))]
    #[schema(example = "<EMAIL>")]
    pub email: String,

    #[validate(custom(function = "validate_username"))]
    #[schema(example = "johndoe")]
    pub username: String,

    #[validate(custom(function = "validate_password"))]
    #[schema(example = "SecurePass123!")]
    pub password: String,

    #[schema(example = "buyer")]
    pub role: Option<UserRole>,
}
