use super::AppError;

pub struct CustomErrors;

impl CustomErrors {
    pub fn user_not_found() -> AppError {
        AppError::NotFound("User not found".to_string())
    }

    pub fn invalid_credentials() -> AppError {
        AppError::Authentication("Invalid credentials".to_string())
    }

    pub fn email_already_exists() -> AppError {
        AppError::Conflict("Email already exists".to_string())
    }

    pub fn username_already_exists() -> AppError {
        AppError::Conflict("Username already exists".to_string())
    }

    pub fn passwords_do_not_match() -> AppError {
        AppError::BadRequest("Passwords do not match".to_string())
    }

    pub fn invalid_token() -> AppError {
        AppError::Authentication("Invalid or expired token".to_string())
    }

    #[allow(dead_code)]
    pub fn insufficient_permissions() -> AppError {
        AppError::Authorization("Insufficient permissions".to_string())
    }

    pub fn invalid_phone_number() -> AppError {
        AppError::BadRequest("Invalid phone number format".to_string())
    }

    pub fn invalid_otp() -> AppError {
        AppError::BadRequest("Invalid OTP code".to_string())
    }

    pub fn otp_expired() -> AppError {
        AppError::BadRequest("OTP code has expired".to_string())
    }

    pub fn otp_already_used() -> AppError {
        AppError::BadRequest("OTP code has already been used".to_string())
    }

    pub fn otp_too_many_attempts() -> AppError {
        AppError::BadRequest("Too many OTP verification attempts".to_string())
    }

    #[allow(dead_code)]
    pub fn phone_not_verified() -> AppError {
        AppError::BadRequest("Phone number not verified".to_string())
    }

    #[allow(dead_code)]
    pub fn phone_already_exists() -> AppError {
        AppError::Conflict("Phone number already exists".to_string())
    }
}
