'use client';

import { Home } from 'lucide-react';

interface Props {
  onClick?: () => void;
}
const BackButton = (props: Props) => {
  const handleOnClick = () => {
    if (props.onClick) {
      props.onClick();
    } else {
      window.location.href = '/';
    }
  };
  return (
    <Home
      className="border border-primary rounded-md p-1 w-8 h-8 text-green-400! hover:text-green-500! transition-colors cursor-pointer"
      onClick={handleOnClick}
    />
  );
};
export default BackButton;
