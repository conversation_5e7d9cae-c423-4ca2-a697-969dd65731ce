import { Components } from '@/components/registry';
import { Code, Globe, Server, Smartphone } from 'lucide-react';
import React from 'react';

interface ServicesCategoriesProps {
  className?: string;
}

const ServicesCategories: React.FC<ServicesCategoriesProps> = ({
  className,
}) => {
  const categories = [
    {
      icon: Code,
      title: 'توسعه نرم‌افزار',
      description:
        'راهکارهای سفارشی برای کسب و کار شما با جدیدترین تکنولوژی‌ها',
      count: 8,
    },
    {
      icon: Smartphone,
      title: 'اپلیکیشن موبایل',
      description:
        'طراحی و توسعه اپلیکیشن‌های موبایل حرفه‌ای برای iOS و Android',
      count: 6,
    },
    {
      icon: Globe,
      title: 'طراحی وب',
      description: 'وب‌سایت‌های واکنش‌گرا و مدرن با تجربه کاربری بی‌نظیر',
      count: 10,
    },
    {
      icon: Server,
      title: 'طراحی بک اند',
      description: 'طراحی بک اند سریع امن و مدرن برای اپلیکیشن‌های شما',
      count: 10,
    },
  ];

  return (
    <section
      className={`py-12 lg:py-16 bg-background relative ${className || ''}`}
    >
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 text-primary/5 font-mono text-sm rotate-45 animate-pulse">
          &lt;services/&gt;
        </div>
        <div
          className="absolute top-1/3 right-1/3 text-accent/5 font-mono text-sm -rotate-45 animate-pulse"
          style={{ animationDelay: '2s' }}
        >
          {'{categories}'}
        </div>
        <div
          className="absolute bottom-1/4 left-1/2 text-primary/5 font-mono text-sm rotate-12 animate-pulse"
          style={{ animationDelay: '4s' }}
        >
          function()
        </div>
      </div>

      <div className="container relative z-10 mx-auto px-4">
        <div className="text-center mb-12">
          <div className="inline-block mb-4 px-4 py-2 bg-card/80 text-primary rounded-lg text-sm font-mono border border-primary/20">
            <span className="text-accent">const</span> {'services = ['}
            <span className="text-primary">'comprehensive'</span>
            {'];'}
          </div>
          <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-4 font-mono text-foreground">
            دسته‌بندی خدمات ما
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
            از توسعه نرم‌افزار تا امنیت سایبری، تمام نیازهای فناوری شما را پوشش
            می‌دهیم
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
          {categories.map((category, index) => (
            <Components.ServiceCategoryCard
              key={index}
              icon={category.icon}
              title={category.title}
              description={category.description}
              count={category.count}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default ServicesCategories;
