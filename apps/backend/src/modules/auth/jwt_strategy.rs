use jsonwebtoken::{encode, decode, Header, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Enco<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>};
use serde::{Deserialize, Serialize};
use chrono::{Utc, Duration};
use crate::common::{config::JwtConfig, errors::AppError};

#[derive(Debug, Serialize, Deserialize)]
struct Claims {
    sub: String,
    exp: usize,
    iat: usize,
    token_type: String,
}

pub struct JwtStrategy {
    config: JwtConfig,
}

impl JwtStrategy {
    pub fn new(config: &JwtConfig) -> Self {
        Self {
            config: config.clone(),
        }
    }

    pub fn generate_access_token(&self, user_id: &str) -> Result<String, AppError> {
        let now = Utc::now();
        let expiration = now + Duration::seconds(self.config.expires_in as i64);

        let claims = Claims {
            sub: user_id.to_string(),
            exp: expiration.timestamp() as usize,
            iat: now.timestamp() as usize,
            token_type: "access".to_string(),
        };

        encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(self.config.secret.as_ref()),
        )
        .map_err(AppError::Jwt)
    }

    pub fn generate_refresh_token(&self, user_id: &str) -> Result<String, AppError> {
        let now = Utc::now();
        let expiration = now + Duration::days(7);

        let claims = Claims {
            sub: user_id.to_string(),
            exp: expiration.timestamp() as usize,
            iat: now.timestamp() as usize,
            token_type: "refresh".to_string(),
        };

        encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(self.config.secret.as_ref()),
        )
        .map_err(AppError::Jwt)
    }

    pub fn verify_token(&self, token: &str) -> Result<String, AppError> {
        let validation = Validation::new(Algorithm::HS256);

        let token_data = decode::<Claims>(
            token,
            &DecodingKey::from_secret(self.config.secret.as_ref()),
            &validation,
        )
        .map_err(AppError::Jwt)?;

        Ok(token_data.claims.sub)
    }

    pub fn verify_refresh_token(&self, token: &str) -> Result<String, AppError> {
        let validation = Validation::new(Algorithm::HS256);

        let token_data = decode::<Claims>(
            token,
            &DecodingKey::from_secret(self.config.secret.as_ref()),
            &validation,
        )
        .map_err(AppError::Jwt)?;

        if token_data.claims.token_type != "refresh" {
            return Err(AppError::Jwt(jsonwebtoken::errors::Error::from(
                jsonwebtoken::errors::ErrorKind::InvalidToken
            )));
        }

        Ok(token_data.claims.sub)
    }
}
