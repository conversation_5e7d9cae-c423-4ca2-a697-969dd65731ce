use serde::{Deserialize, Serialize};
use utoipa::ToSchema;
use validator::Validate;
use crate::modules::users::entities::UserRole;

#[derive(Debug, Deserialize, Serialize, Validate, ToSchema)]
pub struct SendOtpDto {
    #[validate(length(min = 10, max = 15, message = "Phone number must be between 10 and 15 characters"))]
    pub phone: String,
}

#[derive(Debug, Deserialize, Serialize, Validate, ToSchema)]
pub struct VerifyOtpDto {
    #[validate(length(min = 10, max = 15, message = "Phone number must be between 10 and 15 characters"))]
    pub phone: String,
    #[validate(length(min = 4, max = 8, message = "OTP code must be between 4 and 8 characters"))]
    pub otp_code: String,
}

#[derive(Debug, Deserialize, Serialize, Validate, ToSchema)]
pub struct PhoneLoginDto {
    #[validate(length(min = 10, max = 15, message = "Phone number must be between 10 and 15 characters"))]
    pub phone: String,
    #[validate(length(min = 4, max = 8, message = "OTP code must be between 4 and 8 characters"))]
    pub otp_code: String,
    #[schema(example = "buyer")]
    pub role: Option<UserRole>,
}
