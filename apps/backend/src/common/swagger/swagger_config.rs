use utoipa::OpenApi;
use utoipa::openapi::security::{SecurityScheme, HttpAuthScheme, Http};
use utoipa::Modify;

struct SecurityAddon;

impl Modify for SecurityAddon {
    fn modify(&self, openapi: &mut utoipa::openapi::OpenApi) {
        let components = openapi.components.as_mut().unwrap();
        components.add_security_scheme(
            "bearer_auth",
            SecurityScheme::Http(Http::new(HttpAuthScheme::Bearer))
        );
    }
}

#[derive(OpenApi)]
#[openapi(
    paths(
        crate::modules::health::health_controller::check,
        crate::modules::health::health_controller::ready,
        crate::modules::health::health_controller::live,
        crate::modules::auth::auth_controller::login,
        crate::modules::auth::auth_controller::register,
        crate::modules::auth::auth_controller::refresh_token,
        crate::modules::auth::auth_controller::logout,
        crate::modules::auth::auth_controller::send_otp,
        crate::modules::auth::auth_controller::verify_otp,
        crate::modules::auth::auth_controller::phone_login,
        crate::modules::auth::auth_controller::google_auth_url,
        crate::modules::auth::auth_controller::google_callback_redirect,
        crate::modules::auth::auth_controller::google_callback,
        crate::modules::users::users_controller::get_all,
        crate::modules::users::users_controller::get_by_id,
        crate::modules::users::users_controller::create,
        crate::modules::users::users_controller::update,
        crate::modules::users::users_controller::delete,
        crate::modules::users::users_controller::get_me,
        crate::modules::users::users_controller::get_profile,
        crate::modules::wizard::wizard_controller::submit_wizard,
        crate::modules::wizard::wizard_controller::get_all_submissions,
        crate::modules::wizard::wizard_controller::get_submission_by_id,
        crate::modules::wizard::wizard_controller::update_submission_status,
    ),
    components(schemas(
        crate::modules::auth::dto::LoginDto,
        crate::modules::auth::dto::RegisterDto,
        crate::modules::auth::dto::AuthResponseDto,
        crate::modules::auth::dto::RefreshTokenDto,
        crate::modules::shared::otp::SendOtpDto,
        crate::modules::shared::otp::VerifyOtpDto,
        crate::modules::shared::otp::PhoneLoginDto,
        crate::modules::auth::dto::GoogleAuthCallbackDto,
        crate::modules::auth::dto::GoogleAuthUrlDto,
        crate::modules::users::dto::UserResponseDto,
        crate::modules::users::dto::CreateUserDto,
        crate::modules::users::dto::UpdateUserDto,
        crate::modules::wizard::dto::CreateWizardSubmissionDto,
        crate::modules::wizard::dto::WizardSubmissionResponseDto,
        crate::modules::wizard::wizard_controller::UpdateStatusRequest,
        crate::modules::health::health_service::HealthStatus,
        crate::modules::health::health_service::ReadinessStatus,
        crate::modules::health::health_service::ReadinessChecks,
        crate::modules::health::health_service::LivenessStatus,
        crate::common::utils::pagination::PaginationParams,
        crate::common::utils::pagination::PaginationMeta,
        crate::common::utils::pagination::PaginatedResponse<crate::modules::users::dto::UserResponseDto>,
        crate::common::utils::pagination::PaginatedResponse<crate::modules::wizard::dto::WizardSubmissionResponseDto>,
        crate::common::errors::ErrorResponse,
    )),
    modifiers(&SecurityAddon),
    tags(
        (name = "auth", description = "Authentication endpoints"),
        (name = "users", description = "User management endpoints"),
        (name = "health", description = "Health check endpoints"),
        (name = "wizard", description = "Wizard submission endpoints")
    )
)]
#[allow(dead_code)]
pub struct ApiDoc;

#[allow(dead_code)]
pub struct SwaggerConfig;

#[allow(dead_code)]
impl SwaggerConfig {
    pub fn create_docs() -> utoipa::openapi::OpenApi {
        let mut openapi = ApiDoc::openapi();
        openapi.info.title = "Dolfak API".to_string();
        openapi.info.version = "1.0.0".to_string();
        openapi.info.description = Some("Dolfak SuperApp Backend API".to_string());
        openapi
    }
}
