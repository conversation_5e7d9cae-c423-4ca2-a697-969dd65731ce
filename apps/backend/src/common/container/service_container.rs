use std::sync::Arc;
use crate::common::config::app_config::AppConfig;
use crate::common::database::connection::DatabaseConnection;
use crate::common::cache::cache_service::CacheService;
use crate::common::logger::LoggerService;

/// Service Container for dependency injection
/// This provides a clean way to manage service dependencies without tight coupling
pub struct ServiceContainer {
    pub config: Arc<AppConfig>,
    pub database: Arc<DatabaseConnection>,
    pub cache: Arc<CacheService>,
    pub logger: Arc<LoggerService>,
}

impl ServiceContainer {
    /// Create a new service container with all dependencies
    #[allow(dead_code)]
    pub fn new(
        config: AppConfig,
        database: DatabaseConnection,
        cache: CacheService,
    ) -> Self {
        let logger = LoggerService::new();

        Self {
            config: Arc::new(config),
            database: Arc::new(database),
            cache: Arc::new(cache),
            logger: Arc::new(logger),
        }
    }

    /// Get a reference to the configuration
    #[allow(dead_code)]
    pub fn config(&self) -> &AppConfig {
        &self.config
    }

    /// Get a reference to the database connection
    #[allow(dead_code)]
    pub fn database(&self) -> &DatabaseConnection {
        &self.database
    }

    /// Get a reference to the cache service
    #[allow(dead_code)]
    pub fn cache(&self) -> &CacheService {
        &self.cache
    }

    /// Get a reference to the logger service
    #[allow(dead_code)]
    pub fn logger(&self) -> &LoggerService {
        &self.logger
    }
}

impl Clone for ServiceContainer {
    fn clone(&self) -> Self {
        Self {
            config: Arc::clone(&self.config),
            database: Arc::clone(&self.database),
            cache: Arc::clone(&self.cache),
            logger: Arc::clone(&self.logger),
        }
    }
}
