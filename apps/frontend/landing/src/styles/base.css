@layer base {
  html {
    font-size: var(--f-size);
  }
  h1 {
    font-size: calc(var(--f-size) * pow(var(--type-scale), 4));
    line-height: 1.2;
  }
  h2 {
    font-size: calc(var(--f-size) * pow(var(--type-scale), 3));
  }
  h3 {
    font-size: calc(var(--f-size) * pow(var(--type-scale), 2));
  }
  h4 {
    font-size: calc(var(--f-size) * var(--type-scale));
  }
  p {
    margin-bottom: calc(1ex * var(--type-scale));
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground min-h-screen;
    font-family: var(--font-yekanBakhFaNum);
    overflow-y: scroll;
  }

  /* Text clamp utilities */
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Prevent CLS for interactive elements */
  button,
  a[role='button'],
  input,
  select,
  textarea {
    min-height: var(--min-tap-target-height);
  }

  /* Override min-height for specific small interactive elements */
  button[data-slot='checkbox'],
  [role='checkbox'] {
    min-height: unset !important;
  }

  /* Ensure inputs have proper background and border when filled */
  input:not(:placeholder-shown),
  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus {
    background-color: var(--input) !important;
    color: var(--foreground) !important;
    -webkit-box-shadow: 0 0 0px 1000px var(--input) inset !important;
    -webkit-text-fill-color: var(--foreground) !important;
    transition: background-color 5000s ease-in-out 0s;
    border: 1px solid var(--border) !important;
  }

  /* Ensure all inputs have a border by default */
  input {
    border: 1px solid var(--border) !important;
  }

  /* Preserve focus styles for inputs */
  input:focus-visible {
    border-color: var(--primary) !important;
    box-shadow: 0 0 0 3px var(--ring) !important;
  }

  /* Preserve hover styles */
  input:hover {
    border-color: var(--accent) !important;
  }
}
