import { z } from 'zod';

export const loginSchema = z.object({
  email: z.string().min(1, 'ایمیل اجباری است').email('فرمت ایمیل نامعتبر است'),
  password: z.string().min(1, 'رمز عبور اجباری است'),
});

export const phoneSchema = z.object({
  phone: z
    .string()
    .min(11, 'شماره تلفن باید ۱۱ رقم باشد')
    .max(11, 'شماره تلفن باید ۱۱ رقم باشد')
    .regex(/^09[0-9]{9}$/, 'فرمت شماره تلفن نامعتبر است'),
});

export const otpSchema = z.object({
  phone: z.string(),
  otp_code: z
    .string()
    .min(4, 'کد تایید باید حداقل ۴ رقم باشد')
    .max(8, 'کد تایید نباید بیشتر از ۸ رقم باشد')
    .regex(/^\d+$/, 'کد تایید فقط باید شامل اعداد باشد'),
});

export const googleCallbackSchema = z.object({
  code: z.string().min(1, 'کد احراز هویت ضروری است'),
  state: z.string().optional(),
});

export const signupSchema = z
  .object({
    username: z
      .string()
      .min(3, 'نام کاربری باید حداقل ۳ کاراکتر باشد')
      .max(50, 'نام کاربری نباید بیشتر از ۵۰ کاراکتر باشد')
      .regex(
        /^[a-zA-Z0-9_]+$/,
        'نام کاربری فقط می‌تواند شامل حروف، اعداد و _ باشد',
      ),
    email: z
      .string()
      .min(1, 'ایمیل اجباری است')
      .email('فرمت ایمیل نامعتبر است'),
    password: z
      .string()
      .min(8, 'رمز عبور باید حداقل ۸ کاراکتر باشد')
      .max(128, 'رمز عبور نباید بیشتر از ۱۲۸ کاراکتر باشد')
      .regex(/[A-Z]/, 'رمز عبور باید حداقل یک حرف بزرگ داشته باشد')
      .regex(/[a-z]/, 'رمز عبور باید حداقل یک حرف کوچک داشته باشد')
      .regex(/[0-9]/, 'رمز عبور باید حداقل یک عدد داشته باشد')
      .regex(/[^A-Za-z0-9]/, 'رمز عبور باید حداقل یک کاراکتر خاص داشته باشد'),
    confirm_password: z.string(),
    role: z
      .string()
      .min(1, 'انتخاب نوع کاربر اجباری است')
      .refine((val) => ['buyer', 'coder'].includes(val), {
        message: 'نوع کاربر نامعتبر است',
      }),
  })
  .refine((data) => data.password === data.confirm_password, {
    message: 'رمزهای عبور مطابقت ندارند',
    path: ['confirm_password'],
  });

export const forgotPasswordSchema = z.object({
  email: z.string().min(1, 'ایمیل اجباری است').email('فرمت ایمیل نامعتبر است'),
});

export type LoginFormData = z.infer<typeof loginSchema>;
export type PhoneFormData = z.infer<typeof phoneSchema>;
export type OtpFormData = z.infer<typeof otpSchema>;
export type GoogleCallbackData = z.infer<typeof googleCallbackSchema>;
export type SignupFormData = z.infer<typeof signupSchema>;
export type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;
