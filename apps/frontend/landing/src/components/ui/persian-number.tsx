'use client';

import * as React from 'react';
import { cn } from '@/lib/core/utils';
import { toPersianNumbers, formatPersianCurrency } from '@/lib/utils/persian';

export interface PersianNumberProps extends React.HTMLAttributes<HTMLSpanElement> {
  value: number | string;
  type?: 'number' | 'currency' | 'percentage';
  currency?: 'IRR' | 'USD';
  showSign?: boolean;
  precision?: number;
}

const PersianNumber = React.forwardRef<HTMLSpanElement, PersianNumberProps>(
  ({ 
    className, 
    value, 
    type = 'number', 
    currency = 'IRR', 
    showSign = false,
    precision,
    ...props 
  }, ref) => {
    const formatValue = () => {
      const numValue = typeof value === 'string' ? parseFloat(value) : value;
      
      if (isNaN(numValue)) {
        return toPersianNumbers(value.toString());
      }

      switch (type) {
        case 'currency':
          return formatPersianCurrency(numValue, currency);
          
        case 'percentage':
          const percentValue = precision !== undefined 
            ? numValue.toFixed(precision)
            : numValue.toString();
          return `${toPersianNumbers(percentValue)}%`;
          
        case 'number':
        default:
          let formattedNumber: string;
          
          if (precision !== undefined) {
            formattedNumber = numValue.toFixed(precision);
          } else {
            // Use Intl.NumberFormat for proper thousand separators
            formattedNumber = new Intl.NumberFormat('fa-IR').format(numValue);
          }
          
          // Convert to Persian numbers
          formattedNumber = toPersianNumbers(formattedNumber);
          
          // Add sign if requested
          if (showSign && numValue > 0) {
            formattedNumber = `+${formattedNumber}`;
          }
          
          return formattedNumber;
      }
    };

    const getColorClass = () => {
      if (type === 'number' && showSign) {
        const numValue = typeof value === 'string' ? parseFloat(value) : value;
        if (numValue > 0) return 'text-green-600';
        if (numValue < 0) return 'text-red-600';
      }
      return '';
    };

    return (
      <span
        className={cn(
          'font-medium tabular-nums',
          getColorClass(),
          className
        )}
        dir="rtl"
        ref={ref}
        {...props}
      >
        {formatValue()}
      </span>
    );
  }
);
PersianNumber.displayName = 'PersianNumber';

export { PersianNumber };
