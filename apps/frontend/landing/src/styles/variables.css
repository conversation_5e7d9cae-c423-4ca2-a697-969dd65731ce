/* Fluid Typography System */
:root {
  --fluid-min-width: 320;
  --fluid-max-width: 1280;
  --fluid-min-size: 16;
  --fluid-max-size: 20;

  --fluid-screen: 100vw;
  --fluid-bp: calc(
    (var(--fluid-screen) - var(--fluid-min-width) * 1px) /
      (var(--fluid-max-width) - var(--fluid-min-width))
  );

  --f-size: clamp(
    var(--fluid-min-size) * 1px,
    var(--fluid-min-size) * 1px +
      (var(--fluid-max-size) - var(--fluid-min-size)) * var(--fluid-bp),
    var(--fluid-max-size) * 1px
  );

  --type-scale: 1.125; /* Minor Third */
  --min-tap-target-height: 48px;
  --header-height: 4.5rem;
  --sidebar: hsl(0 0% 98%);
  --sidebar-foreground: hsl(240 5.3% 26.1%);
  --sidebar-primary: hsl(240 5.9% 10%);
  --sidebar-primary-foreground: hsl(0 0% 98%);
  --sidebar-accent: hsl(240 4.8% 95.9%);
  --sidebar-accent-foreground: hsl(240 5.9% 10%);
  --sidebar-border: hsl(220 13% 91%);
  --sidebar-ring: hsl(217.2 91.2% 59.8%);
}

@theme {
  --font-yekanBakhFaNum:
    'YekanBakhFaNum', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto',
    'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans',
    'Helvetica Neue', sans-serif;
  --font-mono: var(--font-geist-mono);
  --breakpoint-xs: 30rem;
  --breakpoint-2xl: 100rem;
  --breakpoint-3xl: 120rem;
  --breakpoint-4xl: 140rem;
  --breakpoint-5xl: 160rem;
}

:root {
  --background: oklch(0.08 0.01 140);
  --foreground: oklch(0.95 0.02 140);
  --card: oklch(0.1 0.015 140);
  --card-foreground: oklch(0.9 0.02 140);
  --popover: oklch(0.1 0.015 140);
  --popover-foreground: oklch(0.9 0.02 140);
  --primary: oklch(0.65 0.25 140);
  --primary-foreground: oklch(0.05 0.01 140);
  --secondary: oklch(0.15 0.03 140);
  --secondary-foreground: oklch(0.8 0.02 140);
  --muted: oklch(0.12 0.02 140);
  --muted-foreground: oklch(0.6 0.015 140);
  --accent: oklch(0.4 0.15 140);
  --accent-foreground: oklch(0.95 0.02 140);
  --destructive: oklch(0.5 0.2 20);
  --destructive-foreground: oklch(0.95 0.02 140);
  --border: oklch(0.2 0.05 140);
  --input: oklch(0.15 0.03 140);
  --ring: oklch(0.5 0.2 140);
  --chart-1: oklch(0.6 0.25 140);
  --chart-2: oklch(0.55 0.2 160);
  --chart-3: oklch(0.5 0.15 120);
  --chart-4: oklch(0.45 0.18 180);
  --chart-5: oklch(0.65 0.22 100);
  --radius: 0.5rem;
  --sidebar: oklch(0.1 0.015 140);
  --sidebar-foreground: oklch(0.9 0.02 140);
  --sidebar-primary: oklch(0.65 0.25 140);
  --sidebar-primary-foreground: oklch(0.05 0.01 140);
  --sidebar-accent: oklch(0.15 0.03 140);
  --sidebar-accent-foreground: oklch(0.8 0.02 140);
  --sidebar-border: oklch(0.2 0.05 140);
  --sidebar-ring: oklch(0.5 0.2 140);
}

.light {
  --background: oklch(0.98 0.01 140);
  --foreground: oklch(0.08 0.02 140);
  --card: oklch(0.95 0.015 140);
  --card-foreground: oklch(0.1 0.02 140);
  --popover: oklch(0.95 0.015 140);
  --popover-foreground: oklch(0.1 0.02 140);
  --primary: oklch(0.5 0.25 140);
  --primary-foreground: oklch(0.98 0.01 140);
  --secondary: oklch(0.9 0.03 140);
  --secondary-foreground: oklch(0.2 0.02 140);
  --muted: oklch(0.92 0.02 140);
  --muted-foreground: oklch(0.4 0.015 140);
  --accent: oklch(0.6 0.15 140);
  --accent-foreground: oklch(0.05 0.02 140);
  --destructive: oklch(0.6 0.2 20);
  --destructive-foreground: oklch(0.05 0.02 140);
  --border: oklch(0.85 0.05 140);
  --input: oklch(0.9 0.03 140);
  --ring: oklch(0.5 0.2 140);
  --chart-1: oklch(0.5 0.25 140);
  --chart-2: oklch(0.45 0.2 160);
  --chart-3: oklch(0.4 0.15 120);
  --chart-4: oklch(0.35 0.18 180);
  --chart-5: oklch(0.55 0.22 100);
  --sidebar: oklch(0.95 0.015 140);
  --sidebar-foreground: oklch(0.1 0.02 140);
  --sidebar-primary: oklch(0.5 0.25 140);
  --sidebar-primary-foreground: oklch(0.98 0.01 140);
  --sidebar-accent: oklch(0.9 0.03 140);
  --sidebar-accent-foreground: oklch(0.2 0.02 140);
  --sidebar-border: oklch(0.85 0.05 140);
  --sidebar-ring: oklch(0.5 0.2 140);
}

.dark {
  --background: oklch(0.05 0.01 140);
  --foreground: oklch(0.98 0.02 140);
  --card: oklch(0.08 0.015 140);
  --card-foreground: oklch(0.95 0.02 140);
  --popover: oklch(0.08 0.015 140);
  --popover-foreground: oklch(0.95 0.02 140);
  --primary: oklch(0.7 0.3 140);
  --primary-foreground: oklch(0.03 0.005 140);
  --secondary: oklch(0.12 0.03 140);
  --secondary-foreground: oklch(0.85 0.02 140);
  --muted: oklch(0.1 0.02 140);
  --muted-foreground: oklch(0.65 0.015 140);
  --accent: oklch(0.45 0.2 140);
  --accent-foreground: oklch(0.98 0.02 140);
  --destructive: oklch(0.45 0.18 15);
  --destructive-foreground: oklch(0.9 0.02 140);
  --border: oklch(0.18 0.05 140);
  --input: oklch(0.12 0.03 140);
  --ring: oklch(0.55 0.25 140);
  --chart-1: oklch(0.65 0.3 140);
  --chart-2: oklch(0.6 0.25 160);
  --chart-3: oklch(0.55 0.2 120);
  --chart-4: oklch(0.5 0.22 180);
  --chart-5: oklch(0.7 0.28 100);
  --sidebar: oklch(0.08 0.015 140);
  --sidebar-foreground: oklch(0.95 0.02 140);
  --sidebar-primary: oklch(0.7 0.3 140);
  --sidebar-primary-foreground: oklch(0.03 0.005 140);
  --sidebar-accent: oklch(0.12 0.03 140);
  --sidebar-accent-foreground: oklch(0.85 0.02 140);
  --sidebar-border: oklch(0.18 0.05 140);
  --sidebar-ring: oklch(0.55 0.25 140);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}
