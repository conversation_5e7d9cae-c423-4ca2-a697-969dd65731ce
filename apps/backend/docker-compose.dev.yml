services:
  dolfak-postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: dolfak_db
      POSTGRES_USER: dolfak
      POSTGRES_PASSWORD: dolfak123
    ports:
      - '${POSTGRES_PORT:-5433}:5432'
    volumes:
      - dolfak-postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U dolfak -d dolfak_db']
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - dolfak-network

  dolfak-redis:
    image: redis:7-alpine
    ports:
      - '${REDIS_PORT:-6380}:6379'
    volumes:
      - dolfak-redis_data:/data
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - dolfak-network
    command: redis-server --appendonly yes

  dolfak-minio:
    image: minio/minio:latest
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - '${MINIO_PORT:-9002}:9000'
      - '${MINIO_CONSOLE_PORT:-9003}:9001'
    volumes:
      - dolfak-minio_data:/data
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:9000/minio/health/live']
      interval: 30s
      timeout: 20s
      retries: 3
    restart: unless-stopped
    networks:
      - dolfak-network
    command: server /data --console-address ":9001"

  dolfak-backend:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - '8080:8080'
    depends_on:
      dolfak-postgres:
        condition: service_healthy
      dolfak-redis:
        condition: service_healthy
      dolfak-minio:
        condition: service_healthy
    env_file:
      - .env
    volumes:
      - /app/target
      - dolfak-cargo_cache:/usr/local/cargo/registry
    environment:
      - CARGO_TARGET_DIR=/app/target
    networks:
      - dolfak-network
    develop:
      watch:
        - action: sync
          path: ./src
          target: /app/src
        - action: sync
          path: ./Cargo.toml
          target: /app/Cargo.toml
        - action: sync
          path: ./Cargo.lock
          target: /app/Cargo.lock
        - action: sync
          path: ./diesel.toml
          target: /app/diesel.toml
        - action: sync
          path: ./migrations
          target: /app/migrations
          ignore:
            - target/

networks:
  dolfak-network:
    driver: bridge

volumes:
  dolfak-postgres_data:
    driver: local
  dolfak-redis_data:
    driver: local
  dolfak-minio_data:
    driver: local
  dolfak-cargo_cache:
    driver: local
