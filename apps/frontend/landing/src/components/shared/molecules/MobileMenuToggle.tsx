'use client';

const MobileMenuToggle = () => {
  return (
    <span className="block relative w-[clamp(1.25rem,3vw,1.5rem)] h-[clamp(1.25rem,3vw,1.5rem)]">
      <span className="absolute block w-full h-[2px] bg-green-300 transform transition-all duration-200 ease-in-out top-[15%] peer-has-[:checked]:rotate-45 peer-has-[:checked]:top-1/2 peer-has-[:checked]:-translate-y-1/2"></span>
      <span className="absolute block w-full h-[2px] bg-green-300 top-1/2 -translate-y-1/2 transition-opacity duration-100 opacity-100 peer-has-[:checked]:opacity-0"></span>
      <span className="absolute block w-full h-[2px] bg-green-300 transform transition-all duration-200 ease-in-out top-[85%] peer-has-[:checked]:-rotate-45 peer-has-[:checked]:top-1/2 peer-has-[:checked]:-translate-y-1/2"></span>
    </span>
  );
};

export default MobileMenuToggle;
