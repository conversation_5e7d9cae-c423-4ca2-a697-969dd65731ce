'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/core/utils';
import { DashboardUser } from '@/lib/dashboard/auth';
import { Bell, Menu, Search } from 'lucide-react';

interface DashboardHeaderProps {
  user: DashboardUser;
  onMenuToggle: () => void;
  className?: string;
}

export function DashboardHeader({
  user,
  onMenuToggle,
  className,
}: DashboardHeaderProps) {
  const getRoleLabel = (role: string) => {
    return role === 'buyer' ? 'خریدار' : 'توسعه‌دهنده';
  };

  const getRoleColor = (role: string) => {
    return role === 'buyer'
      ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
  };

  return (
    <header
      className={cn(
        'border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60',
        className,
      )}
    >
      <div className="flex h-16 items-center gap-4 px-4">
        <Button
          variant="ghost"
          size="icon"
          onClick={onMenuToggle}
          className="md:hidden"
        >
          <Menu className="h-7! w-7!" />
        </Button>

        <div className="flex-1 flex items-center gap-4">
          <div className="relative max-w-sm flex-1">
            <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input placeholder="جستجو..." className="pr-10" />
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Button variant="ghost" size="icon" className="relative">
            <Bell className="h-4! w-4!" />
            <span className="absolute -top-1 -right-1 h-5! w-5! bg-destructive rounded-full text-[12px] flex items-center justify-center text-white">
              3
            </span>
          </Button>

          <div className="hidden sm:flex items-center gap-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src={`https://avatar.vercel.sh/${user.username}`} />
              <AvatarFallback className="bg-primary text-primary-foreground text-xs">
                {user.username.slice(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="hidden md:block text-right">
              <div className="flex items-center gap-2">
                <p className="text-sm font-medium text-foreground">
                  {user.username}
                </p>
                <span
                  className={cn(
                    'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                    getRoleColor(user.role),
                  )}
                >
                  {getRoleLabel(user.role)}
                </span>
              </div>
              <p className="text-xs text-muted-foreground">{user.email}</p>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
