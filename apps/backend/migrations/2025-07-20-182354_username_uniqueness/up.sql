-- Your SQL goes here
-- Create a temporary table with unique usernames
CREATE TEMP TABLE username_updates AS
SELECT
    id,
    CASE
        WHEN row_num = 1 THEN username
        ELSE username || '_' || row_num
    END as new_username
FROM (
    SELECT id, username, ROW_NUMBER() OVER (PARTITION BY username ORDER BY created_at) as row_num
    FROM users
) ranked_users
WHERE row_num > 1;

-- Update duplicate usernames
UPDATE users
SET username = username_updates.new_username
FROM username_updates
WHERE users.id = username_updates.id;

-- Add the unique constraint
ALTER TABLE users ADD CONSTRAINT users_username_key UNIQUE (username);

