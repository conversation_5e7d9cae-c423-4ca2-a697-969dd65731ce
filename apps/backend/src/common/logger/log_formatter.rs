use chrono::Utc;
use serde_json::json;

#[allow(dead_code)]
pub struct LogFormatter;

#[allow(dead_code)]
impl LogFormatter {
    pub fn format_structured_log(
        level: &str,
        message: &str,
        context: Option<serde_json::Value>,
    ) -> String {
        let log_entry = json!({
            "timestamp": Utc::now().to_rfc3339(),
            "level": level,
            "message": message,
            "context": context
        });

        log_entry.to_string()
    }

    pub fn format_request_log(
        method: &str,
        path: &str,
        status_code: u16,
        duration_ms: u64,
        user_id: Option<&str>,
    ) -> String {
        let log_entry = json!({
            "timestamp": Utc::now().to_rfc3339(),
            "type": "request",
            "method": method,
            "path": path,
            "status_code": status_code,
            "duration_ms": duration_ms,
            "user_id": user_id
        });

        log_entry.to_string()
    }
}
