use actix_web::web::Bytes;
use anyhow::Result;
use s3::creds::Credentials;
use s3::{Bucket, Region};
use std::env;

#[allow(dead_code)]
pub struct MinIOStorage {
    bucket: Bucket,
    endpoint: String,
}

impl Default for MinIOStorage {
    fn default() -> Self {
        Self::new()
    }
}

impl MinIOStorage {
    pub fn new() -> Self {
        let minio_port = env::var("MINIO_PORT").unwrap_or_else(|_| "9002".to_string());
        let endpoint = env::var("MINIO_ENDPOINT").unwrap_or_else(|_| format!("http://localhost:{minio_port}"));
        let access_key = env::var("MINIO_ACCESS_KEY").unwrap_or_else(|_| "minioadmin".to_string());
        let secret_key = env::var("MINIO_SECRET_KEY").unwrap_or_else(|_| "minioadmin123".to_string());
        let bucket_name = env::var("MINIO_BUCKET_NAME").unwrap_or_else(|_| "dolfak-uploads".to_string());

        let credentials = Credentials::new(Some(&access_key), Some(&secret_key), None, None, None).unwrap();
        let region = Region::Custom { region: "us-east-1".to_owned(), endpoint: endpoint.clone() };
        let bucket = Bucket::new(&bucket_name, region, credentials).unwrap();

        Self {
            bucket: *bucket,
            endpoint,
        }
    }

    #[allow(dead_code)]
    async fn ensure_bucket_exists(&self) -> Result<()> {
        // Try to check if bucket exists by listing objects (head operation)
        match self.bucket.list("".to_string(), None).await {
            Ok(_) => {
                // Bucket exists and is accessible
                Ok(())
            }
            Err(_) => {
                // Bucket doesn't exist or isn't accessible, try to create it
                log::info!("Creating MinIO bucket: {}", self.bucket.name);

                // Get the credentials for bucket creation
                let creds = self.bucket.credentials().await
                    .map_err(|e| anyhow::anyhow!("Failed to get credentials: {}", e))?;

                Bucket::create(
                    &self.bucket.name,
                    self.bucket.region.clone(),
                    creds,
                    s3::BucketConfiguration::default(),
                )
                .await
                .map_err(|e| anyhow::anyhow!("Failed to create bucket: {}", e))?;

                log::info!("Successfully created MinIO bucket: {}", self.bucket.name);
                Ok(())
            }
        }
    }

    #[allow(dead_code)]
    pub async fn upload(&self, filename: &str, file_data: Bytes) -> Result<String> {
        // Ensure bucket exists before uploading
        self.ensure_bucket_exists().await?;

        log::debug!("Uploading file to MinIO: {filename}");

        self.bucket
            .put_object(filename, &file_data)
            .await
            .map_err(|e| anyhow::anyhow!("Failed to upload file to MinIO: {}", e))?;

        let url = format!("{}/{}/{}", self.endpoint, self.bucket.name, filename);
        log::debug!("File uploaded successfully: {url}");
        Ok(url)
    }

    #[allow(dead_code)]
    pub async fn delete(&self, filename: &str) -> Result<()> {
        log::debug!("Deleting file from MinIO: {filename}");

        self.bucket
            .delete_object(filename)
            .await
            .map_err(|e| anyhow::anyhow!("Failed to delete file from MinIO: {}", e))?;

        log::debug!("File deleted successfully: {filename}");
        Ok(())
    }
}
