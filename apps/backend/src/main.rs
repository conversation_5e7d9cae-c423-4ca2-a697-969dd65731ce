use actix_web::{App, HttpServer};
use dotenv::dotenv;
use std::env;

mod schema;
mod main_module;
mod common;
mod modules;

use main_module::MainModule;
use common::logger::LoggerService;
use common::validators::EnvValidator;
use common::middleware::{LoggingMiddleware, ValidationMiddleware};

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    dotenv().ok();

    // Validate environment variables before starting the application
    if let Err(validation_error) = EnvValidator::validate_required_vars() {
        eprintln!("❌ Environment validation failed!");
        eprintln!("Please set the following environment variables in your .env file:");
        for missing_var in &validation_error.missing_vars {
            eprintln!("  - {missing_var}");
        }
        eprintln!("\nExample values can be found in .env.example");
        std::process::exit(1);
    }

    if env::var("RUST_LOG").is_err() {
        env::set_var("RUST_LOG", "actix_web=info,dolfak_backend=debug");
    }
    env_logger::init();

    let main_module = MainModule::new().await;
    let config = &main_module.common_module.config;

    LoggerService::log_startup("✅ All environment variables validated successfully!");
    LoggerService::log_startup(&format!("Starting server at {}:{}", config.server_host, config.server_port));

    let main_module = MainModule::new().await;

    HttpServer::new(move || {
        App::new()
            .wrap(LoggingMiddleware)
            .wrap(ValidationMiddleware)
            .wrap(MainModule::create_cors())
            .configure(|cfg| main_module.configure(cfg))
    })
    .bind((config.server_host.clone(), config.server_port))?
    .run()
    .await
}
