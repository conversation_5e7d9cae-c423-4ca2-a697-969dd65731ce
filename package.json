{"name": "dolfak", "private": true, "scripts": {"backend:dev": "cd apps/backend && cargo run", "landing:dev": "cd apps/frontend/landing && pnpm run dev", "build": "turbo run build", "dev": "turbo run dev", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types", "clean": "turbo run clean", "backend:watch": "cd apps/backend && cargo watch -x run", "landing:build": "cd apps/frontend/landing && pnpm run build", "docker:backend:build": "cd apps/backend && docker build -t dolfak-backend .", "docker:backend:build:no-cache": "cd apps/backend && docker build --no-cache -t dolfak-backend .", "docker:backend:up": "cd apps/backend && docker-compose up -d", "docker:backend:down": "cd apps/backend && docker-compose down", "docker:backend:down:volumes": "cd apps/backend && docker-compose down -v", "docker:backend:dev": "cd apps/backend && docker-compose up", "docker:backend:logs": "cd apps/backend && docker-compose logs -f backend", "docker:backend:logs:all": "cd apps/backend && docker-compose logs -f", "docker:backend:restart": "cd apps/backend && docker-compose restart backend", "docker:backend:restart:all": "cd apps/backend && docker-compose restart", "docker:backend:stop": "cd apps/backend && docker-compose stop", "docker:backend:start": "cd apps/backend && docker-compose start", "docker:backend:ps": "cd apps/backend && docker-compose ps", "docker:backend:exec": "cd apps/backend && docker-compose exec backend sh", "docker:backend:exec:postgres": "cd apps/backend && docker-compose exec postgres psql -U dolfak -d dolfak_db", "docker:backend:exec:redis": "cd apps/backend && docker-compose exec redis redis-cli", "docker:backend:health": "cd apps/backend && docker-compose ps --format table", "docker:backend:clean": "cd apps/backend && docker system prune -f", "docker:backend:clean:all": "cd apps/backend && docker system prune -af --volumes", "docker:backend:rebuild": "cd apps/backend && docker-compose down && docker build --no-cache -t dolfak-backend . && docker-compose up -d", "docker:backend:watch": "cd apps/backend && docker-compose -f docker-compose.dev.yml watch", "docker:backend:watch:up": "cd apps/backend && docker-compose -f docker-compose.dev.yml up --watch", "docker:backend:watch:down": "cd apps/backend && docker-compose -f docker-compose.dev.yml down", "docker:backend:watch:build": "cd apps/backend && docker-compose -f docker-compose.dev.yml build", "docker:backend:watch:rebuild": "cd apps/backend && docker-compose -f docker-compose.dev.yml down && docker-compose -f docker-compose.dev.yml build --no-cache && docker-compose -f docker-compose.dev.yml up --watch", "backend:diesel:migrate": "cd apps/backend && diesel migration run", "backend:diesel:revert": "cd apps/backend && diesel migration revert", "backend:diesel:reset": "cd apps/backend && diesel migration reset", "backend:diesel:run": "cd apps/backend && diesel migration run", "backend:diesel:revert:all": "cd apps/backend && diesel migration", "backend:diesel:reset:all": "cd apps/backend && diesel migration reset", "backend:diesel:run:all": "cd apps/backend && diesel migration run", "backend:diesel:generate:diff-schema": "cd apps/backend && diesel migration generate --diff-schema", "backend:diesel:generate": "cd apps/backend && diesel migration generate", "backend:diesel:generate:all": "cd apps/backend && diesel migration generate", "backend:diesel:setup": "cd apps/backend && diesel setup", "backend:diesel:setup:all": "cd apps/backend && diesel setup", "backend:diesel:print-schema": "cd apps/backend && diesel print-schema > src/schema.rs", "backend:diesel:print-schema:all": "cd apps/backend && diesel print-schema > src/schema.rs", "backend:diesel:print-schema:force": "cd apps/backend && diesel print-schema --force > src/schema.rs"}, "devDependencies": {"prettier": "^3.5.3", "turbo": "2.5.4", "typescript": "5.8.3"}, "packageManager": "pnpm@10.13.1", "engines": {"node": ">=18"}}