use actix_web::web;
use crate::common::CommonModule;
use super::wizard_controller::{Wizard<PERSON><PERSON>roll<PERSON>, submit_wizard, get_all_submissions, get_submission_by_id, update_submission_status};

pub struct WizardModule;

impl WizardModule {
    pub fn configure(cfg: &mut web::ServiceConfig, common_module: &CommonModule) {
        let wizard_controller = WizardController::new(common_module);

        cfg.service(
            web::scope("/wizard")
                .app_data(web::Data::new(wizard_controller))
                .route("/submit", web::post().to(submit_wizard))
                .route("/submissions", web::get().to(get_all_submissions))
                .route("/submissions/{id}", web::get().to(get_submission_by_id))
                .route("/submissions/{id}/status", web::patch().to(update_submission_status))
        );
    }
}
