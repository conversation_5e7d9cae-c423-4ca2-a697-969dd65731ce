import { Button } from '@/components/ui/button';
import { ArrowR<PERSON>, Clock, Rocket, Tag, User } from 'lucide-react';
import Link from 'next/link';
import React from 'react';

interface BlogCardProps {
  title: string;
  excerpt: string;
  author: string;
  readTime: string;
  tags: string[];
  slug: string;
  date: string;
  className?: string;
}

const BlogCard: React.FC<BlogCardProps> = ({
  title,
  excerpt,
  author,
  readTime,
  tags,
  slug,
  date,
  className,
}) => {
  return (
    <article
      className={`bg-card/80 backdrop-blur-sm p-4 sm:p-6 rounded-xl border border-primary/20 shadow-sm transition-all duration-300 hover:border-primary/40 hover:scale-[1.02] hover:shadow-md group ${className || ''}`}
    >
      <div className="space-y-3 sm:space-y-4">
        <div className="flex flex-wrap items-center gap-2 text-xs sm:text-sm text-muted-foreground font-mono">
          <div className="flex items-center gap-1">
            <User className="w-3 h-3 sm:w-4 sm:h-4" />
            <span>{author}</span>
          </div>
          <span className="text-primary/50">•</span>
          <div className="flex items-center gap-1">
            <Clock className="w-3 h-3 sm:w-4 sm:h-4" />
            <span>{readTime}</span>
          </div>
          <span className="text-primary/50">•</span>
          <span>{date}</span>
        </div>

        <h3 className="text-lg sm:text-xl font-bold font-mono text-foreground group-hover:text-primary transition-colors duration-300">
          {title}
        </h3>

        <p className="text-sm sm:text-base text-muted-foreground leading-relaxed line-clamp-3">
          {excerpt}
        </p>

        <div className="flex flex-wrap gap-2">
          {tags.map((tag, index) => (
            <span
              key={index}
              className="inline-flex items-center gap-1 px-2 py-1 bg-primary/10 text-primary text-xs font-mono rounded-md border border-primary/20"
            >
              <Tag className="w-3 h-3" />
              {tag}
            </span>
          ))}
        </div>

        <div className="pt-2">
          <Link href={`/blog/${slug}`}>
            <Button
              variant="outline"
              size="sm"
              className="font-mono text-xs sm:text-sm bg-gradient-to-r from-primary/20 to-accent/20 hover:from-primary/30 hover:to-accent/30 border-primary/30 text-primary hover:border-primary/50 shadow-lg hover:scale-105 transition-all duration-300 group/btn neo-brutal flex items-center gap-2"
            >
              <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5 group-hover/btn:-translate-x-1 transition-transform order-2" />
              <span className="order-1">مطالعه کامل</span>
              <Rocket className="w-4 h-4 sm:w-5 sm:h-5 group-hover/btn:animate-pulse order-0" />
            </Button>
          </Link>
        </div>
      </div>
    </article>
  );
};

export default BlogCard;
