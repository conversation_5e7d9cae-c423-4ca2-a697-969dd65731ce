'use client';
import { But<PERSON> } from '@/components/ui/button';
import { useAuth } from '@/hooks/auth/use-auth';
import { useAuthModal } from '@/hooks/auth/use-auth-modal';
import { ArrowRight, Rocket } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface Props {
popular?: boolean;
}

export const PricingButton = ({ popular }: Props) => {
  const { isAuthenticated } = useAuth();
  const { openModal } = useAuthModal();
  const router = useRouter();

  const handleButtonClick = () => {
    if (isAuthenticated) {
      router.push('/dashboard');
    } else {
      openModal();
    }
  };
  return (
    <Button
      onClick={handleButtonClick}
      className={`w-full font-mono transition-all duration-300 group text-sm sm:text-base py-3 sm:py-4 flex items-center justify-center gap-3 sm:gap-4 neo-brutal hover:scale-105 ${
        popular
          ? 'bg-gradient-to-r from-primary to-accent hover:from-primary/80 hover:to-accent/80 text-primary-foreground shadow-lg hover:shadow-xl'
          : 'bg-gradient-to-r from-primary/20 to-accent/20 hover:from-primary/30 hover:to-accent/30 border border-primary/30 text-primary hover:border-primary/50 shadow-lg'
      }`}
      variant={popular ? 'default' : 'outline'}
    >
      <ArrowRight className="w-5 h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 group-hover:-translate-x-1 transition-transform order-2" />
      <span className="order-1">
        {isAuthenticated ? 'خرید' : 'برای شروع وارد شوید'}
      </span>
      <Rocket className="w-5 h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 group-hover:animate-pulse order-0" />
    </Button>
  );
};
