use std::env;

#[derive(Clone)]
#[allow(dead_code)]
pub struct RedisConfig {
    pub url: String,
    pub max_connections: u32,
}

impl Default for RedisConfig {
    fn default() -> Self {
        Self::new()
    }
}

impl RedisConfig {
    pub fn new() -> Self {
        let redis_port = env::var("REDIS_PORT").unwrap_or_else(|_| "6380".to_string());
        Self {
            url: env::var("REDIS_URL")
                .unwrap_or_else(|_| format!("redis://localhost:{redis_port}")),
            max_connections: env::var("REDIS_MAX_CONNECTIONS")
                .unwrap_or_else(|_| "10".to_string())
                .parse()
                .expect("Invalid max connections number"),
        }
    }
}
