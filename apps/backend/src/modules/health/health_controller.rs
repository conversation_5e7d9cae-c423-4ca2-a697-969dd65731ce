use actix_web::{HttpResponse, Result};
use crate::common::logger::LoggerService;
use super::health_service::{HealthService, HealthStatus, ReadinessStatus, LivenessStatus};

pub struct HealthController;

#[utoipa::path(
    get,
    path = "/api/health",
    responses(
        (status = 200, description = "Health check successful", body = HealthStatus)
    ),
    tag = "health"
)]
pub async fn check() -> Result<HttpResponse> {
    let health_status = HealthService::check_health().await;
    LoggerService::log_health_check("general", health_status.status == "ok", None);
    Ok(HttpResponse::Ok().json(health_status))
}

#[utoipa::path(
    get,
    path = "/api/health/ready",
    responses(
        (status = 200, description = "Service is ready", body = ReadinessStatus),
        (status = 503, description = "Service is not ready", body = ReadinessStatus)
    ),
    tag = "health"
)]
pub async fn ready() -> Result<HttpResponse> {
    let readiness_status = HealthService::check_readiness().await;
    LoggerService::log_health_check("readiness", readiness_status.is_ready, None);

    if readiness_status.is_ready {
        Ok(HttpResponse::Ok().json(readiness_status))
    } else {
        Ok(HttpResponse::ServiceUnavailable().json(readiness_status))
    }
}

#[utoipa::path(
    get,
    path = "/api/health/live",
    responses(
        (status = 200, description = "Service is alive", body = LivenessStatus),
        (status = 503, description = "Service is not alive", body = LivenessStatus)
    ),
    tag = "health"
)]
pub async fn live() -> Result<HttpResponse> {
    let liveness_status = HealthService::check_liveness().await;
    LoggerService::log_health_check("liveness", liveness_status.is_alive, None);

    if liveness_status.is_alive {
        Ok(HttpResponse::Ok().json(liveness_status))
    } else {
        Ok(HttpResponse::ServiceUnavailable().json(liveness_status))
    }
}

impl HealthController {
    pub async fn check() -> Result<HttpResponse> {
        check().await
    }

    pub async fn ready() -> Result<HttpResponse> {
        ready().await
    }

    pub async fn live() -> Result<HttpResponse> {
        live().await
    }
}
