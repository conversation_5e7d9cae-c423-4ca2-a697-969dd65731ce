use chrono::{DateTime, Utc};
use uuid::Uuid;
use serde::{Deserialize, Serialize};
use diesel::prelude::*;
use crate::schema::users;
use super::user_role::UserRole;

#[derive(Debug, Clone, Serialize, Deserialize, Queryable, Selectable, Identifiable)]
#[diesel(table_name = users)]
pub struct UserEntity {
    pub id: Uuid,
    pub email: String,
    pub username: String,
    pub password_hash: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub phone: Option<String>,
    pub phone_verified: Option<bool>,
    pub role: UserRole,
}

#[derive(Insertable)]
#[diesel(table_name = users)]
pub struct NewUserEntity {
    pub id: Uuid,
    pub email: String,
    pub username: String,
    pub password_hash: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub phone: Option<String>,
    pub phone_verified: Option<bool>,
    pub role: UserRole,
}

impl UserEntity {
    pub fn create_new(email: String, username: String, password_hash: String) -> NewUserEntity {
        let now = Utc::now();
        NewUserEntity {
            id: Uuid::new_v4(),
            email,
            username,
            password_hash,
            created_at: now,
            updated_at: now,
            phone: None,
            phone_verified: Some(false),
            role: UserRole::default(),
        }
    }

    pub fn new_with_role(email: String, username: String, password_hash: String, role: UserRole) -> NewUserEntity {
        let now = Utc::now();
        NewUserEntity {
            id: Uuid::new_v4(),
            email,
            username,
            password_hash,
            created_at: now,
            updated_at: now,
            phone: None,
            phone_verified: Some(false),
            role,
        }
    }

    #[allow(dead_code)]
    pub fn update_timestamp(&mut self) {
        self.updated_at = Utc::now();
    }
}
