'use client';

import { DashboardProvider } from '@/components/dashboard/providers/DashboardProvider';
import { LoadingPlaceholder } from '@/components/shared/atoms/LoadingPlaceholder';
import { useAuth } from '@/hooks/auth/use-auth';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function PrivateLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/');
    }
  }, [isAuthenticated, isLoading, router]);

  if (isLoading) {
    return <LoadingPlaceholder />;
  }

  if (!isAuthenticated) {
    return null;
  }

  return <DashboardProvider>{children}</DashboardProvider>;
}
