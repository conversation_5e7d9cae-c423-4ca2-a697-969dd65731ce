'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { getAvailableProjects } from '@/lib/data/mock-projects';
import { PROJECT_CATEGORIES, PROGRAMMING_SKILLS, Project } from '@/lib/types/project';
import { filterProjects, sortProjects } from '@/lib/utils/project';
import { Filter, Search, X } from 'lucide-react';
import { useState } from 'react';
import { ProjectCard } from '../atoms/ProjectCard';

interface CoderProjectsBrowserProps {
  onViewProject?: (projectId: string) => void;
  onApplyToProject?: (projectId: string) => void;
}

export function CoderProjectsBrowser({
  onViewProject,
  onApplyToProject
}: CoderProjectsBrowserProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'budget_high' | 'budget_low' | 'deadline'>('newest');
  const [showFilters, setShowFilters] = useState(false);
  
  // Filter states
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedSkills, setSelectedSkills] = useState<string[]>([]);
  const [selectedComplexity, setSelectedComplexity] = useState<string>('');
  const [budgetRange, setBudgetRange] = useState({ min: '', max: '' });

  const allProjects = getAvailableProjects();

  const getFilteredProjects = () => {
    let projects = allProjects;

    // Apply filters
    const filters: any = {};
    
    if (selectedCategory) filters.category = selectedCategory;
    if (selectedSkills.length > 0) filters.skills = selectedSkills;
    if (selectedComplexity) filters.complexity = selectedComplexity;
    if (searchTerm) filters.search = searchTerm;
    
    if (budgetRange.min || budgetRange.max) {
      filters.budgetRange = {
        min: budgetRange.min ? parseInt(budgetRange.min) * 10 : 0, // Convert Toman to Rial
        max: budgetRange.max ? parseInt(budgetRange.max) * 10 : Infinity
      };
    }

    projects = filterProjects(projects, filters);
    return sortProjects(projects, sortBy);
  };

  const clearFilters = () => {
    setSelectedCategory('');
    setSelectedSkills([]);
    setSelectedComplexity('');
    setBudgetRange({ min: '', max: '' });
    setSearchTerm('');
  };

  const hasActiveFilters = selectedCategory || selectedSkills.length > 0 || selectedComplexity || budgetRange.min || budgetRange.max || searchTerm;

  const filteredProjects = getFilteredProjects();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">پروژه‌های موجود</h2>
        <Button
          variant="outline"
          onClick={() => setShowFilters(!showFilters)}
        >
          <Filter className="h-4 w-4 ml-2" />
          فیلترها
        </Button>
      </div>

      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="جستجو در پروژه‌ها..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pr-10"
          />
        </div>
        <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder="مرتب‌سازی" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="newest">جدیدترین</SelectItem>
            <SelectItem value="oldest">قدیمی‌ترین</SelectItem>
            <SelectItem value="budget_high">بودجه (زیاد به کم)</SelectItem>
            <SelectItem value="budget_low">بودجه (کم به زیاد)</SelectItem>
            <SelectItem value="deadline">نزدیک‌ترین ددلاین</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {showFilters && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">فیلترها</CardTitle>
              {hasActiveFilters && (
                <Button variant="ghost" size="sm" onClick={clearFilters}>
                  <X className="h-4 w-4 ml-1" />
                  پاک کردن همه
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              {/* Category Filter */}
              <div className="space-y-2">
                <Label>دسته‌بندی</Label>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="انتخاب دسته‌بندی" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">همه دسته‌ها</SelectItem>
                    {PROJECT_CATEGORIES.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Complexity Filter */}
              <div className="space-y-2">
                <Label>سطح پیچیدگی</Label>
                <Select value={selectedComplexity} onValueChange={setSelectedComplexity}>
                  <SelectTrigger>
                    <SelectValue placeholder="انتخاب سطح" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">همه سطوح</SelectItem>
                    <SelectItem value="beginner">مبتدی</SelectItem>
                    <SelectItem value="intermediate">متوسط</SelectItem>
                    <SelectItem value="expert">پیشرفته</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Budget Range */}
              <div className="space-y-2">
                <Label>بودجه (تومان)</Label>
                <div className="flex gap-2">
                  <Input
                    placeholder="حداقل"
                    value={budgetRange.min}
                    onChange={(e) => setBudgetRange(prev => ({ ...prev, min: e.target.value }))}
                    type="number"
                  />
                  <Input
                    placeholder="حداکثر"
                    value={budgetRange.max}
                    onChange={(e) => setBudgetRange(prev => ({ ...prev, max: e.target.value }))}
                    type="number"
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Skills Filter */}
            <div className="space-y-3">
              <Label>مهارت‌های مورد نیاز</Label>
              <div className="grid gap-2 grid-cols-2 md:grid-cols-4 lg:grid-cols-6">
                {PROGRAMMING_SKILLS.slice(0, 24).map((skill) => (
                  <div key={skill} className="flex items-center space-x-2">
                    <Checkbox
                      id={skill}
                      checked={selectedSkills.includes(skill)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedSkills(prev => [...prev, skill]);
                        } else {
                          setSelectedSkills(prev => prev.filter(s => s !== skill));
                        }
                      }}
                    />
                    <Label htmlFor={skill} className="text-sm">
                      {skill}
                    </Label>
                  </div>
                ))}
              </div>
              {selectedSkills.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {selectedSkills.map((skill) => (
                    <Badge key={skill} variant="secondary" className="text-xs">
                      {skill}
                      <button
                        onClick={() => setSelectedSkills(prev => prev.filter(s => s !== skill))}
                        className="ml-1 hover:text-destructive"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      <div className="flex items-center justify-between">
        <p className="text-muted-foreground">
          {filteredProjects.length} پروژه یافت شد
        </p>
        {hasActiveFilters && (
          <Badge variant="secondary">
            فیلتر فعال
          </Badge>
        )}
      </div>

      {filteredProjects.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-muted-foreground mb-4">
            هیچ پروژه‌ای با این فیلترها یافت نشد
          </div>
          <Button variant="outline" onClick={clearFilters}>
            پاک کردن فیلترها
          </Button>
        </div>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredProjects.map((project) => (
            <ProjectCard
              key={project.id}
              project={project}
              variant="coder"
              onViewDetails={onViewProject}
              onApply={onApplyToProject}
            />
          ))}
        </div>
      )}
    </div>
  );
}
