/* YekanBakhFaNum Font Faces */
@font-face {
  font-family: 'YekanBakhFaNum';
  src: url('/fonts/YekanBakhFaNum-Thin.woff2') format('woff2');
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'YekanBakhFaNum';
  src: url('/fonts/YekanBakhFaNum-Light.woff2') format('woff2');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'YekanBakhFaNum';
  src: url('/fonts/YekanBakhFaNum-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'YekanBakhFaNum';
  src: url('/fonts/YekanBakhFaNum-SemiBold.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'YekanBakhFaNum';
  src: url('/fonts/YekanBakhFaNum-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'YekanBakhFaNum';
  src: url('/fonts/YekanBakhFaNum-ExtraBold.woff2') format('woff2');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'YekanBakhFaNum';
  src: url('/fonts/YekanBakhFaNum-Black.woff2') format('woff2');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'YekanBakhFaNum';
  src: url('/fonts/YekanBakhFaNum-ExtraBlack.woff2') format('woff2');
  font-weight: 950;
  font-style: normal;
  font-display: swap;
}
