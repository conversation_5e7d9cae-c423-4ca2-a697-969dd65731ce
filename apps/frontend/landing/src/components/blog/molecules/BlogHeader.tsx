import React from 'react';

interface BlogHeaderProps {
  className?: string;
}

const BlogHeader: React.FC<BlogHeaderProps> = ({ className }) => {
  return (
    <section
      className={`py-8 sm:py-6 lg:py-10 bg-gradient-to-br from-background via-card to-secondary relative overflow-hidden ${className || ''}`}
    >
      {/* Geeky decorative elements */}
      <div className="absolute -top-40 -right-40 w-[clamp(12rem,25vw,20rem)] h-[clamp(12rem,25vw,20rem)] rounded-full bg-primary/10 mix-blend-screen filter blur-3xl opacity-70 neon-glow"></div>
      <div className="absolute -bottom-20 left-20 w-[clamp(10rem,20vw,16rem)] h-[clamp(10rem,20vw,16rem)] rounded-full bg-accent/10 mix-blend-screen filter blur-3xl opacity-60 neon-glow"></div>

      {/* Binary floating elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 right-1/4 text-primary/5 font-mono text-sm rotate-45 float">
          01100010
        </div>
        <div
          className="absolute top-1/3 left-1/3 text-accent/5 font-mono text-sm -rotate-45 float"
          style={{ animationDelay: '2s' }}
        >
          01101100
        </div>
        <div
          className="absolute bottom-1/4 right-1/2 text-primary/5 font-mono text-sm rotate-12 float"
          style={{ animationDelay: '4s' }}
        >
          01101111
        </div>
        <div
          className="absolute bottom-1/3 left-1/4 text-accent/5 font-mono text-sm -rotate-12 float"
          style={{ animationDelay: '6s' }}
        >
          01100111
        </div>
      </div>

      <div className="container relative z-10 mx-auto px-4 text-center">
        <div className="inline-block mb-4 sm:mb-6 px-4 sm:px-6 py-2 sm:py-3 bg-card/80 text-primary rounded-lg text-sm sm:text-base font-mono border border-primary/20 neon-glow">
          <span className="text-accent">const</span>{' '}
          {'blog = new Knowledge().share();'}
        </div>

        <h1 className="text-md sm:text-xl md:text-2xl lg:text-3xl font-bold mb-2 sm:mb-3 font-mono">
          <span className="text-foreground">بلاگ تخصصی دلفک</span>
        </h1>

        <div className="max-w-3xl mx-auto mb-4 sm:mb-6 text-base sm:text-lg leading-relaxed font-mono bg-card/30 border border-primary/10 rounded-lg p-4 sm:p-6">
          <div className="text-accent mb-3">
            // مرجع کامل راهکارهای برنامه‌نویسی و نکات فنی
          </div>
          <div className="text-muted-foreground">
            در اینجا آخرین مطالب تخصصی، راهنماهای جامع و نکات کاربردی در زمینه
            توسعه نرم‌افزار، برنامه‌نویسی و فناوری‌های مدرن را با شما به اشتراک
            می‌گذاریم.
          </div>
        </div>

        <div className="inline-flex items-center gap-2 text-sm sm:text-base font-mono text-primary">
          <span className="w-2 h-2 bg-primary rounded-full animate-pulse"></span>
          <span>آماده برای یادگیری؟</span>
          <span className="cursor-blink">_</span>
        </div>
      </div>
    </section>
  );
};

export default BlogHeader;
