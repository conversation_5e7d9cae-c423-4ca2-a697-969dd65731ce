import { z } from 'zod';

export const personalInfoValidationSchema = z.object({
  fullName: z
    .string()
    .min(1, 'لطفاً نام و نام خانوادگی خود را وارد کنید')
    .min(2, 'نام و نام خانوادگی باید حداقل ۲ کاراکتر باشد')
    .max(100, 'نام و نام خانوادگی نباید بیشتر از ۱۰۰ کاراکتر باشد'),
  email: z
    .string()
    .min(1, 'لطفاً ایمیل خود را وارد کنید')
    .email('لطفاً ایمیل معتبر وارد کنید'),
  phone: z
    .string()
    .min(1, 'لطفاً شماره تماس خود را وارد کنید')
    .min(11, 'شماره تلفن باید ۱۱ رقم باشد')
    .max(11, 'شماره تلفن باید ۱۱ رقم باشد')
    .regex(
      /^09[0-9]{9}$/,
      'لطفاً شماره تماس معتبر وارد کنید (مثال: 09123456789)',
    ),
});

export const personalInfoSchema = z.object({
  fullName: z.string().optional(),
  email: z.string().optional(),
  phone: z.string().optional(),
});

export const projectTypeValidationSchema = z.object({
  projectType: z.string().min(1, 'لطفاً نوع پروژه خود را انتخاب کنید'),
  description: z
    .string()
    .min(20, 'توضیحات باید حداقل ۲۰ کاراکتر باشد')
    .max(1000, 'توضیحات نباید بیشتر از ۱۰۰۰ کاراکتر باشد'),
});

export const projectTypeSchema = z.object({
  projectType: z.string().optional(),
  description: z.string().optional(),
});

export const wizardSubmissionSchema = z.object({
  full_name: z
    .string()
    .min(2, 'نام و نام خانوادگی باید حداقل ۲ کاراکتر باشد')
    .max(100, 'نام و نام خانوادگی نباید بیشتر از ۱۰۰ کاراکتر باشد'),
  email: z.string().min(1, 'ایمیل اجباری است').email('فرمت ایمیل نامعتبر است'),
  phone: z
    .string()
    .min(11, 'شماره تلفن باید ۱۱ رقم باشد')
    .max(11, 'شماره تلفن باید ۱۱ رقم باشد')
    .regex(/^09[0-9]{9}$/, 'شماره تلفن معتبر وارد کنید (مثال: 09123456789)'),
  project_type: z.enum(['webapp', 'mobile', 'startup'], {
    message: 'لطفاً نوع پروژه خود را انتخاب کنید',
  }),
  description: z
    .string()
    .min(20, 'توضیحات باید حداقل ۲۰ کاراکتر باشد')
    .max(1000, 'توضیحات نباید بیشتر از ۱۰۰۰ کاراکتر باشد'),
});

export type PersonalInfoData = z.infer<typeof personalInfoSchema>;
export type ProjectTypeData = z.infer<typeof projectTypeSchema>;
export type WizardSubmissionData = z.infer<typeof wizardSubmissionSchema>;
