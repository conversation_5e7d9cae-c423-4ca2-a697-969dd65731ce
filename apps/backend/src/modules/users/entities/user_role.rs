use serde::{Deserialize, Serialize};
use diesel::{FromSqlRow, AsExpression};
use diesel::serialize::{Output, ToSql};
use diesel::deserialize::{FromSql, Result as DeserializeResult};
use diesel::sql_types::Text;
use std::io::Write;
use utoipa::ToSchema;

#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize, ToSchema, FromSqlRow, AsExpression)]
#[diesel(sql_type = Text)]
#[serde(rename_all = "lowercase")]
#[derive(Default)]
pub enum UserRole {
    #[serde(rename = "buyer")]
    #[default]
    Buyer,
    #[serde(rename = "coder")]
    Coder,
}


impl std::fmt::Display for UserRole {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            UserRole::Buyer => write!(f, "buyer"),
            UserRole::Coder => write!(f, "coder"),
        }
    }
}

impl From<String> for UserRole {
    fn from(s: String) -> Self {
        match s.as_str() {
            "buyer" => UserRole::Buyer,
            "coder" => UserRole::Coder,
            _ => UserRole::Buyer, // Default to buyer for invalid values
        }
    }
}

impl From<&str> for UserRole {
    fn from(s: &str) -> Self {
        match s {
            "buyer" => UserRole::Buyer,
            "coder" => UserRole::Coder,
            _ => UserRole::Buyer, // Default to buyer for invalid values
        }
    }
}

impl ToSql<Text, diesel::pg::Pg> for UserRole {
    fn to_sql<'b>(&'b self, out: &mut Output<'b, '_, diesel::pg::Pg>) -> diesel::serialize::Result {
        match *self {
            UserRole::Buyer => out.write_all(b"buyer")?,
            UserRole::Coder => out.write_all(b"coder")?,
        }
        Ok(diesel::serialize::IsNull::No)
    }
}

impl FromSql<Text, diesel::pg::Pg> for UserRole {
    fn from_sql(bytes: diesel::pg::PgValue) -> DeserializeResult<Self> {
        match std::str::from_utf8(bytes.as_bytes())? {
            "buyer" => Ok(UserRole::Buyer),
            "coder" => Ok(UserRole::Coder),
            _ => Err("Unrecognized enum variant".into()),
        }
    }
}
