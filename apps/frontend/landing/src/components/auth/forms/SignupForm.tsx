'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useAuth } from '@/hooks/auth/use-auth';
import { register } from '@/lib/auth/auth';
import { SignupFormData, signupSchema } from '@/lib/validations/auth';
import { zodResolver } from '@hookform/resolvers/zod';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { RoleSelector } from '../atoms/role-selector';

interface SignupFormProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export function SignupForm({ onSuccess, onError }: SignupFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { login: authLogin } = useAuth();

  const form = useForm<SignupFormData>({
    resolver: zod<PERSON><PERSON>olver(signupSchema),
    defaultValues: {
      username: '',
      email: '',
      password: '',
      confirm_password: '',
      role: '',
    },
  });

  const onSubmit = async (values: SignupFormData) => {
    setIsLoading(true);
    try {
      const response = await register(values);
      authLogin(response.access_token, response.refresh_token, response.user);
      onSuccess?.();
    } catch (error: any) {
      // Error is handled by axios interceptor with toast
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form
        dir="rtl"
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-4"
        noValidate
      >
        <FormField
          control={form.control}
          name="username"
          render={({ field }) => (
            <FormItem>
              <FormLabel>نام کاربری</FormLabel>
              <FormControl>
                <Input
                  type="text"
                  placeholder="نام کاربری خود را وارد کنید"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>ایمیل</FormLabel>
              <FormControl>
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="role"
          render={({ field, fieldState }) => (
            <FormItem>
              <div className="flex items-center gap-4">
                <FormLabel className="mb-2">
                  خریدار هستید یا برنامه نویس؟
                </FormLabel>
                <FormControl>
                  <RoleSelector
                    value={field.value}
                    onValueChange={field.onChange}
                    showLabel={false}
                    error={!!fieldState.error}
                  />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>رمز عبور</FormLabel>
              <FormControl>
                <Input
                  type="password"
                  placeholder="رمز عبور خود را وارد کنید"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="confirm_password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>تکرار رمز عبور</FormLabel>
              <FormControl>
                <Input
                  type="password"
                  placeholder="رمز عبور خود را دوباره وارد کنید"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit" className="w-full" disabled={isLoading}>
          {isLoading ? 'در حال ثبت‌نام...' : 'ثبت‌نام'}
        </Button>
      </form>
    </Form>
  );
}
