use diesel::prelude::*;
use diesel_async::RunQueryDsl;
use uuid::Uuid;
use chrono::Utc;
use crate::common::{CommonModule, database::connection::DbPool, errors::AppError};
use crate::modules::shared::otp::{PhoneOtpEntity, NewPhoneOtpEntity, UpdatePhoneOtpEntity};
use crate::schema::phone_otp;

pub struct PhoneOtpRepository {
    db: DbPool,
}

impl PhoneOtpRepository {
    pub fn new(common_module: &CommonModule) -> Self {
        Self {
            db: common_module.database.pool.clone(),
        }
    }

    pub async fn create(&self, new_otp: &NewPhoneOtpEntity) -> Result<PhoneOtpEntity, AppError> {
        let mut conn = self.db.get().await?;

        let otp = diesel::insert_into(phone_otp::table)
            .values(new_otp)
            .returning(PhoneOtpEntity::as_returning())
            .get_result(&mut conn)
            .await
            .map_err(|e| AppError::InternalServer(e.to_string()))?;

        Ok(otp)
    }

    #[allow(dead_code)]
    pub async fn find_latest_by_phone(&self, phone_number: &str) -> Result<Option<PhoneOtpEntity>, AppError> {
        let mut conn = self.db.get().await?;

        let otp = phone_otp::table
            .filter(phone_otp::phone.eq(phone_number))
            .order(phone_otp::created_at.desc())
            .first::<PhoneOtpEntity>(&mut conn)
            .await
            .optional()
            .map_err(|e| AppError::InternalServer(e.to_string()))?;

        Ok(otp)
    }

    pub async fn find_valid_by_phone_and_code(&self, phone_number: &str, code: &str) -> Result<Option<PhoneOtpEntity>, AppError> {
        let mut conn = self.db.get().await?;
        let now = Utc::now();

        let otp = phone_otp::table
            .filter(phone_otp::phone.eq(phone_number))
            .filter(phone_otp::otp_code.eq(code))
            .filter(phone_otp::expires_at.gt(now))
            .filter(phone_otp::verified.eq(false).or(phone_otp::verified.is_null()))
            .first::<PhoneOtpEntity>(&mut conn)
            .await
            .optional()
            .map_err(|e| AppError::InternalServer(e.to_string()))?;

        Ok(otp)
    }

    pub async fn update(&self, otp_id: Uuid, update_data: &UpdatePhoneOtpEntity) -> Result<PhoneOtpEntity, AppError> {
        let mut conn = self.db.get().await?;

        let updated_otp = diesel::update(phone_otp::table.find(otp_id))
            .set(update_data)
            .returning(PhoneOtpEntity::as_returning())
            .get_result(&mut conn)
            .await
            .map_err(|e| AppError::InternalServer(e.to_string()))?;

        Ok(updated_otp)
    }

    pub async fn increment_attempts(&self, otp_id: Uuid) -> Result<PhoneOtpEntity, AppError> {
        let mut conn = self.db.get().await?;
        let now = Utc::now();

        let updated_otp = diesel::update(phone_otp::table.find(otp_id))
            .set((
                phone_otp::attempts.eq(phone_otp::attempts + 1),
                phone_otp::updated_at.eq(now),
            ))
            .returning(PhoneOtpEntity::as_returning())
            .get_result(&mut conn)
            .await
            .map_err(|e| AppError::InternalServer(e.to_string()))?;

        Ok(updated_otp)
    }

    pub async fn mark_as_verified(&self, otp_id: Uuid) -> Result<PhoneOtpEntity, AppError> {
        let now = Utc::now();

        let update_data = UpdatePhoneOtpEntity {
            verified: Some(true),
            updated_at: Some(now),
            attempts: None,
        };

        self.update(otp_id, &update_data).await
    }

    #[allow(dead_code)]
    pub async fn cleanup_expired(&self) -> Result<usize, AppError> {
        let mut conn = self.db.get().await?;
        let now = Utc::now();

        let deleted = diesel::delete(phone_otp::table)
            .filter(phone_otp::expires_at.lt(now))
            .execute(&mut conn)
            .await
            .map_err(|e| AppError::InternalServer(e.to_string()))?;

        Ok(deleted)
    }
}
