use actix_web::web::Bytes;
use anyhow::Result;
use uuid::Uuid;
use super::{file_validator::FileValidator, local_storage::LocalStorage, minio_storage::MinIOStorage};

#[allow(dead_code)]
pub struct UploadService {
    local_storage: LocalStorage,
    minio_storage: MinIOStorage,
}

impl Default for UploadService {
    fn default() -> Self {
        Self::new()
    }
}

impl UploadService {
    pub fn new() -> Self {
        Self {
            local_storage: LocalStorage::new(),
            minio_storage: MinIOStorage::new(),
        }
    }

    #[allow(dead_code)]
    pub async fn upload_file(
        &self,
        file_data: Bytes,
        filename: &str,
        content_type: &str,
        use_minio: bool,
    ) -> Result<String> {
        FileValidator::validate_file(&file_data, content_type)?;

        let file_id = Uuid::new_v4().to_string();
        let file_extension = std::path::Path::new(filename)
            .extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("");
        let new_filename = format!("{file_id}.{file_extension}");

        if use_minio {
            self.minio_storage.upload(&new_filename, file_data).await
        } else {
            self.local_storage.upload(&new_filename, file_data).await
        }
    }

    #[allow(dead_code)]
    pub async fn delete_file(&self, filename: &str, use_minio: bool) -> Result<()> {
        if use_minio {
            self.minio_storage.delete(filename).await
        } else {
            self.local_storage.delete(filename).await
        }
    }
}
