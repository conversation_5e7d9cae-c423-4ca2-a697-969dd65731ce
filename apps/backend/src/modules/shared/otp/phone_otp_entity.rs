use diesel::prelude::*;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use crate::schema::phone_otp;

#[derive(Queryable, Selectable, Serialize, Deserialize, Debug, Clone)]
#[diesel(table_name = phone_otp)]
#[diesel(check_for_backend(diesel::pg::Pg))]
pub struct PhoneOtpEntity {
    pub id: Uuid,
    pub phone: String,
    pub otp_code: String,
    pub verified: Option<bool>,
    pub expires_at: DateTime<Utc>,
    pub attempts: Option<i32>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Insertable, Debug)]
#[diesel(table_name = phone_otp)]
pub struct NewPhoneOtpEntity {
    pub phone: String,
    pub otp_code: String,
    pub expires_at: DateTime<Utc>,
    pub attempts: i32,
}

impl NewPhoneOtpEntity {
    pub fn new(phone: String, otp_code: String, expires_at: DateTime<Utc>) -> Self {
        Self {
            phone,
            otp_code,
            expires_at,
            attempts: 0,
        }
    }
}

#[derive(AsChangeset, Debug)]
#[diesel(table_name = phone_otp)]
pub struct UpdatePhoneOtpEntity {
    pub verified: Option<bool>,
    pub attempts: Option<i32>,
    pub updated_at: Option<DateTime<Utc>>,
}
