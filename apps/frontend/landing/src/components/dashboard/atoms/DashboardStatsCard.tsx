'use client';

import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/core/utils';

interface DashboardStatsCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon?: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  className?: string;
}

export function DashboardStatsCard({
  title,
  value,
  description,
  icon,
  trend,
  className,
}: DashboardStatsCardProps) {
  return (
    <div
      className={cn(
        'rounded-lg border bg-card p-6 shadow-sm transition-all hover:shadow-md',
        className,
      )}
    >
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
          <p className="text-2xl font-bold">{value}</p>
          {description && (
            <p className="text-sm text-muted-foreground">{description}</p>
          )}
        </div>
        {icon && (
          <div className="rounded-full bg-primary/10 p-2 text-primary">
            {icon}
          </div>
        )}
      </div>
      {trend && (
        <div className="mt-4 flex items-center gap-2">
          <Badge
            variant={trend.isPositive ? 'default' : 'destructive'}
            className="text-xs"
          >
            {trend.isPositive ? '↗' : '↘'} {Math.abs(trend.value)}%
          </Badge>
          <span className="text-xs text-muted-foreground">نسبت به ماه قبل</span>
        </div>
      )}
    </div>
  );
}
