import { BuyerSection, CoderSection } from '@/lib/store/dashboard';

export interface MenuItem {
  label: string;
  icon: string;
  section: BuyerSection | CoderSection;
  href?: string;
  roles: Array<'buyer' | 'coder'>;
  badge?: string;
  children?: MenuItem[];
}

export const buyerMenuItems: MenuItem[] = [
  {
    label: 'داشبورد',
    icon: 'LayoutDashboard',
    section: 'overview',
    roles: ['buyer'],
  },
  {
    label: 'پروژه‌های من',
    icon: 'FolderOpen',
    section: 'projects',
    roles: ['buyer'],
  },
  {
    label: 'جستجوی توسعه‌دهندگان',
    icon: 'Search',
    section: 'browse-developers',
    roles: ['buyer'],
  },
  {
    label: 'درخواست‌ها',
    icon: 'MessageSquare',
    section: 'requests',
    roles: ['buyer'],
  },
  {
    label: 'پیام‌ها',
    icon: 'Mail',
    section: 'messages',
    roles: ['buyer'],
    badge: '3',
  },
  {
    label: 'کیف پول',
    icon: 'Wallet',
    section: 'wallet',
    roles: ['buyer'],
  },
  {
    label: 'تنظیمات',
    icon: 'Settings',
    section: 'settings',
    roles: ['buyer'],
  },
];

export const coderMenuItems: MenuItem[] = [
  {
    label: 'داشبورد',
    icon: 'LayoutDashboard',
    section: 'overview',
    roles: ['coder'],
  },
  {
    label: 'پروژه‌های فعال',
    icon: 'Code',
    section: 'active-projects',
    roles: ['coder'],
  },
  {
    label: 'درخواست‌های جدید',
    icon: 'Bell',
    section: 'new-requests',
    roles: ['coder'],
    badge: '5',
  },
  {
    label: 'نمونه کارها',
    icon: 'Briefcase',
    section: 'portfolio',
    roles: ['coder'],
  },
  {
    label: 'پیام‌ها',
    icon: 'Mail',
    section: 'messages',
    roles: ['coder'],
    badge: '2',
  },
  {
    label: 'درآمد',
    icon: 'TrendingUp',
    section: 'earnings',
    roles: ['coder'],
  },
  {
    label: 'تنظیمات',
    icon: 'Settings',
    section: 'settings',
    roles: ['coder'],
  },
];

export const getMenuItemsForRole = (role: 'buyer' | 'coder'): MenuItem[] => {
  return role === 'buyer' ? buyerMenuItems : coderMenuItems;
};
