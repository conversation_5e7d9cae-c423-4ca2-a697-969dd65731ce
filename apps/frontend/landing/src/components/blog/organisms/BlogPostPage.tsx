import { Button } from '@/components/ui/button';
import { ArrowLeft, Calendar, Clock, Rocket, Tag, User } from 'lucide-react';
import Link from 'next/link';
import React from 'react';

interface BlogPost {
  id: string;
  title: string;
  content: string;
  author: string;
  readTime: string;
  tags: string[];
  date: string;
}

interface BlogPostPageProps {
  post: BlogPost;
  className?: string;
}

const BlogPostPage: React.FC<BlogPostPageProps> = ({ post, className }) => {
  const formatContent = (content: string) => {
    const paragraphs = content.split('\n\n');

    return paragraphs.map((paragraph, index) => {
      const lines = paragraph.split('\n');

      // Check if it's a list
      if (
        lines.every((line) => line.trim().startsWith('-') || line.trim() === '')
      ) {
        return (
          <ul key={index} className="mb-6 pr-6 space-y-2">
            {lines
              .filter((line) => line.trim().startsWith('-'))
              .map((line, lineIndex) => (
                <li key={lineIndex} className="text-muted-foreground">
                  {line.replace(/^-\s*/, '')}
                </li>
              ))}
          </ul>
        );
      }

      // Check if it's a numbered list
      if (lines.every((line) => line.match(/^\d+\./) || line.trim() === '')) {
        return (
          <ol key={index} className="mb-6 pr-6 space-y-2 list-decimal">
            {lines
              .filter((line) => line.match(/^\d+\./))
              .map((line, lineIndex) => (
                <li key={lineIndex} className="text-muted-foreground">
                  {line.replace(/^\d+\.\s*/, '')}
                </li>
              ))}
          </ol>
        );
      }

      // Check for section headers (lines that end with colon and are relatively short)
      if (paragraph.length < 100 && paragraph.endsWith(':')) {
        return (
          <h3
            key={index}
            className="text-lg sm:text-xl md:text-2xl font-bold mb-4 font-mono text-primary mt-6"
          >
            {paragraph.replace(':', '')}
          </h3>
        );
      }

      // Check for subsection headers (lines that are short and don't end with period)
      if (
        paragraph.length < 80 &&
        !paragraph.endsWith('.') &&
        !paragraph.includes(':')
      ) {
        return (
          <h4
            key={index}
            className="text-base sm:text-lg font-bold mb-3 font-mono text-accent mt-4"
          >
            {paragraph}
          </h4>
        );
      }

      // Regular paragraph
      return (
        <p
          key={index}
          className="mb-4 text-muted-foreground leading-relaxed text-justify"
        >
          {paragraph}
        </p>
      );
    });
  };

  return (
    <div
      className={`min-h-screen bg-gradient-to-br from-background via-card to-secondary ${className || ''}`}
    >
      {/* Header Section */}
      <section className="py-8 sm:py-12 lg:py-16 bg-gradient-to-br from-background via-card to-secondary relative overflow-hidden">
        {/* Geeky decorative elements */}
        <div className="absolute -top-40 -right-40 w-[clamp(12rem,25vw,20rem)] h-[clamp(12rem,25vw,20rem)] rounded-full bg-primary/10 mix-blend-screen filter blur-3xl opacity-70 neon-glow"></div>
        <div className="absolute -bottom-20 left-20 w-[clamp(10rem,20vw,16rem)] h-[clamp(10rem,20vw,16rem)] rounded-full bg-accent/10 mix-blend-screen filter blur-3xl opacity-60 neon-glow"></div>

        {/* Binary floating elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 right-1/4 text-primary/5 font-mono text-sm rotate-45 float">
            01110010
          </div>
          <div
            className="absolute top-1/3 left-1/3 text-accent/5 font-mono text-sm -rotate-45 float"
            style={{ animationDelay: '2s' }}
          >
            01100101
          </div>
        </div>

        <div className="container relative z-10 mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {/* Back button */}
            <Link href="/blog">
              <Button
                variant="outline"
                size="sm"
                className="mb-6 font-mono bg-gradient-to-r from-primary/20 to-accent/20 hover:from-primary/30 hover:to-accent/30 border-primary/30 text-primary hover:border-primary/50 shadow-lg hover:scale-105 transition-all duration-300 group neo-brutal flex items-center gap-2"
              >
                <ArrowLeft className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform order-2" />
                <span className="order-1">بازگشت به بلاگ</span>
                <Rocket className="w-4 h-4 group-hover:animate-pulse order-0" />
              </Button>
            </Link>

            {/* Post metadata */}
            <div className="flex flex-wrap items-center gap-3 sm:gap-4 mb-4 sm:mb-6 text-xs sm:text-sm text-muted-foreground font-mono">
              <div className="flex items-center gap-1">
                <User className="w-4 h-4" />
                <span>{post.author}</span>
              </div>
              <span className="text-primary/50">•</span>
              <div className="flex items-center gap-1">
                <Calendar className="w-4 h-4" />
                <span>{post.date}</span>
              </div>
              <span className="text-primary/50">•</span>
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                <span>{post.readTime}</span>
              </div>
            </div>

            {/* Title */}
            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 sm:mb-6 font-mono text-foreground">
              {post.title}
            </h1>

            {/* Tags */}
            <div className="flex flex-wrap gap-2 mb-6 sm:mb-8">
              {post.tags.map((tag, index) => (
                <span
                  key={index}
                  className="inline-flex items-center gap-1 px-3 py-1 bg-primary/10 text-primary text-xs sm:text-sm font-mono rounded-md border border-primary/20"
                >
                  <Tag className="w-3 h-3" />
                  {tag}
                </span>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Content Section */}
      <section className="py-8 sm:py-12 lg:py-16 relative">
        {/* Code-like background elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none opacity-20">
          <div className="absolute top-20 right-10 text-primary/10 font-mono text-xs rotate-12 float">
            {'// Reading mode activated'}
          </div>
          <div
            className="absolute top-1/2 left-10 text-accent/10 font-mono text-xs -rotate-12 float"
            style={{ animationDelay: '3s' }}
          >
            {'const knowledge = true;'}
          </div>
          <div
            className="absolute bottom-20 right-1/3 text-primary/10 font-mono text-xs rotate-6 float"
            style={{ animationDelay: '5s' }}
          >
            {'console.log("learning...");'}
          </div>
        </div>

        <div className="container relative z-10 mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <article className="bg-card/80 backdrop-blur-sm rounded-xl border border-primary/20 p-6 sm:p-8 lg:p-12 shadow-lg">
              <div className="prose prose-lg max-w-none">
                {formatContent(post.content)}
              </div>
            </article>

            {/* Footer navigation */}
            <div className="mt-8 sm:mt-12 text-center">
              <Link href="/blog">
                <Button
                  variant="outline"
                  size="lg"
                  className="font-mono bg-gradient-to-r from-primary/20 to-accent/20 hover:from-primary/30 hover:to-accent/30 border-primary/30 text-primary hover:border-primary/50 shadow-lg hover:scale-105 transition-all duration-300 group neo-brutal flex items-center gap-3"
                >
                  <ArrowLeft className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform order-2" />
                  <span className="order-1">مشاهده سایر مقالات</span>
                  <Rocket className="w-5 h-5 group-hover:animate-pulse order-0" />
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default BlogPostPage;
