'use client';

import {
  logout as apiLogout,
  getCurrentUser,
  verifyToken,
} from '@/lib/auth/auth';
import { TokenManager } from '@/lib/auth/token-manager';
import { User } from '@/lib/auth/types';
import { useRouter } from 'next/navigation';
import {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from 'react';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (accessToken: string, refreshToken: string, user: User) => void;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  const isAuthenticated = !!user;

  const login = (accessToken: string, refreshToken: string, userData: User) => {
    TokenManager.setTokens(accessToken, refreshToken);
    TokenManager.setUser(userData);
    setUser(userData);
  };

  const logout = async () => {
    try {
      await apiLogout();
    } catch (error) {
      console.warn('Logout API call failed:', error);
    } finally {
      TokenManager.clearAll();
      setUser(null);
      router.push('/');
    }
  };

  const refreshUser = async () => {
    try {
      setIsLoading(true);

      const hasTokens = TokenManager.checkAuthenticationStatus();

      if (!hasTokens) {
        setUser(null);
        return;
      }

      const isValid = await verifyToken();

      if (isValid) {
        const currentUser = await getCurrentUser();
        console.log(currentUser);
        setUser(currentUser);
        TokenManager.setUser(currentUser);
      } else {
        TokenManager.clearAll();
        setUser(null);
      }
    } catch (error) {
      console.error('Token verification failed:', error);
      TokenManager.clearAll();
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    refreshUser();
  }, []);

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    logout,
    refreshUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
