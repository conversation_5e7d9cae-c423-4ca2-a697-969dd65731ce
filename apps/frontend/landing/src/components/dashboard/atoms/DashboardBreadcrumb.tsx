'use client';

import {
  Bread<PERSON>rumb,
  Bread<PERSON>rumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { DashboardSection } from '@/lib/store/dashboard';
import { ChevronLeft } from 'lucide-react';

interface BreadcrumbItem {
  label: string;
  section?: DashboardSection;
}

interface DashboardBreadcrumbProps {
  items: BreadcrumbItem[];
  onNavigate?: (section: DashboardSection) => void;
}

export function DashboardBreadcrumb({ items, onNavigate }: DashboardBreadcrumbProps) {
  return (
    <Breadcrumb>
      <BreadcrumbList>
        {items.map((item, index) => (
          <div key={index} className="flex items-center">
            <BreadcrumbItem>
              {index === items.length - 1 ? (
                <BreadcrumbPage>{item.label}</BreadcrumbPage>
              ) : (
                <BreadcrumbLink
                  className="cursor-pointer hover:text-primary"
                  onClick={() => item.section && onNavigate?.(item.section)}
                >
                  {item.label}
                </BreadcrumbLink>
              )}
            </BreadcrumbItem>
            {index < items.length - 1 && (
              <BreadcrumbSeparator>
                <ChevronLeft className="h-4 w-4" />
              </BreadcrumbSeparator>
            )}
          </div>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  );
}
