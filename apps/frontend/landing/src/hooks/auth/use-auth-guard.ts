'use client';

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { useAuth } from './use-auth';
import { useAuthModal } from './use-auth-modal';

export function useAuthGuard(redirectTo: string = '/') {
  const { isAuthenticated, isLoading } = useAuth();
  const { openModal } = useAuthModal();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      const currentPath = window.location.pathname + window.location.search;
      localStorage.setItem('redirect_after_login', currentPath);
      router.push(redirectTo);
      openModal();
    }
  }, [isAuthenticated, isLoading, redirectTo, router, openModal]);

  return { isAuthenticated, isLoading };
}

export function useGuestGuard(redirectTo: string = '/') {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      router.push(redirectTo);
    }
  }, [isAuthenticated, isLoading, redirectTo, router]);

  return { isAuthenticated, isLoading };
}
