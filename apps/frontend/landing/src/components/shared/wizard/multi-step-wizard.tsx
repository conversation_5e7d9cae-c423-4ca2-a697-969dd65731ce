'use client';
import { cn } from '@/lib/core/utils';
import { useAtom } from 'jotai';
import React, { Suspense, useEffect, useMemo, useState } from 'react';
import {
  useWizardAutoSave,
  useWizardNavigation,
  useWizardStore,
} from './wizard-hooks';
import { WizardNavigation } from './wizard-navigation';
import { WizardProgress } from './wizard-progress';
import { WizardConfig, WizardStepProps, WizardTheme } from './wizard-types';
import { createStepContext } from './wizard-utils';

interface MultiStepWizardProps extends WizardConfig {
  className?: string;
  theme?: WizardTheme;
  loadingComponent?: React.ComponentType;
  errorComponent?: React.ComponentType<{ error: string; onRetry: () => void }>;
  showStepProgress?: boolean;
  showNavigation?: boolean;
  allowStepClick?: boolean;
}

const WizardLoadingFallback: React.FC = () => (
  <div className="w-full max-w-4xl mx-auto p-6">
    <div className="text-center text-muted-foreground">در حال بارگذاری...</div>
  </div>
);

const WizardErrorFallback: React.FC<{ error: string; onRetry: () => void }> = ({
  error,
  onRetry,
}) => (
  <div className="w-full max-w-4xl mx-auto p-6">
    <div className="text-center">
      <div className="text-destructive mb-4">{error}</div>
      <button
        onClick={onRetry}
        className="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90"
      >
        تلاش مجدد
      </button>
    </div>
  </div>
);

export const MultiStepWizard: React.FC<MultiStepWizardProps> = ({
  className,
  theme = {},
  loadingComponent: LoadingComponent = WizardLoadingFallback,
  errorComponent: ErrorComponent = WizardErrorFallback,
  showStepProgress = true,
  showNavigation = true,
  allowStepClick = false,
  ...config
}) => {
  const [error, setError] = useState<string | null>(null);

  const stableConfig = useMemo(
    () => ({
      ...config,
      showStepProgress,
      showNavigation,
      allowStepClick,
      theme,
    }),
    [config, showStepProgress, showNavigation, allowStepClick, theme],
  );

  // Validate configuration
  React.useEffect(() => {
    if (!stableConfig.steps || stableConfig.steps.length === 0) {
      setError('Wizard must have at least one step');
      return;
    }

    if (!stableConfig.wizardId || stableConfig.wizardId.trim() === '') {
      setError('Wizard must have a valid wizardId');
      return;
    }

    // Check for duplicate step IDs
    const stepIds = new Set();
    for (const step of stableConfig.steps) {
      if (stepIds.has(step.id)) {
        setError(`Duplicate step ID found: ${step.id}`);
        return;
      }
      stepIds.add(step.id);
    }

    setError(null);
  }, [stableConfig.steps, stableConfig.wizardId]);

  const store = useWizardStore(stableConfig.wizardId);
  const navigation = useWizardNavigation(stableConfig, store);
  const autoSave = useWizardAutoSave(stableConfig, store);

  const [wizardState] = useAtom(store.wizardAtom);
  const [, setStepData] = useAtom(store.setStepDataAtom);
  const [, setValidationErrors] = useAtom(store.setValidationErrorsAtom);

  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (stableConfig.confirmOnExit && autoSave.hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = 'تغییرات شما ذخیره نشده است. آیا مطمئن هستید؟';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [stableConfig.confirmOnExit, autoSave.hasUnsavedChanges]);

  useEffect(() => {
    if (!wizardState) return;

    const currentStepIndex = wizardState.currentStep;
    const currentStepConfig = stableConfig.steps[currentStepIndex];
    const currentStepData = wizardState.stepData[currentStepIndex] || {};

    if (currentStepConfig?.validate) {
      const validateAsync = async () => {
        try {
          const context = createStepContext(
            currentStepIndex,
            stableConfig.steps.length,
            stableConfig.wizardId,
            wizardState.stepData || {},
            wizardState.validationErrors || {},
          );

          const validationResult = await currentStepConfig.validate!(
            currentStepData,
            context,
          );

          const errors = Array.isArray(validationResult)
            ? validationResult
            : validationResult === true
              ? []
              : typeof validationResult === 'string'
                ? [validationResult]
                : [];

          setValidationErrors(currentStepIndex, errors);
        } catch (error) {
          console.warn('Validation error:', error);
        }
      };

      validateAsync();
    }
  }, [
    wizardState?.stepData,
    wizardState?.currentStep,
    stableConfig.steps,
    stableConfig.wizardId,
    setValidationErrors,
  ]);

  if (!wizardState && !error) {
    return <LoadingComponent />;
  }

  if (error) {
    return <ErrorComponent error={error} onRetry={() => setError(null)} />;
  }

  if (!wizardState) {
    return <LoadingComponent />;
  }

  const { visibleSteps, currentStep } = navigation;

  if (visibleSteps.length === 0) {
    return (
      <div className={cn('w-full max-w-4xl mx-auto p-6', className)}>
        <div className="text-center text-muted-foreground">
          هیچ مرحله‌ای برای نمایش یافت نشد.
        </div>
      </div>
    );
  }

  if (!currentStep) {
    return (
      <div className={cn('w-full max-w-4xl mx-auto p-6', className)}>
        <div className="text-center text-muted-foreground">
          مرحله فعلی یافت نشد.
        </div>
      </div>
    );
  }

  const currentStepConfig = currentStep.step;
  const currentStepIndex = currentStep.originalIndex;
  const currentStepData = wizardState?.stepData?.[currentStepIndex] || {};

  const context = createStepContext(
    currentStepIndex,
    stableConfig.steps.length,
    stableConfig.wizardId,
    wizardState?.stepData || {},
    wizardState?.validationErrors || {},
  );

  const handleDataChange = (data: any) => {
    if (typeof data === 'function') {
      const updatedData = data(currentStepData);
      setStepData(currentStepIndex, updatedData);
    } else {
      setStepData(currentStepIndex, { ...currentStepData, ...data });
    }
  };

  const stepProps: WizardStepProps = {
    data: currentStepData,
    onDataChange: handleDataChange,
    onNext: navigation.next,
    onPrevious: navigation.previous,
    context,
    canGoNext: navigation.canGoNext,
    canGoPrevious: navigation.canGoPrevious,
  };

  const StepComponent = currentStepConfig.component;

  const containerClasses = cn(
    'w-full mx-auto p-4 transition-all duration-300 border rounded-lg shadow-md bg-background',
    theme.animationType === 'fade' && 'animate-in fade-in-50',
    theme.animationType === 'slide' && 'animate-in slide-in-from-right-5',
    className,
  );

  const renderStepWithErrorBoundary = () => {
    try {
      return (
        <Suspense fallback={<LoadingComponent />}>
          <StepComponent {...stepProps} />
        </Suspense>
      );
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'خطای نامشخص';
      return (
        <ErrorComponent error={errorMessage} onRetry={() => setError(null)} />
      );
    }
  };

  return (
    <div className={containerClasses} data-wizard-id={stableConfig.wizardId}>
      {showStepProgress && (
        <WizardProgress
          currentStep={navigation.currentVisibleStepIndex}
          totalSteps={visibleSteps.length}
          steps={visibleSteps.map(({ step }) => ({
            id: step.id,
            title: step.title,
            isOptional: step.isOptional,
            icon: step.icon,
            estimatedTime: step.estimatedTime,
          }))}
          completedSteps={wizardState?.completedSteps || new Set()}
          hasErrors={Object.fromEntries(
            Object.entries(wizardState?.validationErrors || {}).map(
              ([key, errors]) => [
                key,
                Array.isArray(errors) ? errors.length > 0 : false,
              ],
            ),
          )}
          variant={theme.progressVariant}
          clickable={allowStepClick}
          onStepClick={allowStepClick ? navigation.goToStep : undefined}
        />
      )}

      <div className="mb-4" data-step-id={currentStepConfig.id}>
        {renderStepWithErrorBoundary()}
      </div>

      {showNavigation && (
        <WizardNavigation
          canGoNext={stepProps.canGoNext}
          canGoPrevious={stepProps.canGoPrevious}
          isFirstStep={context.isFirstStep}
          isLastStep={context.isLastStep}
          isValidating={wizardState?.isLoading || false}
          onNext={navigation.next}
          onPrevious={navigation.previous}
          onSave={stableConfig.autoSave ? autoSave.manualSave : undefined}
          onReset={navigation.reset}
          showSave={stableConfig.autoSave}
          showReset={true}
          hasUnsavedChanges={autoSave.hasUnsavedChanges}
          position={theme.navigationPosition}
          onSkip={currentStepConfig.allowSkip ? navigation.next : undefined}
          showSkip={currentStepConfig.allowSkip}
        />
      )}

      {stableConfig.autoSave && autoSave.hasUnsavedChanges && (
        <div className="fixed bottom-4 right-4 text-xs text-muted-foreground bg-background/90 backdrop-blur-sm px-3 py-2 rounded-md border">
          آخرین ذخیره:{' '}
          {autoSave.lastSaveTime
            ? new Date(autoSave.lastSaveTime).toLocaleTimeString('fa-IR')
            : 'هرگز'}
        </div>
      )}
    </div>
  );
};
