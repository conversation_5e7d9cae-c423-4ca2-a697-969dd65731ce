'use client';

import { TokenManager } from '@/lib/auth/token-manager';
import { User } from '@/lib/auth/types';

export interface DashboardUser extends User {
  role: 'buyer' | 'coder';
}

export const getCurrentUserRole = (): 'buyer' | 'coder' | null => {
  const user = TokenManager.getUser();
  if (!user?.role) return null;
  return user.role === 'buyer' || user.role === 'coder' ? user.role : null;
};

export const isAuthorizedForRole = (
  requiredRole: 'buyer' | 'coder',
): boolean => {
  const userRole = getCurrentUserRole();
  return userRole === requiredRole;
};

export const getCurrentUser = (): DashboardUser | null => {
  const user = TokenManager.getUser();
  if (!user) return null;

  return {
    ...user,
    role: user.role === 'buyer' || user.role === 'coder' ? user.role : 'buyer',
  };
};
