'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { getGoogleAuthUrl } from '@/lib/auth/auth';
import { useState } from 'react';
import { RoleSelector } from '../atoms/role-selector';

interface GoogleOAuthWithRoleProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export function GoogleOAuthWithRole({
  onSuccess,
  onError,
}: GoogleOAuthWithRoleProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [selectedRole, setSelectedRole] = useState<string>('buyer');

  const handleGoogleAuth = async () => {
    try {
      setIsLoading(true);

      // Store the selected role in localStorage to use after OAuth callback
      localStorage.setItem('google_oauth_role', selectedRole);

      const urlParams = new URLSearchParams(window.location.search);
      const redirectPath = urlParams.get('redirect');
      if (redirectPath) {
        localStorage.setItem('redirect_after_login', redirectPath);
      }

      const response = await getGoogleAuthUrl();

      // Pass role via state parameter instead of modifying redirect URI
      const authUrl = new URL(response.auth_url);
      const currentState = authUrl.searchParams.get('state') || '';
      const stateWithRole = `${currentState}&role=${selectedRole}`;
      authUrl.searchParams.set('state', stateWithRole);

      window.location.href = authUrl.toString();
    } catch (error) {
      console.error('Google OAuth error:', error);
      const errorMessage =
        error instanceof Error ? error.message : 'احراز هویت گوگل ناموفق بود';
      onError?.(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto py-2 gap-0">
      <CardHeader className="text-center">
        <CardTitle className="text-md font-bold">ورود با گوگل</CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        <RoleSelector value={selectedRole} onValueChange={setSelectedRole} />

        <Button
          onClick={handleGoogleAuth}
          disabled={isLoading}
          className="w-full"
        >
          <svg className="w-6! h-6! mr-2" viewBox="0 0 24 24">
            <path
              fill="currentColor"
              d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
            />
            <path
              fill="currentColor"
              d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
            />
            <path
              fill="currentColor"
              d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
            />
            <path
              fill="currentColor"
              d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
            />
          </svg>
          {isLoading ? 'در حال احراز هویت...' : 'ورود با گوگل'}
        </Button>
      </CardContent>
    </Card>
  );
}
