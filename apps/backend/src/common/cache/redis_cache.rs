use redis::{Client, Commands};
use anyhow::Result;

#[derive(Clone)]
#[allow(dead_code)]
pub struct RedisCache {
    client: Client,
}

impl RedisCache {
    pub fn new(client: Client) -> Self {
        Self { client }
    }

    #[allow(dead_code)]
    pub async fn get(&self, key: &str) -> Result<Option<String>> {
        let mut conn = self.client.get_connection()?;
        let result: Option<String> = conn.get(key)?;
        Ok(result)
    }

    #[allow(dead_code)]
    pub async fn set(&self, key: &str, value: &str, expiry: Option<u64>) -> Result<()> {
        let mut conn = self.client.get_connection()?;

        if let Some(exp) = expiry {
            conn.set_ex::<_, _, ()>(key, value, exp)?;
        } else {
            conn.set::<_, _, ()>(key, value)?;
        }

        Ok(())
    }

    #[allow(dead_code)]
    pub async fn delete(&self, key: &str) -> Result<()> {
        let mut conn = self.client.get_connection()?;
        conn.del::<_, ()>(key)?;
        Ok(())
    }

    #[allow(dead_code)]
    pub async fn exists(&self, key: &str) -> Result<bool> {
        let mut conn = self.client.get_connection()?;
        let result: bool = conn.exists(key)?;
        Ok(result)
    }
}
