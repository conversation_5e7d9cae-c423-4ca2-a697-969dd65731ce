-- Add phone_otp table for storing OTP verification codes
CREATE TABLE phone_otp (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    phone VARCHAR NOT NULL,
    otp_code VARCHAR(6) NOT NULL,
    verified BOOLEAN DEFAULT FALSE,
    expires_at TIMESTAMPTZ NOT NULL,
    attempts INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index for faster phone lookups
CREATE INDEX idx_phone_otp_phone ON phone_otp(phone);
CREATE INDEX idx_phone_otp_expires_at ON phone_otp(expires_at);

-- Add phone field to users table
ALTER TABLE users ADD COLUMN phone VARCHAR;
ALTER TABLE users ADD COLUMN phone_verified BOOLEAN DEFAULT FALSE;
