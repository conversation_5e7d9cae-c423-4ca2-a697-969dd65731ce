'use client';

import { Components } from '@/components/registry';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from '@/components/ui/hover-card';
import { Separator } from '@/components/ui/separator';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { useAuth } from '@/hooks/auth/use-auth';
import { useAuthModal } from '@/hooks/auth/use-auth-modal';
import { ArrowRight, LogOut, User } from 'lucide-react';
import Link from 'next/link';

const HeaderNavigation = () => {
  const { user, isAuthenticated, isLoading, logout } = useAuth();
  const { openModal, resetModal } = useAuthModal();

  const handleLogout = async () => {
    await logout();
    resetModal(); // Reset modal state on logout
  };
  return (
    <>
      <div className="fixed inset-0 bg-background/80 backdrop-blur-md z-40 md:hidden transition-opacity duration-200 ease-[cubic-bezier(0.33,1,0.68,1)] opacity-0 pointer-events-none peer-has-[:checked]:opacity-100" />

      <nav className="flex items-center justify-between relative z-50 h-full px-4">
        <div className="flex-1 flex justify-start items-center gap-4">
          <Components.Logo
            asLink={true}
            className="hidden md:block sm:cursor-pointer flex-shrink-0"
          />
          <div className="hidden md:block! md:min-w-0">
            <Components.Navigation className="hidden md:block" />
          </div>
        </div>

        <div className="md:hidden">
          <Components.Logo
            asLink={true}
            className="scale-[clamp(0.85,3vw,1)] sm:cursor-pointer"
          />
        </div>

        <div className="flex-1 flex justify-end items-center">
          <label className="flex items-center md:hidden peer">
            <input type="checkbox" className="absolute opacity-0" />
            <Components.MobileMenuToggle />
          </label>

          <div className="fixed top-0 right-0 w-[clamp(280px,85vw,380px)] bg-card border-l border-primary/20 shadow-lg z-50 transform transition-all duration-300 ease-[cubic-bezier(0.33,1,0.68,1)] md:hidden translate-x-full peer-has-[:checked]:translate-x-0">
            <div className="p-2 h-full flex flex-col bg-gradient-to-br from-card to-secondary">
              <div className="flex justify-between items-center mb-1">
                <Components.Logo
                  asLink={true}
                  className="scale-75 sm:cursor-pointer"
                />
                <button
                  onClick={() =>
                    (
                      document.querySelector(
                        'input[type="checkbox"]',
                      ) as HTMLInputElement
                    )?.click()
                  }
                  className="w-8 h-8 flex items-center justify-center rounded-lg bg-primary/10 hover:bg-primary/20 transition-colors text-primary border border-primary/30 font-mono"
                  aria-label="Close menu"
                >
                  <span className="text-sm">×</span>
                </button>
              </div>
              <Separator className="my-1 border-primary/20" />
              <Components.Navigation className="mx-auto text-center pb-1" />
              <Separator className="mt-1 border-primary/20" />
              <div className="mt-auto">
                <div className="flex justify-center mb-1">
                  <ThemeToggle />
                </div>
                {!isLoading && (
                  <>
                    {isAuthenticated ? (
                      <div className="space-y-2">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <button className="w-full flex items-center justify-center gap-2 p-2 bg-primary/10 rounded-lg border border-primary/20 hover:bg-primary/20 transition-colors cursor-pointer">
                              <User className="w-3 h-3 text-primary" />
                              <span className="text-xs font-mono text-primary">
                                {user?.username || user?.email || 'کاربر'}
                              </span>
                            </button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent
                            align="center"
                            className="w-48 bg-card/95 backdrop-blur-sm border-primary/20"
                          >
                            <DropdownMenuItem
                              onClick={handleLogout}
                              className="text-green-800 hover:bg-green-950/50 hover:text-green-900 cursor-pointer font-mono focus:bg-green-950/50 focus:text-green-300 transition-colors"
                            >
                              <LogOut className="w-3 h-3 ml-2" />
                              خروج
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    ) : (
                      <Button
                        className="bg-gradient-to-r from-primary to-accent hover:from-primary/80 hover:to-accent/80 shadow-lg hover:scale-105 font-mono text-[clamp(0.4rem,3vw,0.8rem)] transition-all duration-300 group neo-brutal flex items-center gap-2"
                        onClick={openModal}
                      >
                        <span>ورود / ثبت‌نام</span>
                        <ArrowRight className="w-3 h-3 group-hover:-translate-x-1 transition-transform" />
                      </Button>
                    )}
                  </>
                )}
                <div className="mt-1 text-center text-xs text-muted-foreground font-mono">
                  <Link
                    href="/contact"
                    className="hover:text-primary transition-colors"
                    onClick={() =>
                      (
                        document.querySelector(
                          'input[type="checkbox"]',
                        ) as HTMLInputElement
                      )?.click()
                    }
                  >
                    پشتیبانی و تماس
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="hidden md:flex items-center gap-3">
          <ThemeToggle />
          <div className="flex gap-2 text-xs text-muted-foreground font-mono">
            <Link
              target="_blank"
              href="tel:09112191746"
              className="text-black! dark:text-white! hover:text-primary transition-colors flex items-center gap-1 border border-primary/20 px-2 py-1 rounded bg-card/50"
            >
              <span dir="ltr">۰۹۱۱۲۱۹۱۷۴۶</span>
            </Link>
            <span className="text-primary/50">|</span>
            <Link
              target="_blank"
              href="mailto:<EMAIL>"
              className="text-black! dark:text-white! hover:text-primary transition-colors flex items-center gap-1 border border-primary/20 px-2 py-1 rounded bg-card/50"
            >
              <span dir="ltr"><EMAIL></span>
            </Link>
          </div>

          {!isLoading && (
            <>
              {isAuthenticated ? (
                <div className="flex items-center gap-2 ">
                  <HoverCard openDelay={300} closeDelay={100}>
                    <HoverCardTrigger asChild>
                      <button className="flex items-center gap-2 px-2 py-1 bg-primary/10 rounded-lg border border-primary/20 hover:bg-primary/20 transition-colors cursor-pointer">
                        <User className="w-3 h-3 text-primary" />
                        <span className="text-xs font-mono text-primary">
                          {user?.username || user?.email || 'کاربر'}
                        </span>
                      </button>
                    </HoverCardTrigger>
                    <HoverCardContent
                      align="end"
                      className="w-32 py-0! bg-card/95 backdrop-blur-sm dark:border-green-100/30 mx-auto"
                    >
                      <button
                        onClick={handleLogout}
                        className="w-full text-sm text-center text-green-800 hover:bg-green-100/50 hover:text-green-900 cursor-pointer font-mono focus:bg-green-600/50 focus:text-800-300 transition-colors rounded-sm flex items-center gap-2"
                      >
                        <LogOut className="w-3 h-3 ml-2" />
                        خروج
                      </button>
                    </HoverCardContent>
                  </HoverCard>
                </div>
              ) : (
                <Button
                  size="xs"
                  className="transition-all duration-300 hover:scale-105 group neo-brutal text-green-200! bg-gradient-to-r from-primary to-accent hover:from-primary/80 hover:to-accent/80 shadow-sm font-mono text-[clamp(0.4rem,3vw,0.8rem)]!"
                  onClick={openModal}
                >
                  ورود / ثبت‌نام
                </Button>
              )}
            </>
          )}
        </div>
      </nav>
    </>
  );
};

export default HeaderNavigation;
