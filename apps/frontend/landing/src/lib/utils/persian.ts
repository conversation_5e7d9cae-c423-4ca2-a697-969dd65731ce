/**
 * Persian/Farsi utility functions for better localization support
 */

// Persian number conversion
const persianNumbers = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

export function toPersianNumbers(str: string): string {
  return str.replace(/[0-9]/g, (match) => {
    const index = englishNumbers.indexOf(match);
    return index !== -1 ? persianNumbers[index] : match;
  });
}

export function toEnglishNumbers(str: string): string {
  return str.replace(/[۰-۹]/g, (match) => {
    const index = persianNumbers.indexOf(match);
    return index !== -1 ? englishNumbers[index] : match;
  });
}

// Persian date formatting
export function formatPersianDate(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  try {
    return new Intl.DateTimeFormat('fa-IR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      calendar: 'persian'
    }).format(dateObj);
  } catch (error) {
    // Fallback to Gregorian if Persian calendar is not supported
    return new Intl.DateTimeFormat('fa-IR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(dateObj);
  }
}

export function formatPersianDateTime(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  try {
    return new Intl.DateTimeFormat('fa-IR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      calendar: 'persian'
    }).format(dateObj);
  } catch (error) {
    // Fallback to Gregorian if Persian calendar is not supported
    return new Intl.DateTimeFormat('fa-IR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(dateObj);
  }
}

export function formatPersianRelativeTime(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();
  const diffInMs = now.getTime() - dateObj.getTime();
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
  const diffInWeeks = Math.floor(diffInDays / 7);
  const diffInMonths = Math.floor(diffInDays / 30);
  const diffInYears = Math.floor(diffInDays / 365);

  if (diffInMinutes < 1) {
    return 'همین الان';
  } else if (diffInMinutes < 60) {
    return `${toPersianNumbers(diffInMinutes.toString())} دقیقه پیش`;
  } else if (diffInHours < 24) {
    return `${toPersianNumbers(diffInHours.toString())} ساعت پیش`;
  } else if (diffInDays < 7) {
    return `${toPersianNumbers(diffInDays.toString())} روز پیش`;
  } else if (diffInWeeks < 4) {
    return `${toPersianNumbers(diffInWeeks.toString())} هفته پیش`;
  } else if (diffInMonths < 12) {
    return `${toPersianNumbers(diffInMonths.toString())} ماه پیش`;
  } else {
    return `${toPersianNumbers(diffInYears.toString())} سال پیش`;
  }
}

// Persian currency formatting
export function formatPersianCurrency(amount: number, currency: 'IRR' | 'USD' = 'IRR'): string {
  if (currency === 'IRR') {
    // Convert to Toman (divide by 10) and format
    const tomanAmount = amount / 10;
    const formatted = new Intl.NumberFormat('fa-IR').format(tomanAmount);
    return `${toPersianNumbers(formatted)} تومان`;
  } else {
    const formatted = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
    return formatted;
  }
}

// Persian text utilities
export function isPersianText(text: string): boolean {
  const persianRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
  return persianRegex.test(text);
}

export function getTextDirection(text: string): 'rtl' | 'ltr' {
  return isPersianText(text) ? 'rtl' : 'ltr';
}

// Persian pluralization (simplified)
export function pluralizePersian(count: number, singular: string, plural?: string): string {
  const persianCount = toPersianNumbers(count.toString());
  
  if (count === 1) {
    return `${persianCount} ${singular}`;
  }
  
  // If no plural form provided, use singular with count
  const pluralForm = plural || singular;
  return `${persianCount} ${pluralForm}`;
}

// Common Persian plurals
export const persianPlurals = {
  project: { singular: 'پروژه', plural: 'پروژه' },
  developer: { singular: 'توسعه‌دهنده', plural: 'توسعه‌دهنده' },
  day: { singular: 'روز', plural: 'روز' },
  hour: { singular: 'ساعت', plural: 'ساعت' },
  minute: { singular: 'دقیقه', plural: 'دقیقه' },
  review: { singular: 'نظر', plural: 'نظر' },
  skill: { singular: 'مهارت', plural: 'مهارت' },
  proposal: { singular: 'پیشنهاد', plural: 'پیشنهاد' },
  message: { singular: 'پیام', plural: 'پیام' },
  notification: { singular: 'اعلان', plural: 'اعلان' }
};

// Persian ordinal numbers (simplified)
export function toPersianOrdinal(num: number): string {
  const persianNum = toPersianNumbers(num.toString());
  
  if (num === 1) return `${persianNum}م`;
  if (num === 2) return `${persianNum}م`;
  if (num === 3) return `${persianNum}م`;
  
  return `${persianNum}م`;
}

// Persian text truncation with proper handling of RTL
export function truncatePersianText(text: string, maxLength: number, suffix: string = '...'): string {
  if (text.length <= maxLength) {
    return text;
  }
  
  // For Persian text, we want to truncate from the end (left side in RTL)
  const truncated = text.substring(0, maxLength - suffix.length);
  return truncated + suffix;
}

// Persian search normalization
export function normalizePersianSearch(text: string): string {
  return text
    .toLowerCase()
    // Normalize Persian characters
    .replace(/ي/g, 'ی')
    .replace(/ك/g, 'ک')
    .replace(/ء/g, '')
    // Remove diacritics
    .replace(/[\u064B-\u0652]/g, '')
    // Normalize whitespace
    .replace(/\s+/g, ' ')
    .trim();
}

// Persian keyboard layout helpers
export function isPersianKeyboard(text: string): boolean {
  // Check if text contains Persian characters
  return isPersianText(text);
}

// Persian validation helpers
export function isValidPersianName(name: string): boolean {
  const persianNameRegex = /^[\u0600-\u06FF\u0750-\u077F\s]+$/;
  return persianNameRegex.test(name.trim());
}

export function isValidPersianPhone(phone: string): boolean {
  // Iranian mobile phone number pattern
  const iranianPhoneRegex = /^(\+98|0)?9\d{9}$/;
  return iranianPhoneRegex.test(phone.replace(/\s/g, ''));
}

// Persian sorting
export function sortPersianText(texts: string[]): string[] {
  return texts.sort((a, b) => {
    return a.localeCompare(b, 'fa-IR');
  });
}
