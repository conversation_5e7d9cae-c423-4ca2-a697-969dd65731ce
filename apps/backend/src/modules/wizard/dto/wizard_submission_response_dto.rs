use serde::{Deserialize, Serialize};
use utoipa::ToSchema;
use chrono::{DateTime, Utc};
use uuid::Uuid;

#[derive(Debug, Serialize, Deserialize, ToSchema)]
pub struct WizardSubmissionResponseDto {
    #[schema(example = "550e8400-e29b-41d4-a716-************")]
    pub id: Uuid,

    #[schema(example = "محمد احمدی")]
    pub full_name: String,

    #[schema(example = "<EMAIL>")]
    pub email: String,

    #[schema(example = "09123456789")]
    pub phone: String,

    #[schema(example = "webapp")]
    pub project_type: String,

    #[schema(example = "توضیحات پروژه")]
    pub description: String,

    #[schema(example = "pending")]
    pub status: String,

    #[schema(example = "2024-01-15T10:30:00Z")]
    pub created_at: DateTime<Utc>,

    #[schema(example = "2024-01-15T10:30:00Z")]
    pub updated_at: DateTime<Utc>,
}
