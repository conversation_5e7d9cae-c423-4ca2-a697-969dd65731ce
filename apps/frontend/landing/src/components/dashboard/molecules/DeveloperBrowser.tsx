'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Slider } from '@/components/ui/slider';
import { searchCoders } from '@/lib/data/mock-users';
import { PROGRAMMING_SKILLS, EXPERIENCE_LEVELS, AVAILABILITY_STATUS, CoderProfile } from '@/lib/types/user';
import { Filter, Search, X } from 'lucide-react';
import { useState } from 'react';
import { UserProfileCard } from '../atoms/UserProfileCard';

interface DeveloperBrowserProps {
  onViewProfile?: (userId: string) => void;
  onSendMessage?: (userId: string) => void;
  onHire?: (userId: string) => void;
}

export function DeveloperBrowser({
  onViewProfile,
  onSendMessage,
  onHire
}: DeveloperBrowserProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  
  // Filter states
  const [selectedSkills, setSelectedSkills] = useState<string[]>([]);
  const [selectedExperience, setSelectedExperience] = useState<string>('');
  const [selectedAvailability, setSelectedAvailability] = useState<string>('');
  const [locationFilter, setLocationFilter] = useState('');
  const [minRating, setMinRating] = useState([0]);

  const getFilteredDevelopers = (): CoderProfile[] => {
    const filters: any = {};
    
    if (selectedSkills.length > 0) filters.skills = selectedSkills;
    if (selectedExperience) filters.experience = selectedExperience;
    if (selectedAvailability) filters.availability = selectedAvailability;
    if (locationFilter) filters.location = locationFilter;
    if (minRating[0] > 0) filters.minRating = minRating[0];

    let developers = searchCoders(filters);

    // Apply search term
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      developers = developers.filter(dev => 
        dev.username.toLowerCase().includes(searchLower) ||
        dev.bio?.toLowerCase().includes(searchLower) ||
        dev.skills.some(skill => skill.toLowerCase().includes(searchLower))
      );
    }

    return developers;
  };

  const clearFilters = () => {
    setSelectedSkills([]);
    setSelectedExperience('');
    setSelectedAvailability('');
    setLocationFilter('');
    setMinRating([0]);
    setSearchTerm('');
  };

  const hasActiveFilters = selectedSkills.length > 0 || selectedExperience || selectedAvailability || locationFilter || minRating[0] > 0 || searchTerm;

  const filteredDevelopers = getFilteredDevelopers();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">جستجوی توسعه‌دهندگان</h2>
        <Button
          variant="outline"
          onClick={() => setShowFilters(!showFilters)}
        >
          <Filter className="h-4 w-4 ml-2" />
          فیلترها
        </Button>
      </div>

      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="جستجو در توسعه‌دهندگان..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pr-10"
          />
        </div>
      </div>

      {showFilters && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">فیلترها</CardTitle>
              {hasActiveFilters && (
                <Button variant="ghost" size="sm" onClick={clearFilters}>
                  <X className="h-4 w-4 ml-1" />
                  پاک کردن همه
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              {/* Experience Filter */}
              <div className="space-y-2">
                <Label>سطح تجربه</Label>
                <Select value={selectedExperience} onValueChange={setSelectedExperience}>
                  <SelectTrigger>
                    <SelectValue placeholder="انتخاب سطح تجربه" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">همه سطوح</SelectItem>
                    {EXPERIENCE_LEVELS.map((level) => (
                      <SelectItem key={level.value} value={level.value}>
                        {level.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Availability Filter */}
              <div className="space-y-2">
                <Label>وضعیت دسترسی</Label>
                <Select value={selectedAvailability} onValueChange={setSelectedAvailability}>
                  <SelectTrigger>
                    <SelectValue placeholder="انتخاب وضعیت" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">همه وضعیت‌ها</SelectItem>
                    {AVAILABILITY_STATUS.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        {status.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Location Filter */}
              <div className="space-y-2">
                <Label>موقعیت مکانی</Label>
                <Input
                  placeholder="شهر یا استان"
                  value={locationFilter}
                  onChange={(e) => setLocationFilter(e.target.value)}
                />
              </div>

              {/* Rating Filter */}
              <div className="space-y-2">
                <Label>حداقل امتیاز: {minRating[0]}</Label>
                <Slider
                  value={minRating}
                  onValueChange={setMinRating}
                  max={5}
                  min={0}
                  step={0.5}
                  className="w-full"
                />
              </div>
            </div>

            <Separator />

            {/* Skills Filter */}
            <div className="space-y-3">
              <Label>مهارت‌های مورد نیاز</Label>
              <div className="grid gap-2 grid-cols-2 md:grid-cols-4 lg:grid-cols-6">
                {PROGRAMMING_SKILLS.slice(0, 24).map((skill) => (
                  <div key={skill} className="flex items-center space-x-2">
                    <Checkbox
                      id={skill}
                      checked={selectedSkills.includes(skill)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedSkills(prev => [...prev, skill]);
                        } else {
                          setSelectedSkills(prev => prev.filter(s => s !== skill));
                        }
                      }}
                    />
                    <Label htmlFor={skill} className="text-sm">
                      {skill}
                    </Label>
                  </div>
                ))}
              </div>
              {selectedSkills.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {selectedSkills.map((skill) => (
                    <Badge key={skill} variant="secondary" className="text-xs">
                      {skill}
                      <button
                        onClick={() => setSelectedSkills(prev => prev.filter(s => s !== skill))}
                        className="ml-1 hover:text-destructive"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      <div className="flex items-center justify-between">
        <p className="text-muted-foreground">
          {filteredDevelopers.length} توسعه‌دهنده یافت شد
        </p>
        {hasActiveFilters && (
          <Badge variant="secondary">
            فیلتر فعال
          </Badge>
        )}
      </div>

      {filteredDevelopers.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-muted-foreground mb-4">
            هیچ توسعه‌دهنده‌ای با این فیلترها یافت نشد
          </div>
          <Button variant="outline" onClick={clearFilters}>
            پاک کردن فیلترها
          </Button>
        </div>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredDevelopers.map((developer) => (
            <UserProfileCard
              key={developer.id}
              user={developer}
              variant="detailed"
              onViewProfile={onViewProfile}
              onSendMessage={onSendMessage}
              onHire={onHire}
            />
          ))}
        </div>
      )}
    </div>
  );
}
