import { cn } from '@/lib/core/utils';
import React from 'react';

interface ContactHeroProps {
  className?: string;
}

const ContactHero: React.FC<ContactHeroProps> = ({ className }) => {
  return (
    <section
      className={cn(
        'relative py-10 bg-gradient-to-br from-background via-card to-secondary text-foreground overflow-hidden',
        className,
      )}
    >
      <div className="absolute inset-0 overflow-hidden">
        {Array.from({ length: 8 }).map((_, i) => (
          <div
            key={i}
            className="absolute text-primary/5 font-mono text-xs"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 8}s`,
              animationDuration: `${8 + Math.random() * 4}s`,
            }}
          >
            {Math.random().toString(36).substring(2, 8)}
          </div>
        ))}
      </div>

      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary via-accent to-primary"></div>
      <div className="absolute -top-20 -right-20 w-40 h-40 rounded-full bg-primary/10 mix-blend-screen filter blur-3xl opacity-20"></div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-primary via-accent to-primary bg-clip-text text-transparent">
            تماس با ما
          </h1>
          <p className="text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto mb-8">
            ما آماده پاسخگویی به سوالات و درخواست‌های شما هستیم. با ما در ارتباط
            باشید.
          </p>
          <div className="flex justify-center">
            <div className="w-24 h-1 bg-gradient-to-r from-primary via-accent to-primary rounded-full"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactHero;
