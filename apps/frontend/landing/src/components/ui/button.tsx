import { cn } from '@/lib/core/utils';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import * as React from 'react';

const buttonVariants = cva(
  "cursor-pointer disabled:cursor-not-allowed inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-2 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive ",
  {
    variants: {
      variant: {
        default:
          'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',
        destructive:
          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40',
        outline:
          'border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground',
        secondary:
          'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline',
      },
      size: {
        default: 'px-[clamp(0.75rem,2vw,1rem)] has-[>svg]:px-3 py-2',
        xxs: 'rounded-md gap-0.5 px-[clamp(0.125rem,0.5vw,0.25rem)] has-[>svg]:px-1 py-1',
        xs: 'rounded-md gap-1 px-[clamp(0.25rem,1vw,0.5rem)] has-[>svg]:px-2 py-1.25',
        sm: 'rounded-md gap-1.5 px-[clamp(0.5rem,1.5vw,0.75rem)] has-[>svg]:px-2.5 py-2',
        md: 'rounded-md px-[clamp(0.75rem,2vw,1rem)] has-[>svg]:px-3 py-2',
        lg: 'rounded-md px-[clamp(1rem,3vw,1.5rem)] has-[>svg]:px-4 py-2.5',
        icon: 'size-[clamp(2rem,5vw,2.25rem)]',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'sm',
    },
  },
);

function Button({
  className,
  variant,
  size,
  asChild = false,
  ...props
}: React.ComponentProps<'button'> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean;
  }) {
  const Comp = asChild ? Slot : 'button';

  return (
    <Comp
      data-slot="button"
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    />
  );
}

export { Button, buttonVariants };
