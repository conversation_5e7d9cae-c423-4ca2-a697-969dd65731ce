import { Components } from '@/components/registry';
import { notFound } from 'next/navigation';
import blogData from '@/data/blog.json';

const getBlogPost = (slug: string) => {
  return blogData.find(post => post.slug === slug) || null;
};

export async function generateStaticParams() {
  return blogData.map(post => ({ slug: post.slug }));
}

export default function BlogPostPage({ params }: { params: { slug: string } }) {
  const post = getBlogPost(params.slug);

  if (!post) {
    notFound();
  }

  return <Components.BlogPostPage post={post} />;
}

export function generateMetadata({ params }: { params: { slug: string } }) {
  const post = getBlogPost(params.slug);

  if (!post) {
    return {
      title: 'مقاله یافت نشد | بلاگ دلفک',
    };
  }

  return {
    title: `${post.title} | بلاگ دلفک`,
    description: post.content.slice(0, 160) + '...',
  };
}
