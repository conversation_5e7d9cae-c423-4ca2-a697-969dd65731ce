import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import React from 'react';
import ContactCard from '../atoms/ContactCard';

interface ContactFormProps {
  className?: string;
}

const ContactForm: React.FC<ContactFormProps> = ({ className }) => {
  return (
    <ContactCard title="پیام شما" className={className}>
      <form className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="name">نام</Label>
            <Input id="name" name="name" placeholder="نام شما" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">ایمیل</Label>
            <Input
              id="email"
              name="email"
              type="email"
              placeholder="<EMAIL>"
            />
          </div>
        </div>
        <div className="space-y-2">
          <Label htmlFor="subject">موضوع</Label>
          <Input id="subject" name="subject" placeholder="موضوع پیام" />
        </div>
        <div className="space-y-2">
          <Label htmlFor="message">پیام</Label>
          <textarea
            id="message"
            name="message"
            rows={5}
            className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            placeholder="متن پیام شما..."
          />
        </div>
        <Button type="submit" className="w-full" size="lg">
          ارسال پیام
        </Button>
      </form>
    </ContactCard>
  );
};

export default ContactForm;
