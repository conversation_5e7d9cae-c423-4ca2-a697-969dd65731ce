use std::env;

#[derive(Clone)]
pub struct JwtConfig {
    pub secret: String,
    pub expires_in: u64,
}

impl Default for JwtConfig {
    fn default() -> Self {
        Self::new()
    }
}

impl JwtConfig {
    pub fn new() -> Self {
        Self {
            secret: env::var("JWT_SECRET")
                .expect("JWT_SECRET must be set"),
            expires_in: env::var("JWT_EXPIRES_IN")
                .unwrap_or_else(|_| "3600".to_string())
                .parse()
                .expect("Invalid JWT expires in value"),
        }
    }
}
